{"cells": [{"cell_type": "code", "id": "initial_id", "metadata": {"collapsed": true, "ExecuteTime": {"end_time": "2025-02-11T02:09:39.460913Z", "start_time": "2025-02-11T02:09:38.693023Z"}}, "source": ["import os\n", "from pathlib import Path\n", "from openai import OpenAI\n", "\n", "client = OpenAI(\n", "    api_key=os.getenv(\"DASHSCOPE_API_KEY\"),  # 如果您没有配置环境变量，请在此处替换您的API-KEY\n", "    base_url=\"https://dashscope.aliyuncs.com/compatible-mode/v1\",  # 填写DashScope服务base_url\n", ")\n", "\n", "file_object = client.files.create(file=Path(\"Text.txt\"), purpose=\"file-extract\")\n", "print(file_object.id)"], "outputs": [{"name": "stdout", "output_type": "stream", "text": ["file-fe-WijJwUgDsfAuOYhq2A85rbuu\n"]}], "execution_count": 6}, {"metadata": {"ExecuteTime": {"end_time": "2025-02-11T02:09:49.568845Z", "start_time": "2025-02-11T02:09:49.325792Z"}}, "cell_type": "code", "source": ["file_id = \"file-fe-WijJwUgDsfAuOYhq2A85rbuu\"\n", "file_info = client.files.retrieve(file_id)\n", "print(file_info)"], "id": "caa16a6b1ef92c83", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["FileObject(id='file-fe-WijJwUgDsfAuOYhq2A85rbuu', bytes=17651, created_at=1739239779, filename='Text.txt', object='file', purpose='file-extract', status='processed', status_details=None, meta={})\n"]}], "execution_count": 7}, {"metadata": {"ExecuteTime": {"end_time": "2025-02-11T02:10:49.747257Z", "start_time": "2025-02-11T02:10:21.119033Z"}}, "cell_type": "code", "source": ["import os\n", "from openai import OpenAI\n", "\n", "client = OpenAI(\n", "    api_key=os.getenv(\"DASHSCOPE_API_KEY\"),  # 如果您没有配置环境变量，请在此处替换您的API-KEY\n", "    base_url=\"https://dashscope.aliyuncs.com/compatible-mode/v1\",  # 填写DashScope服务base_url\n", ")\n", "# 初始化messages列表\n", "completion = client.chat.completions.create(\n", "    model=\"qwen-long\",\n", "    messages=[\n", "        {'role': 'system', 'content': 'You are a helpful assistant.'},\n", "        # 请将 'file-fe-xxx'替换为您实际对话场景所使用的 file-id。\n", "        {'role': 'system', 'content': 'fileid://file-fe-WijJwUgDsfAuOYhq2A85rbuu'},\n", "        {'role': 'user', 'content': '这个文件的主要内容是什么，帮我总结一下'}\n", "    ],\n", "    stream=True,\n", "    stream_options={\"include_usage\": True}\n", ")\n", "\n", "full_content = \"\"\n", "for chunk in completion:\n", "    if chunk.choices and chunk.choices[0].delta.content:\n", "        # 拼接输出内容\n", "        full_content += chunk.choices[0].delta.content\n", "        print(chunk.model_dump())\n", "\n", "print({full_content})"], "id": "b7c0f387645301b0", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '文件', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '《', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': 'Text', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '.txt》主要描述', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '了如何选择和', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '实施文档上传的方式', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '至Qwen-', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': 'Long模型，以便', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '该模型可以根据上传', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '的文档内容作出', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '回应。以下是文件', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '的主要内容总结：\\n\\n', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '### 文档上传', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '方式选择指南\\n', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '1. **通过', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': 'file-id上传**\\n', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '   - **推荐', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '理由**：适合', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '需要频繁引用和', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '管理的文档，', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '操作简便，能', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '有效减少文本输入', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '错误。\\n   -', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': ' **文件格式**', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '：支持TXT、', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': 'DOCX、PDF', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '、XLSX', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '、EPUB、', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': 'MOBI、MD', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '等文本文件，', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '不支持图片或', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '扫描文档。\\n  ', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': ' - **文件大小', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '限制**：单', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '文件不超过15', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '0MB，单', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '个阿里云账号', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '最多可上传1', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '万个文件，总', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '文件大小不超过1', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '00GB。\\n', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '   - **使用', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '场景**：长期', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '存储和频繁引用', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '的文档。\\n\\n2', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '. **通过纯', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '文本上传**\\n  ', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': ' - **适用场景', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '**：适合小', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '规模文档或临时', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '内容，文档较', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '短且不需要长期', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '存储。\\n   -', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': ' **限制**：', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '文本内容长度不能', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '超过100', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '万Token。\\n\\n3', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '. **通过JSON', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '字符串上传**\\n  ', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': ' - **适用场景', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '**：适合传递', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '复杂数据结构的', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '文档，确保数据', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '完整性。\\n   -', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': ' **格式要求**', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '：文档信息需', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '按`content`', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '（内容）、`', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': 'file_type`（', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '类型）、`filename', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '`（名称）、', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '`title`（', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '标题）的格式', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '组织成JSON字符串', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '。\\n\\n### 使用file', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '-id上传文档的具体', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '步骤\\n- **', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '上传文件**：', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '通过OpenAI兼容', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '接口上传文件并', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '获取file-id。\\n', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '- **配置对话', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '**：将file', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '-id传入System', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': ' Message中，并在', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': 'User Message中输入', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '问题。\\n- **', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '流式输出**', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '：设置stream及', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': 'stream_options参数，', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '使模型流式', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '输出回复并展示', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': 'Token使用情况。\\n\\n', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '### 多文档', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '处理\\n- **', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '传入多文档', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '**：可以在一条', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': 'System Message中传', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '入多个file-id', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '。\\n- **追', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '加文档**：', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '在对话过程中追', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '加新的file-id', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '，使模型参考', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '新文档信息。\\n\\n', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '### 限制与', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '注意事项\\n- **', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '文件上传限制**', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '：单文件大小', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '不超过150', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': 'MB，总量不超过', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '1万个文件，', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '总文件大小不超过', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '100GB', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '。\\n- **输入', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '输出限制**：', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '系统和用户的消息', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '限制为9,', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '000 Token', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '，file-id方式', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '的最大输入限制为', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '10,0', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '00,0', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '00 Token，', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': 'file-id数量不超过', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '100个', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '。\\n- **输出', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '限制**：最大', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '输出为6,', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '000 Token', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '。\\n- **费用', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '计算**：根据', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '输入和输出的', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': 'Token数量分别计', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '费。\\n\\n### ', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '常见问题', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '解答\\n- **', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': 'SDK兼容性**', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '：Dashscope SDK', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '兼容，但文件', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '上传与file-id', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '获取目前只支持', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '通过OpenAI SDK', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '。\\n- **图片', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '处理**：Q', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': 'wen-Long不', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '支持图片或扫描', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '文档，建议使用', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': 'Qwen-VL', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '视觉理解模型。\\n', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '- **流式', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '回复**：支持', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '通过设置stream参数', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '实现。\\n- **', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '批量任务**：', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '支持批量提交任务', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '，费用为实时', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '调用的5', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '0%。\\n-', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': ' **file-id共享', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '**：file-id', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '只能在同一阿里云', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '账号内的API Key', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '之间共享。\\n\\n文件', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '还提供了详细的代码', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '示例，帮助', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '用户更好地理解和应用', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'id': 'chatcmpl-6b758bd2-b562-927f-8ccb-20c166b4b886', 'choices': [{'delta': {'content': '上述上传方式。', 'function_call': None, 'refusal': None, 'role': None, 'tool_calls': None}, 'finish_reason': None, 'index': 0, 'logprobs': None}], 'created': 1739239824, 'model': 'qwen-long', 'object': 'chat.completion.chunk', 'service_tier': None, 'system_fingerprint': None, 'usage': None}\n", "{'文件《Text.txt》主要描述了如何选择和实施文档上传的方式至Qwen-Long模型，以便该模型可以根据上传的文档内容作出回应。以下是文件的主要内容总结：\\n\\n### 文档上传方式选择指南\\n1. **通过file-id上传**\\n   - **推荐理由**：适合需要频繁引用和管理的文档，操作简便，能有效减少文本输入错误。\\n   - **文件格式**：支持TXT、DOCX、PDF、XLSX、EPUB、MOBI、MD等文本文件，不支持图片或扫描文档。\\n   - **文件大小限制**：单文件不超过150MB，单个阿里云账号最多可上传1万个文件，总文件大小不超过100GB。\\n   - **使用场景**：长期存储和频繁引用的文档。\\n\\n2. **通过纯文本上传**\\n   - **适用场景**：适合小规模文档或临时内容，文档较短且不需要长期存储。\\n   - **限制**：文本内容长度不能超过100万Token。\\n\\n3. **通过JSON字符串上传**\\n   - **适用场景**：适合传递复杂数据结构的文档，确保数据完整性。\\n   - **格式要求**：文档信息需按`content`（内容）、`file_type`（类型）、`filename`（名称）、`title`（标题）的格式组织成JSON字符串。\\n\\n### 使用file-id上传文档的具体步骤\\n- **上传文件**：通过OpenAI兼容接口上传文件并获取file-id。\\n- **配置对话**：将file-id传入System Message中，并在User Message中输入问题。\\n- **流式输出**：设置stream及stream_options参数，使模型流式输出回复并展示Token使用情况。\\n\\n### 多文档处理\\n- **传入多文档**：可以在一条System Message中传入多个file-id。\\n- **追加文档**：在对话过程中追加新的file-id，使模型参考新文档信息。\\n\\n### 限制与注意事项\\n- **文件上传限制**：单文件大小不超过150MB，总量不超过1万个文件，总文件大小不超过100GB。\\n- **输入输出限制**：系统和用户的消息限制为9,000 Token，file-id方式的最大输入限制为10,000,000 Token，file-id数量不超过100个。\\n- **输出限制**：最大输出为6,000 Token。\\n- **费用计算**：根据输入和输出的Token数量分别计费。\\n\\n### 常见问题解答\\n- **SDK兼容性**：Dashscope SDK兼容，但文件上传与file-id获取目前只支持通过OpenAI SDK。\\n- **图片处理**：Qwen-Long不支持图片或扫描文档，建议使用Qwen-VL视觉理解模型。\\n- **流式回复**：支持通过设置stream参数实现。\\n- **批量任务**：支持批量提交任务，费用为实时调用的50%。\\n- **file-id共享**：file-id只能在同一阿里云账号内的API Key之间共享。\\n\\n文件还提供了详细的代码示例，帮助用户更好地理解和应用上述上传方式。'}\n"]}], "execution_count": 8}, {"metadata": {"ExecuteTime": {"end_time": "2025-02-13T06:19:47.626588Z", "start_time": "2025-02-13T06:19:47.619077Z"}}, "cell_type": "code", "source": ["import secrets\n", "secret_key = secrets.token_hex(16)  # 生成长度为 16 的随机字符串\n", "secret_key"], "id": "f4991299705ef74a", "outputs": [{"data": {"text/plain": ["'6024bd320f924bf14922aa0a81cd7faf'"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "execution_count": 2}, {"metadata": {"ExecuteTime": {"end_time": "2025-04-22T07:09:23.425384Z", "start_time": "2025-04-22T07:09:03.607666Z"}}, "cell_type": "code", "source": ["import json\n", "from http import HTTPStatus\n", "import os\n", "from dashscope import Application\n", "\n", "def call_with_stream():\n", "    biz_params = {\n", "        \"abstract\": \"RAG\",\n", "        \"month\": \"5\"\n", "    }\n", "    responses = Application.call(\n", "        api_key=os.getenv(\"DASHSCOPE_API_KEY\"),  # 替换为实际 API Key\n", "        app_id='98476d1974e44ca98066328927954f3a',  # 替换为实际应用 ID\n", "        prompt='rag',\n", "        stream=True,\n", "        biz_params=biz_params,\n", "        flow_stream_mode=\"agent_format\",\n", "        incremental_output=True\n", "    )\n", "\n", "    full_text = \"\"\n", "    for response in responses:\n", "        if response.status_code != HTTPStatus.OK:\n", "            print(f\"请求失败: code={response.status_code}, message={response.message}\")\n", "            return\n", "        else:\n", "            full_text += response.output.text  # 拼接增量内容\n", "            print(f\"流式输出: {response.output.text}\")\n", "            print(f\"当前完整输出: {full_text}\")\n", "\n", "if __name__ == \"__main__\":\n", "    call_with_stream()"], "id": "3bb946c19fe72ba9", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["流式输出: ### 整体分析  \n", "您的论文主题为 RAG（Retrieval-Augmented Generation），主要关注于结合检索与生成技术的研究。该领域的核心在于如何通过增强检索能力来提升文本生成质量，适用于自然语言处理、对话系统及多模态任务等应用场景。根据您的论文内容，推荐的会议应侧重于人工智能技术、信息检索以及相关领域中的前沿研究。从提供的会议信息来看，\"第四届新能源技术创新与低碳发展国际研讨会\"与\"第九届能源技术与材料科学国际学术会议\"更贴近能源与可持续发展的热点问题，而\"2025通信系统与通信网络国际学研讨会\"和\"2025年数字经济与智能计算国际学术会议\"则更偏向于通信网络及智能计算方向。\n", "\n", "---\n", "\n", "### 推荐结果  \n", "\n", "#### 1. 第四届新能源技术创新与低碳发展国际研讨会（NET-LC 2025）  \n", "- **会议时间**：2025-05-09  \n", "- **会议地点**：杭州市  \n", "- **会议检索**：IEEE Xplore, EI, Scopus, Inspec  \n", "- **访问链接**：www.ais.cn/attendees/index/NET-LC2025  \n", "- **推荐理由**：该会议的主题围绕可再生能源系统与智能电网展开，与您的论文在能源领域结合人工智能技术的研究方向高度契合，同时具备较高的学术影响力和检索价值。  \n", "\n", "#### 2. 第九届能源技术与材料科学国际学术会议（ICETMS 2025）  \n", "- **会议时间**：2025-05-23  \n", "- **会议地点**：西安市  \n", "- **会议检索**：EI, Scopus  \n", "- **访问链接**：www.ais.cn/attendees/index/ICETMS2025  \n", "- **推荐理由**：该会议聚焦新能源技术与储能技术，与您的论文在能源领域的研究高度相关。会议具有较强的行业影响力，有助于提升研究成果的曝光率。  \n", "\n", "#### 3. 2025通信系统与通信网络国际学研讨会（CSACN 2025）  \n", "- **会议时间**：2025-05-09  \n", "- **会议地点**：广州市  \n", "- **会议检索**：EI, Scopus, IEEE Xplore  \n", "- **访问链接**：www.ais.cn/attendees/index/CSACN2025  \n", "- **推荐理由**：该会议涉及网络安全与无线通信等主题，部分方向与您的研究在信息检索与生成领域的结合点有所重合，适合希望拓展通信领域合作的研究者。  \n", "\n", "#### 4. 2025年数字经济与智能计算国际学术会议（DEIC 2025）  \n", "- **会议时间**：2025-05-23  \n", "- **会议地点**：上海市  \n", "- **会议检索**：EI, Scopus  \n", "- **访问链接**：www.ais.cn/attendees/index/DEIC2025  \n", "- **推荐理由**：该会议强调人工智能伦理与智能计算的应用，虽然与您的论文直接关联稍弱，但提供了探索人工智能技术商业与社会影响的机会，适合寻求跨学科交流的学者。  \n", "\n", "--- \n", "\n", "如需进一步指导，请随时联系！\n", "\n", "\n", "当前完整输出: ### 整体分析  \n", "您的论文主题为 RAG（Retrieval-Augmented Generation），主要关注于结合检索与生成技术的研究。该领域的核心在于如何通过增强检索能力来提升文本生成质量，适用于自然语言处理、对话系统及多模态任务等应用场景。根据您的论文内容，推荐的会议应侧重于人工智能技术、信息检索以及相关领域中的前沿研究。从提供的会议信息来看，\"第四届新能源技术创新与低碳发展国际研讨会\"与\"第九届能源技术与材料科学国际学术会议\"更贴近能源与可持续发展的热点问题，而\"2025通信系统与通信网络国际学研讨会\"和\"2025年数字经济与智能计算国际学术会议\"则更偏向于通信网络及智能计算方向。\n", "\n", "---\n", "\n", "### 推荐结果  \n", "\n", "#### 1. 第四届新能源技术创新与低碳发展国际研讨会（NET-LC 2025）  \n", "- **会议时间**：2025-05-09  \n", "- **会议地点**：杭州市  \n", "- **会议检索**：IEEE Xplore, EI, Scopus, Inspec  \n", "- **访问链接**：www.ais.cn/attendees/index/NET-LC2025  \n", "- **推荐理由**：该会议的主题围绕可再生能源系统与智能电网展开，与您的论文在能源领域结合人工智能技术的研究方向高度契合，同时具备较高的学术影响力和检索价值。  \n", "\n", "#### 2. 第九届能源技术与材料科学国际学术会议（ICETMS 2025）  \n", "- **会议时间**：2025-05-23  \n", "- **会议地点**：西安市  \n", "- **会议检索**：EI, Scopus  \n", "- **访问链接**：www.ais.cn/attendees/index/ICETMS2025  \n", "- **推荐理由**：该会议聚焦新能源技术与储能技术，与您的论文在能源领域的研究高度相关。会议具有较强的行业影响力，有助于提升研究成果的曝光率。  \n", "\n", "#### 3. 2025通信系统与通信网络国际学研讨会（CSACN 2025）  \n", "- **会议时间**：2025-05-09  \n", "- **会议地点**：广州市  \n", "- **会议检索**：EI, Scopus, IEEE Xplore  \n", "- **访问链接**：www.ais.cn/attendees/index/CSACN2025  \n", "- **推荐理由**：该会议涉及网络安全与无线通信等主题，部分方向与您的研究在信息检索与生成领域的结合点有所重合，适合希望拓展通信领域合作的研究者。  \n", "\n", "#### 4. 2025年数字经济与智能计算国际学术会议（DEIC 2025）  \n", "- **会议时间**：2025-05-23  \n", "- **会议地点**：上海市  \n", "- **会议检索**：EI, Scopus  \n", "- **访问链接**：www.ais.cn/attendees/index/DEIC2025  \n", "- **推荐理由**：该会议强调人工智能伦理与智能计算的应用，虽然与您的论文直接关联稍弱，但提供了探索人工智能技术商业与社会影响的机会，适合寻求跨学科交流的学者。  \n", "\n", "--- \n", "\n", "如需进一步指导，请随时联系！\n", "\n", "\n"]}], "execution_count": 10}, {"metadata": {"ExecuteTime": {"end_time": "2025-06-19T09:15:11.076824Z", "start_time": "2025-06-19T09:15:06.009787Z"}}, "cell_type": "code", "source": ["import os\n", "from http import HTTPStatus\n", "from dashscope import Application\n", "import gradio as gr\n", "\n", "def stream_rag(abstract: str, month: int):\n", "    \"\"\"\n", "    每次 yield 完整的累积文本，Gradio 会自动在前端更新。\n", "    \"\"\"\n", "    biz_params = {\"abstract\": abstract, \"month\": str(month)}\n", "    responses = Application.call(\n", "        api_key=os.getenv(\"DASHSCOPE_API_KEY\"),\n", "        app_id='98476d1974e44ca98066328927954f3a',\n", "        prompt='rag',\n", "        stream=True,\n", "        biz_params=biz_params,\n", "        flow_stream_mode=\"agent_format\",\n", "        incremental_output=True\n", "    )\n", "\n", "    full_text = \"\"\n", "    for response in responses:\n", "        if response.status_code != HTTPStatus.OK:\n", "            # 出错时直接返回错误文本并结束生成\n", "            yield f\"请求失败: code={response.status_code}，message={response.message}\"\n", "            return\n", "        chunk = response.output.text\n", "        full_text += chunk\n", "        # 把当前累积结果 yield 回去，前端会增量渲染\n", "        yield full_text\n", "\n", "# 使用 gr.Interface，回调返回生成器即可\n", "iface = gr.Interface(\n", "    fn=stream_rag,\n", "    inputs=[\n", "        gr.Textbox(lines=5, label=\"论文摘要\", placeholder=\"在此输入论文摘要…\"),\n", "        gr.Number(label=\"期望投稿月份\", value=5, precision=0, interactive=True),\n", "    ],\n", "    outputs=gr.Markdown(label=\"会议推荐结果\"),\n", "    title=\"会议推荐\",\n", "    description=\"输入论文摘要和期望月份，实时展示推荐结果。\",\n", ")\n", "\n", "if __name__ == \"__main__\":\n", "    iface.launch(share=True)\n"], "id": "304831642dbb7518", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["* Running on local URL:  http://127.0.0.1:7860\n", "* Running on public URL: https://b51fd571065fba4ef0.gradio.live\n", "\n", "This share link expires in 72 hours. For free permanent hosting and GPU upgrades, run `gradio deploy` from the terminal in the working directory to deploy to Hugging Face Spaces (https://huggingface.co/spaces)\n"]}, {"data": {"text/plain": ["<IPython.core.display.HTML object>"], "text/html": ["<div><iframe src=\"https://b51fd571065fba4ef0.gradio.live\" width=\"100%\" height=\"500\" allow=\"autoplay; camera; microphone; clipboard-read; clipboard-write;\" frameborder=\"0\" allowfullscreen></iframe></div>"]}, "metadata": {}, "output_type": "display_data"}], "execution_count": 1}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.6"}}, "nbformat": 4, "nbformat_minor": 5}