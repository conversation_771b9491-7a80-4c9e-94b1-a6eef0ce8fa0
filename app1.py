import os
import hashlib
from flask import Flask, render_template, request, jsonify
from alibabacloud_bailian20231229.client import Client as BailianClient
from alibabacloud_tea_openapi import models as open_api_models
from alibabacloud_bailian20231229 import models as bailian_models
from alibabacloud_tea_util import models as util_models
import requests

app = Flask(__name__)

# 从环境变量中获取阿里云凭证
ALIBABA_CLOUD_ACCESS_KEY_ID = os.getenv('ALIBABA_CLOUD_ACCESS_KEY_ID', 'your_access_key_id')
ALIBABA_CLOUD_ACCESS_KEY_SECRET = os.getenv('ALIBABA_CLOUD_ACCESS_KEY_SECRET', 'your_access_key_secret')
WORKSPACE_ID = os.getenv('WORKSPACE_ID', 'your_workspace_id')

# 初始化百炼客户端
def create_client():
    config = open_api_models.Config(
        access_key_id=ALIBABA_CLOUD_ACCESS_KEY_ID,
        access_key_secret=ALIBABA_CLOUD_ACCESS_KEY_SECRET
    )
    config.endpoint = 'bailian.cn-beijing.aliyuncs.com'
    return BailianClient(config)

# 获取文件的MD5值
def get_file_md5(file_path):
    with open(file_path, 'rb') as file:
        return hashlib.md5(file.read()).hexdigest()

# 申请文件上传租约
def apply_file_upload_lease(file_name, file_size):
    client = create_client()
    apply_request = bailian_models.ApplyFileUploadLeaseRequest(
        category_id='default',
        file_name=file_name,
        md5=get_file_md5(file_name),
        size_in_bytes=str(file_size)
    )
    runtime = util_models.RuntimeOptions()

    lease_response = client.apply_file_upload_lease_with_options(WORKSPACE_ID, '', apply_request, {}, runtime)
    return lease_response

# 创建索引
def create_index(index_name):
    client = create_client()
    create_index_request = bailian_models.CreateIndexRequest(
        name=index_name,
        structure_type='unstructured',
        source_type='DATA_CENTER_FILE',
        sink_type='BUILT_IN'
    )
    runtime = util_models.RuntimeOptions()
    headers = {}
    try:
        client.create_index_with_options(WORKSPACE_ID, create_index_request, headers, runtime)
        print(f"索引 {index_name} 创建成功")
    except Exception as error:
        print(f"创建索引时出错: {error.message}")

# 提交索引任务
def submit_index_job():
    client = create_client()
    submit_index_job_request = bailian_models.SubmitIndexJobRequest()
    runtime = util_models.RuntimeOptions()
    headers = {}
    try:
        client.submit_index_job_with_options(WORKSPACE_ID, submit_index_job_request, headers, runtime)
        print("索引任务提交成功")
    except Exception as error:
        print(f"提交索引任务时出错: {error.message}")


@app.route('/')
def index():
    return render_template('index1.html')

@app.route('/upload', methods=['POST'])
def upload_file():
    file = request.files['file']
    if not file:
        return jsonify({'message': 'No file uploaded'}), 400

    file_name = file.filename
    file_size = len(file.read())
    file.seek(0)  # 重新读取文件内容

    # 申请上传租约
    lease_response = apply_file_upload_lease(file_name, file_size)
    if lease_response['Success']:
        lease_url = lease_response['Data']['Param']['Url']
        headers = {
            "X-bailian-extra": lease_response['Data']['Param']['Headers']['X-bailian-extra'],
            "Content-Type": "application/docx"  # 假设上传的是PDF文件，根据实际文件类型修改
        }

        # 上传文件
        upload_response = add_file_to_bailian(lease_url, file, headers)
        if upload_response.status_code == 200:
            print("文件上传成功")
            # 创建索引
            create_index('企业帮助文档库')
            # 提交索引任务
            submit_index_job()
            return jsonify({'message': 'File uploaded and index job submitted successfully'}), 200
        else:
            return jsonify({'message': 'File upload failed'}), 500
    else:
        return jsonify({'message': 'Lease acquisition failed'}), 500

if __name__ == '__main__':
    app.run(debug=True)
