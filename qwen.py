import os
from http import HTTPStatus
from dashscope import Application


def multi_turn_with_knowledge():
    try:
        # 初始化会话参数
        session_history = [
            {
                "role": "system",
                "content": "你是一个智能客服，根据知识库内容回答问题，回答需标注引用来源。"
            },
            {
                "role": "user",
                "content": {"text": "百炼手机Zephyr Z9的屏幕尺寸是多少？"}
            }
        ]

        # 第一次调用（带知识库）
        response = Application.call(
            api_key="sk-470a9e513830456aaf67cff29209e4cb",  # 优先从环境变量读取
            app_id="44e06e185cda46e99ab370e0f1364aa2",  # 替换为实际应用ID
            messages=session_history,  # 传入历史对话
            parameters={
                "has_thoughts": True,  # 显示知识检索过程
                "rag_options": {
                    "pipeline_ids": ["gl1zdsvh0q"],  # 必填：知识库ID
                    # "file_ids": ["specific-doc-id"],          # 可选：指定文档
                    # "metadata_filter": {"category": "手机参数"} # 可选：元数据过滤
                }
            }
        )

        # 处理第一次响应
        if response.status_code == HTTPStatus.OK:
            print("=== 第一轮回答 ===")
            print(f"AI回复：{response.output.text}\n")

            # 将AI回复加入历史
            session_history.append({
                "role": "assistant",
                "content": response.output.text
            })

            # 展示知识库引用
            if response.output.doc_references:
                print("【引用文档】")
                for doc in response.output.doc_references:
                    print(f"-> [{doc.index_id}] {doc.doc_name}\n  内容片段：{doc.text[:80]}...")

        else:
            print(f"首次调用失败：{response.code} - {response.message}")
            return

        # 第二轮对话（延续上下文）
        new_question = {"role": "user", "content": {"text": "这款手机支持卫星通话吗？"}}
        session_history.append(new_question)

        response = Application.call(
            api_key=os.getenv("DASHSCOPE_API_KEY"),
            app_id="44e06e185cda46e99ab370e0f1364aa2",
            messages=session_history,  # 携带完整历史
            parameters={
                "has_thoughts": True,
                "rag_options": {
                    "pipeline_ids": ["gl1zdsvh0q"]
                }
            }
        )

        # 处理第二轮响应
        if response.status_code == HTTPStatus.OK:
            print("\n=== 第二轮回答 ===")
            print(f"AI回复：{response.output.text}")

            # 实时显示思考过程（需has_thoughts=True）
            if response.output.thoughts:
                print("\n【思考链路】")
                for i, thought in enumerate(response.output.thoughts, 1):
                    print(f"{i}. {thought.action}: {thought.observation[:60]}...")

        else:
            print(f"二次调用失败：{response.code} - {response.message}")

    except Exception as e:
        print(f"发生异常：{str(e)}")


if __name__ == "__main__":
    # 环境检查
    required_version = (1, 20, 14)

    multi_turn_with_knowledge()