-- 创建数据库
CREATE DATABASE IF NOT EXISTS ecommerce_analysis DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE ecommerce_analysis;

-- 商品基础信息表
CREATE TABLE IF NOT EXISTS product_base (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    product_id VARCHAR(50) NOT NULL COMMENT '商品ID',
    platform ENUM('taobao', 'jd', 'amazon_cn', 'tmall') NOT NULL COMMENT '平台',
    product_name VARCHAR(255) NOT NULL COMMENT '商品名称',
    product_url VARCHAR(512) NOT NULL COMMENT '商品URL',
    category VARCHAR(100) COMMENT '商品类别',
    brand VARCHAR(100) COMMENT '品牌',
    seller VARCHAR(100) COMMENT '卖家',
    create_time DATETIME NOT NULL COMMENT '创建时间',
    update_time DATETIME NOT NULL COMMENT '更新时间',
    UNIQUE KEY idx_platform_product_id (platform, product_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品基础信息表';

-- 价格信息表
CREATE TABLE IF NOT EXISTS product_price (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    product_id VARCHAR(50) NOT NULL COMMENT '商品ID',
    platform ENUM('taobao', 'jd', 'amazon_cn', 'tmall') NOT NULL COMMENT '平台',
    price DECIMAL(10,2) NOT NULL COMMENT '当前价格',
    original_price DECIMAL(10,2) COMMENT '原价',
    currency VARCHAR(10) DEFAULT 'CNY' COMMENT '货币单位',
    in_stock TINYINT(1) DEFAULT 1 COMMENT '是否有货',
    stock_quantity INT COMMENT '库存数量',
    create_time DATETIME NOT NULL COMMENT '创建时间',
    KEY idx_platform_product_id (platform, product_id),
    KEY idx_create_time (create_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品价格信息表';

-- 促销信息表
CREATE TABLE IF NOT EXISTS promotion (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    product_id VARCHAR(50) NOT NULL COMMENT '商品ID',
    platform ENUM('taobao', 'jd', 'amazon_cn', 'tmall') NOT NULL COMMENT '平台',
    promotion_type VARCHAR(50) COMMENT '促销类型',
    promotion_desc VARCHAR(255) COMMENT '促销描述',
    discount_amount DECIMAL(10,2) COMMENT '优惠金额',
    discount_rate DECIMAL(5,2) COMMENT '折扣率',
    start_time DATETIME COMMENT '开始时间',
    end_time DATETIME COMMENT '结束时间',
    create_time DATETIME NOT NULL COMMENT '创建时间',
    KEY idx_platform_product_id (platform, product_id),
    KEY idx_time_range (start_time, end_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='促销信息表';

-- 评价信息表
CREATE TABLE IF NOT EXISTS review (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    product_id VARCHAR(50) NOT NULL COMMENT '商品ID',
    platform ENUM('taobao', 'jd', 'amazon_cn', 'tmall') NOT NULL COMMENT '平台',
    review_count INT NOT NULL DEFAULT 0 COMMENT '评价总数',
    average_rating DECIMAL(3,1) COMMENT '平均评分',
    positive_rate DECIMAL(5,2) COMMENT '好评率',
    create_time DATETIME NOT NULL COMMENT '创建时间',
    KEY idx_platform_product_id (platform, product_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='评价信息表';

-- 评价标签表
CREATE TABLE IF NOT EXISTS review_tag (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    product_id VARCHAR(50) NOT NULL COMMENT '商品ID',
    platform ENUM('taobao', 'jd', 'amazon_cn', 'tmall') NOT NULL COMMENT '平台',
    tag VARCHAR(50) NOT NULL COMMENT '标签名称',
    count INT NOT NULL DEFAULT 0 COMMENT '出现次数',
    sentiment ENUM('positive', 'negative', 'neutral') COMMENT '情感倾向',
    create_time DATETIME NOT NULL COMMENT '创建时间',
    KEY idx_platform_product_id (platform, product_id),
    KEY idx_tag (tag)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='评价标签表';

-- 销售趋势表
CREATE TABLE IF NOT EXISTS sales_trend (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    product_id VARCHAR(50) NOT NULL COMMENT '商品ID',
    platform ENUM('taobao', 'jd', 'amazon_cn', 'tmall') NOT NULL COMMENT '平台',
    sales_indicator VARCHAR(50) COMMENT '销售指标',
    sales_value INT COMMENT '销售值',
    time_period VARCHAR(50) COMMENT '时间段',
    create_time DATETIME NOT NULL COMMENT '创建时间',
    KEY idx_platform_product_id (platform, product_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='销售趋势表';

-- 爬虫任务表
CREATE TABLE IF NOT EXISTS spider_task (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    task_id VARCHAR(50) NOT NULL COMMENT '任务ID',
    platform ENUM('taobao', 'jd', 'amazon_cn', 'tmall') NOT NULL COMMENT '平台',
    url VARCHAR(512) NOT NULL COMMENT '爬取URL',
    status ENUM('pending', 'running', 'completed', 'failed') DEFAULT 'pending' COMMENT '任务状态',
    retry_count INT DEFAULT 0 COMMENT '重试次数',
    create_time DATETIME NOT NULL COMMENT '创建时间',
    update_time DATETIME NOT NULL COMMENT '更新时间',
    UNIQUE KEY idx_task_id (task_id),
    KEY idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='爬虫任务表';

-- 用户表
CREATE TABLE IF NOT EXISTS user (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) NOT NULL COMMENT '用户名',
    password VARCHAR(100) NOT NULL COMMENT '密码',
    email VARCHAR(100) COMMENT '邮箱',
    role ENUM('admin', 'user') DEFAULT 'user' COMMENT '角色',
    create_time DATETIME NOT NULL COMMENT '创建时间',
    update_time DATETIME NOT NULL COMMENT '更新时间',
    UNIQUE KEY idx_username (username)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';

-- 竞品分析表
CREATE TABLE IF NOT EXISTS competitor_analysis (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    product_id VARCHAR(50) NOT NULL COMMENT '主商品ID',
    platform ENUM('taobao', 'jd', 'amazon_cn', 'tmall') NOT NULL COMMENT '主商品平台',
    competitor_product_id VARCHAR(50) NOT NULL COMMENT '竞品ID',
    competitor_platform ENUM('taobao', 'jd', 'amazon_cn', 'tmall') NOT NULL COMMENT '竞品平台',
    price_diff DECIMAL(10,2) COMMENT '价格差异',
    rating_diff DECIMAL(3,1) COMMENT '评分差异',
    review_count_diff INT COMMENT '评价数量差异',
    create_time DATETIME NOT NULL COMMENT '创建时间',
    KEY idx_product (product_id, platform),
    KEY idx_competitor (competitor_product_id, competitor_platform)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='竞品分析表';

-- 初始化管理员用户
INSERT INTO user (username, password, email, role, create_time, update_time)
VALUES ('admin', '$2b$12$eVGw9HXFfYeYGUVL9QOSZuYGRnO3Jqj6VECy4j6pSHgGzqgdA.Nte', '<EMAIL>', 'admin', NOW(), NOW())
ON DUPLICATE KEY UPDATE update_time = NOW();
