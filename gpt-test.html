<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>未来科技 | 着陆页</title>
  <style>
    * {
      margin: 0; padding: 0; box-sizing: border-box;
    }
    body {
      font-family: "Segoe UI", sans-serif;
      background: #f9f9f9;
      color: #333;
      scroll-behavior: smooth;
    }
    nav {
      position: fixed;
      width: 100%;
      background: rgba(255,255,255,0.95);
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 1rem 2rem;
      z-index: 1000;
    }
    nav h1 {
      font-size: 1.5rem;
      color: #4e54c8;
    }
    nav ul {
      display: flex;
      list-style: none;
      gap: 20px;
    }
    nav a {
      text-decoration: none;
      color: #333;
      font-weight: 500;
      transition: color 0.3s;
    }
    nav a:hover {
      color: #4e54c8;
    }
    header {
      background: linear-gradient(135deg, #4e54c8, #8f94fb);
      color: white;
      padding: 120px 20px 100px;
      text-align: center;
    }
    header h2 {
      font-size: 3rem;
      margin-bottom: 10px;
    }
    header p {
      font-size: 1.2rem;
    }
    section {
      padding: 80px 20px;
      max-width: 1100px;
      margin: auto;
    }
    .fade-in {
      opacity: 0;
      transform: translateY(30px);
      transition: all 0.6s ease;
    }
    .fade-in.appear {
      opacity: 1;
      transform: translateY(0);
    }
    .features, .services {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
      gap: 30px;
      margin-top: 40px;
    }
    .card {
      background: white;
      border-radius: 10px;
      padding: 30px;
      box-shadow: 0 8px 16px rgba(0,0,0,0.05);
      text-align: center;
      transition: transform 0.3s;
    }
    .card:hover {
      transform: translateY(-8px);
    }
    .card h3 {
      color: #4e54c8;
      margin-bottom: 10px;
    }
    .cta, .contact {
      background: #4e54c8;
      color: white;
      text-align: center;
      padding: 60px 20px;
      border-radius: 12px;
      margin-top: 60px;
    }
    .cta button {
      margin-top: 20px;
      background: white;
      color: #4e54c8;
      padding: 12px 24px;
      font-size: 1rem;
      border: none;
      border-radius: 6px;
      cursor: pointer;
      transition: background 0.3s;
    }
    .cta button:hover {
      background: #e0e0e0;
    }
    .contact form {
      display: flex;
      flex-direction: column;
      gap: 15px;
      max-width: 500px;
      margin: auto;
    }
    input, textarea {
      padding: 12px;
      border: none;
      border-radius: 6px;
      font-size: 1rem;
    }
    footer {
      background: #222;
      color: #aaa;
      text-align: center;
      padding: 20px;
      font-size: 0.9rem;
      margin-top: 40px;
    }
  </style>
</head>
<body>

  <nav>
    <h1>未来科技</h1>
    <ul>
      <li><a href="#about">关于我们</a></li>
      <li><a href="#services">服务</a></li>
      <li><a href="#testimonials">评价</a></li>
      <li><a href="#contact">联系</a></li>
    </ul>
  </nav>

  <header>
    <h2>欢迎来到未来科技</h2>
    <p>引领智能时代的科技先锋</p>
  </header>

  <section id="about" class="fade-in">
    <h2>关于我们</h2>
    <p>未来科技致力于人工智能、大数据与智能硬件的融合应用，为企业提供一站式智能解决方案。</p>
    <div class="features">
      <div class="card">
        <h3>智能算法</h3>
        <p>我们拥有自主研发的AI算法，提升效率，节省成本。</p>
      </div>
      <div class="card">
        <h3>数据驱动</h3>
        <p>以数据为核心，洞察用户需求，推动业务增长。</p>
      </div>
      <div class="card">
        <h3>专业团队</h3>
        <p>由顶尖工程师、产品经理组成的高效团队。</p>
      </div>
    </div>
  </section>

  <section id="services" class="fade-in">
    <h2>服务项目</h2>
    <div class="services">
      <div class="card">
        <h3>企业数字化转型</h3>
        <p>帮助企业实现从传统业务到智能运营的全面升级。</p>
      </div>
      <div class="card">
        <h3>智慧城市方案</h3>
        <p>构建城市级智能平台，提升公共管理效率。</p>
      </div>
      <div class="card">
        <h3>个性化推荐系统</h3>
        <p>为电商、内容平台等提供精准推荐服务。</p>
      </div>
    </div>
  </section>

  <section id="testimonials" class="fade-in">
    <h2>客户评价</h2>
    <div class="features">
      <div class="card">
        <p>“未来科技为我们的工厂引入AI视觉系统，大大提升了检测效率！”</p>
        <h4>—— 李总 · 智能制造</h4>
      </div>
      <div class="card">
        <p>“数据平台运行稳定，客户支持反应迅速，非常专业。”</p>
        <h4>—— 张经理 · 金融科技</h4>
      </div>
    </div>
  </section>

  <section class="cta fade-in">
    <h2>准备好迎接未来了吗？</h2>
    <p>立即加入我们，体验科技的无限可能</p>
    <button onclick="subscribe()">免费试用</button>
  </section>

  <section id="contact" class="contact fade-in">
    <h2>联系我们</h2>
    <form onsubmit="return submitForm(event)">
      <input type="text" id="name" placeholder="姓名" required />
      <input type="email" id="email" placeholder="邮箱" required />
      <textarea id="message" rows="5" placeholder="您的留言..." required></textarea>
      <button type="submit">提交信息</button>
    </form>
  </section>

  <footer>
    &copy; 2025 未来科技有限公司 | 版权所有
  </footer>

  <script>
    // 滚动出现动画
    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          entry.target.classList.add('appear');
        }
      });
    }, { threshold: 0.2 });

    document.querySelectorAll('.fade-in').forEach(el => observer.observe(el));

    // 订阅按钮
    function subscribe() {
      alert("感谢您的关注，我们将尽快与您联系！");
    }

    // 表单验证
    function submitForm(e) {
      e.preventDefault();
      const name = document.getElementById("name").value.trim();
      const email = document.getElementById("email").value.trim();
      const msg = document.getElementById("message").value.trim();

      if (!name || !email || !msg) {
        alert("请完整填写所有信息！");
        return false;
      }

      alert("提交成功，感谢您的留言！");
      e.target.reset();
      return false;
    }
  </script>

</body>
</html>
