// RAG-LLMs系统功能模块图
digraph {
	User [label="用户"]
	Upload [label="文档上传与解析"]
	KnowledgeBase [label="知识库构建与向量化"]
	IntentDetection [label="意图识别 (RASA DIET)"]
	Retrieval [label="检索模块 (FAISS)"]
	PromptConstruction [label="提示构建 (LangChain)"]
	LLM [label="答案生成 (Qwen2.5 / DeepSeek-V3)"]
	Response [label="回答输出与引用标注"]
	Admin [label="后台管理与监控"]
	User -> Upload
	Upload -> KnowledgeBase
	User -> IntentDetection
	IntentDetection -> Retrieval
	Retrieval -> PromptConstruction
	PromptConstruction -> LLM
	LLM -> Response
	Admin -> KnowledgeBase
	Admin -> Retrieval
	Admin -> LLM
}
