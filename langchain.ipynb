{"cells": [{"cell_type": "code", "id": "initial_id", "metadata": {"collapsed": true, "ExecuteTime": {"end_time": "2025-03-06T07:50:10.391803Z", "start_time": "2025-03-06T07:50:10.383138Z"}}, "source": ["from platform import python_version\n", "print(python_version())"], "outputs": [{"name": "stdout", "output_type": "stream", "text": ["3.10.9\n"]}], "execution_count": 1}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["from langchain_community.llms import Tongyi\n", "llm = Tongyi(dashscope_api_key=\"your_api_key\", model_name=\"qwen-max\")\n", "print(llm.invoke(\"测试问题\"))"], "id": "4130077db5d0a30d"}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.6"}}, "nbformat": 4, "nbformat_minor": 5}