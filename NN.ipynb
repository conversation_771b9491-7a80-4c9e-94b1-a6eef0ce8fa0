{"cells": [{"cell_type": "code", "id": "initial_id", "metadata": {"collapsed": true, "ExecuteTime": {"end_time": "2025-02-20T06:18:02.112671Z", "start_time": "2025-02-20T06:16:43.352115Z"}}, "source": ["import torch\n", "import torch.nn as nn\n", "import torch.optim as optim\n", "import torchvision\n", "import torchvision.transforms as transforms\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "\n", "# 1. 检查是否可以使用 CUDA\n", "device = torch.device(\"cuda\" if torch.cuda.is_available() else \"cpu\")\n", "print(f\"使用的设备: {device}\")\n", "\n", "# 2. 加载和预处理数据\n", "transform = transforms.Compose([transforms.ToTensor(), transforms.Normalize((0.5,), (0.5,))])\n", "\n", "trainset = torchvision.datasets.MNIST(root='./data', train=True, download=True, transform=transform)\n", "trainloader = torch.utils.data.DataLoader(trainset, batch_size=64, shuffle=True)\n", "\n", "testset = torchvision.datasets.MNIST(root='./data', train=False, download=True, transform=transform)\n", "testloader = torch.utils.data.DataLoader(testset, batch_size=64, shuffle=False)\n", "\n", "# 3. 定义神经网络模型\n", "class Net(nn.Module):\n", "    def __init__(self):\n", "        super(Net, self).__init__()\n", "        self.fc1 = nn.Linear(28 * 28, 128)  # 输入层到隐藏层\n", "        self.fc2 = nn.Linear(128, 64)       # 隐藏层到隐藏层\n", "        self.fc3 = nn.<PERSON>ar(64, 10)        # 隐藏层到输出层\n", "\n", "    def forward(self, x):\n", "        x = x.view(-1, 28 * 28)  # 将图像展平为向量\n", "        x = torch.relu(self.fc1(x))\n", "        x = torch.relu(self.fc2(x))\n", "        x = self.fc3(x)\n", "        return x\n", "\n", "# 将模型移动到 GPU\n", "model = Net().to(device)\n", "\n", "# 4. 定义损失函数和优化器\n", "criterion = nn.CrossEntropyLoss()\n", "optimizer = optim.<PERSON>(model.parameters(), lr=0.001)\n", "\n", "# 5. 训练模型\n", "num_epochs = 10\n", "for epoch in range(num_epochs):\n", "    running_loss = 0.0\n", "    for i, data in enumerate(trainloader, 0):\n", "        inputs, labels = data\n", "        # 将输入数据和标签移动到 GPU\n", "        inputs, labels = inputs.to(device), labels.to(device)\n", "        \n", "        optimizer.zero_grad()\n", "        outputs = model(inputs)\n", "        loss = criterion(outputs, labels)\n", "        loss.backward()\n", "        optimizer.step()\n", "        running_loss += loss.item()\n", "        if i % 100 == 99:  # 每100个批次打印一次\n", "            print(f'Epoch {epoch + 1}, Batch {i + 1}, Loss: {running_loss / 100:.3f}')\n", "            running_loss = 0.0\n", "\n", "print('训练完成')\n", "\n", "# 6. 在测试集上评估模型\n", "correct = 0\n", "total = 0\n", "with torch.no_grad():\n", "    for data in testloader:\n", "        images, labels = data\n", "        # 将测试数据移动到 GPU\n", "        images, labels = images.to(device), labels.to(device)\n", "        outputs = model(images)\n", "        _, predicted = torch.max(outputs.data, 1)\n", "        total += labels.size(0)\n", "        correct += (predicted == labels).sum().item()\n", "\n", "print(f'测试集上的准确率: {100 * correct / total:.2f}%')\n", "\n", "# 7. 可视化一些测试结果\n", "dataiter = iter(testloader)\n", "images, labels = next(dataiter)\n", "# 将数据移动到 GPU 进行预测\n", "images, labels = images.to(device), labels.to(device)\n", "outputs = model(images)\n", "_, predicted = torch.max(outputs, 1)\n", "\n", "# 将数据移回 CPU 以供可视化\n", "images = images.cpu()\n", "predicted = predicted.cpu()\n", "labels = labels.cpu()\n", "\n", "# 显示前6张图像及其预测结果\n", "fig, axes = plt.subplots(1, 6, figsize=(12, 2))\n", "for i in range(6):\n", "    ax = axes[i]\n", "    ax.imshow(images[i].numpy().squeeze(), cmap='gray')\n", "    ax.set_title(f'pred: {predicted[i].item()}')\n", "    ax.axis('off')\n", "plt.show()"], "outputs": [{"name": "stdout", "output_type": "stream", "text": ["使用的设备: cuda\n", "Epoch 1, <PERSON><PERSON> 100, Loss: 1.029\n", "Epoch 1, <PERSON><PERSON> 200, Loss: 0.439\n", "Epoch 1, <PERSON><PERSON> 300, Loss: 0.370\n", "Epoch 1, <PERSON><PERSON> 400, Loss: 0.364\n", "Epoch 1, <PERSON><PERSON> 500, Loss: 0.316\n", "Epoch 1, <PERSON><PERSON> 600, Loss: 0.318\n", "Epoch 1, <PERSON><PERSON> 700, Loss: 0.282\n", "Epoch 1, <PERSON><PERSON> 800, Loss: 0.276\n", "Epoch 1, <PERSON><PERSON> 900, Loss: 0.250\n", "Epoch 2, <PERSON><PERSON> 100, Loss: 0.206\n", "Epoch 2, <PERSON><PERSON> 200, Loss: 0.216\n", "Epoch 2, <PERSON><PERSON> 300, Loss: 0.215\n", "Epoch 2, <PERSON><PERSON> 400, Loss: 0.198\n", "Epoch 2, <PERSON><PERSON> 500, Loss: 0.185\n", "Epoch 2, <PERSON><PERSON> 600, Loss: 0.178\n", "Epoch 2, <PERSON><PERSON> 700, Loss: 0.173\n", "Epoch 2, <PERSON><PERSON> 800, Loss: 0.170\n", "Epoch 2, <PERSON><PERSON> 900, Loss: 0.165\n", "Epoch 3, <PERSON><PERSON> 100, Loss: 0.137\n", "Epoch 3, <PERSON><PERSON> 200, Loss: 0.150\n", "Epoch 3, <PERSON><PERSON> 300, Loss: 0.143\n", "Epoch 3, <PERSON><PERSON> 400, Loss: 0.153\n", "Epoch 3, <PERSON><PERSON> 500, Loss: 0.141\n", "Epoch 3, <PERSON><PERSON> 600, Loss: 0.134\n", "Epoch 3, <PERSON><PERSON> 700, Loss: 0.136\n", "Epoch 3, <PERSON><PERSON> 800, Loss: 0.135\n", "Epoch 3, <PERSON><PERSON> 900, Loss: 0.135\n", "Epoch 4, <PERSON><PERSON> 100, Loss: 0.112\n", "Epoch 4, <PERSON><PERSON> 200, Loss: 0.115\n", "Epoch 4, <PERSON><PERSON> 300, Loss: 0.126\n", "Epoch 4, <PERSON><PERSON> 400, Loss: 0.112\n", "Epoch 4, <PERSON><PERSON> 500, Loss: 0.120\n", "Epoch 4, <PERSON><PERSON> 600, Loss: 0.114\n", "Epoch 4, <PERSON><PERSON> 700, Loss: 0.114\n", "Epoch 4, <PERSON><PERSON> 800, Loss: 0.110\n", "Epoch 4, <PERSON><PERSON> 900, Loss: 0.117\n", "Epoch 5, <PERSON><PERSON> 100, Loss: 0.101\n", "Epoch 5, <PERSON><PERSON> 200, Loss: 0.106\n", "Epoch 5, <PERSON><PERSON> 300, Loss: 0.100\n", "Epoch 5, <PERSON><PERSON> 400, Loss: 0.086\n", "Epoch 5, <PERSON><PERSON> 500, Loss: 0.105\n", "Epoch 5, <PERSON><PERSON> 600, Loss: 0.101\n", "Epoch 5, <PERSON><PERSON> 700, Loss: 0.089\n", "Epoch 5, <PERSON><PERSON> 800, Loss: 0.100\n", "Epoch 5, <PERSON><PERSON> 900, Loss: 0.093\n", "Epoch 6, <PERSON><PERSON> 100, Loss: 0.073\n", "Epoch 6, <PERSON><PERSON> 200, Loss: 0.085\n", "Epoch 6, <PERSON><PERSON> 300, Loss: 0.089\n", "Epoch 6, <PERSON><PERSON> 400, Loss: 0.082\n", "Epoch 6, <PERSON><PERSON> 500, Loss: 0.089\n", "Epoch 6, <PERSON><PERSON> 600, Loss: 0.089\n", "Epoch 6, <PERSON><PERSON> 700, Loss: 0.085\n", "Epoch 6, <PERSON><PERSON> 800, Loss: 0.093\n", "Epoch 6, <PERSON><PERSON> 900, Loss: 0.079\n", "Epoch 7, <PERSON><PERSON> 100, Loss: 0.067\n", "Epoch 7, <PERSON><PERSON> 200, Loss: 0.079\n", "Epoch 7, <PERSON><PERSON> 300, Loss: 0.084\n", "Epoch 7, <PERSON><PERSON> 400, Loss: 0.076\n", "Epoch 7, <PERSON><PERSON> 500, Loss: 0.078\n", "Epoch 7, <PERSON><PERSON> 600, Loss: 0.076\n", "Epoch 7, <PERSON><PERSON> 700, Loss: 0.078\n", "Epoch 7, <PERSON><PERSON> 800, Loss: 0.079\n", "Epoch 7, <PERSON><PERSON> 900, Loss: 0.072\n", "Epoch 8, <PERSON><PERSON> 100, Loss: 0.059\n", "Epoch 8, <PERSON><PERSON> 200, Loss: 0.056\n", "Epoch 8, <PERSON><PERSON> 300, Loss: 0.073\n", "Epoch 8, <PERSON><PERSON> 400, Loss: 0.075\n", "Epoch 8, <PERSON><PERSON> 500, Loss: 0.057\n", "Epoch 8, <PERSON><PERSON> 600, Loss: 0.065\n", "Epoch 8, <PERSON><PERSON> 700, Loss: 0.070\n", "Epoch 8, <PERSON><PERSON> 800, Loss: 0.072\n", "Epoch 8, <PERSON><PERSON> 900, Loss: 0.077\n", "Epoch 9, <PERSON><PERSON> 100, Loss: 0.056\n", "Epoch 9, <PERSON><PERSON> 200, Loss: 0.053\n", "Epoch 9, <PERSON><PERSON> 300, Loss: 0.059\n", "Epoch 9, <PERSON><PERSON> 400, Loss: 0.048\n", "Epoch 9, <PERSON><PERSON> 500, Loss: 0.069\n", "Epoch 9, <PERSON><PERSON> 600, Loss: 0.062\n", "Epoch 9, <PERSON><PERSON> 700, Loss: 0.075\n", "Epoch 9, <PERSON><PERSON> 800, Loss: 0.055\n", "Epoch 9, <PERSON><PERSON> 900, Loss: 0.082\n", "Epoch 10, <PERSON><PERSON> 100, Loss: 0.050\n", "Epoch 10, <PERSON><PERSON> 200, Loss: 0.055\n", "Epoch 10, <PERSON><PERSON> 300, Loss: 0.060\n", "Epoch 10, <PERSON><PERSON> 400, Loss: 0.055\n", "Epoch 10, <PERSON><PERSON> 500, Loss: 0.054\n", "Epoch 10, <PERSON><PERSON> 600, Loss: 0.051\n", "Epoch 10, <PERSON><PERSON> 700, Loss: 0.050\n", "Epoch 10, <PERSON><PERSON> 800, Loss: 0.067\n", "Epoch 10, <PERSON><PERSON> 900, Loss: 0.060\n", "训练完成\n", "测试集上的准确率: 96.98%\n"]}, {"data": {"text/plain": ["<Figure size 1200x200 with 6 Axes>"], "image/png": "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"}, "metadata": {}, "output_type": "display_data"}], "execution_count": 3}, {"metadata": {"ExecuteTime": {"end_time": "2025-02-20T06:40:26.901482Z", "start_time": "2025-02-20T06:38:20.659198Z"}}, "cell_type": "code", "source": ["import pygame\n", "import random\n", "\n", "# 初始化Pygame\n", "pygame.init()\n", "\n", "# 游戏参数\n", "WIDTH, HEIGHT = 300, 600\n", "BLOCK_SIZE = 30\n", "GRID_WIDTH, GRID_HEIGHT = WIDTH // BLOCK_SIZE, HEIGHT // BLOCK_SIZE\n", "\n", "# 颜色定义\n", "BLACK = (0, 0, 0)\n", "WHITE = (255, 255, 255)\n", "COLORS = [\n", "    (0, 255, 255),  # I - 青色\n", "    (255, 255, 0),  # O - 黄色\n", "    (128, 0, 128),  # T - 紫色\n", "    (0, 255, 0),    # S - 绿色\n", "    (255, 0, 0),    # Z - 红色\n", "    (0, 0, 255),    # J - 蓝色\n", "    (255, 127, 0)   # L - 橙色\n", "]\n", "\n", "# 方块形状\n", "SHAPES = [\n", "    [[1, 1, 1, 1]],           # I\n", "    [[1, 1], [1, 1]],         # O\n", "    [[0, 1, 0], [1, 1, 1]],   # T\n", "    [[0, 1, 1], [1, 1, 0]],   # S\n", "    [[1, 1, 0], [0, 1, 1]],   # Z\n", "    [[1, 0, 0], [1, 1, 1]],   # J\n", "    [[0, 0, 1], [1, 1, 1]]    # L\n", "]\n", "\n", "# 创建游戏窗口\n", "screen = pygame.display.set_mode((WIDTH, HEIGHT))\n", "pygame.display.set_caption(\"<PERSON><PERSON><PERSON>\")\n", "clock = pygame.time.Clock()\n", "\n", "# 定义方块类\n", "class Piece:\n", "    def __init__(self, shape, type_idx):\n", "        self.shape = [row[:] for row in shape]  # 复制形状，避免修改原始SHAPES\n", "        self.type = type_idx  # 记录方块类型索引\n", "        self.color = COLORS[type_idx]\n", "        self.x = GRID_WIDTH // 2 - len(shape[0]) // 2\n", "        self.y = 0\n", "\n", "    def rotate(self):\n", "        # 顺时针旋转方块\n", "        self.shape = [list(row) for row in zip(*self.shape[::-1])]\n", "\n", "    def draw(self, screen):\n", "        for i, row in enumerate(self.shape):\n", "            for j, cell in enumerate(row):\n", "                if cell:\n", "                    pygame.draw.rect(screen, self.color,\n", "                                     ((self.x + j) * BLOCK_SIZE,\n", "                                      (self.y + i) * BLOCK_SIZE,\n", "                                      BLOCK_SIZE, BLOCK_SIZE))\n", "\n", "# 定义游戏板类\n", "class Board:\n", "    def __init__(self):\n", "        self.grid = [[0] * GRID_WIDTH for _ in range(GRID_HEIGHT)]\n", "\n", "    def draw(self, screen):\n", "        for i, row in enumerate(self.grid):\n", "            for j, cell in enumerate(row):\n", "                if cell:\n", "                    pygame.draw.rect(screen, COLORS[cell - 1],\n", "                                     (j * BLOCK_SIZE, i * BLOCK_SIZE,\n", "                                      BLOCK_SIZE, BLOCK_SIZE))\n", "\n", "    def fix_piece(self, piece):\n", "        # 使用piece.type而不是SHAPES.index(piece.shape)\n", "        for i, row in enumerate(piece.shape):\n", "            for j, cell in enumerate(row):\n", "                if cell:\n", "                    self.grid[piece.y + i][piece.x + j] = piece.type + 1\n", "\n", "    def check_collision(self, piece, dx=0, dy=0):\n", "        for i, row in enumerate(piece.shape):\n", "            for j, cell in enumerate(row):\n", "                if cell:\n", "                    x = piece.x + j + dx\n", "                    y = piece.y + i + dy\n", "                    if x < 0 or x >= GRID_WIDTH or y >= GRID_HEIGHT or (y >= 0 and self.grid[y][x]):\n", "                        return True\n", "        return False\n", "\n", "    def remove_full_lines(self):\n", "        new_grid = [row for row in self.grid if any(cell == 0 for cell in row)]\n", "        lines_removed = GRID_HEIGHT - len(new_grid)\n", "        new_grid = [[0] * GRID_WIDTH for _ in range(lines_removed)] + new_grid\n", "        self.grid = new_grid\n", "        return lines_removed\n", "\n", "# 主游戏循环\n", "def main():\n", "    board = Board()\n", "    shape_idx = random.randint(0, len(SHAPES) - 1)\n", "    current_piece = Piece(SHAPES[shape_idx], shape_idx)\n", "    game_over = False\n", "    fall_time = 0\n", "    fall_speed = 0.5\n", "\n", "    while not game_over:\n", "        for event in pygame.event.get():\n", "            if event.type == pygame.QUIT:\n", "                game_over = True\n", "            elif event.type == pygame.KEYDOWN:\n", "                if event.key == pygame.K_LEFT:\n", "                    if not board.check_collision(current_piece, dx=-1):\n", "                        current_piece.x -= 1\n", "                elif event.key == pygame.K_RIGHT:\n", "                    if not board.check_collision(current_piece, dx=1):\n", "                        current_piece.x += 1\n", "                elif event.key == pygame.K_DOWN:\n", "                    if not board.check_collision(current_piece, dy=1):\n", "                        current_piece.y += 1\n", "                elif event.key == pygame.K_UP:\n", "                    original_shape = [row[:] for row in current_piece.shape]\n", "                    current_piece.rotate()\n", "                    if board.check_collision(current_piece):\n", "                        current_piece.shape = original_shape\n", "\n", "        fall_time += clock.get_rawtime()\n", "        if fall_time / 1000 >= fall_speed:\n", "            fall_time = 0\n", "            if not board.check_collision(current_piece, dy=1):\n", "                current_piece.y += 1\n", "            else:\n", "                board.fix_piece(current_piece)\n", "                board.remove_full_lines()\n", "                shape_idx = random.randint(0, len(SHAPES) - 1)\n", "                current_piece = Piece(SHAPES[shape_idx], shape_idx)\n", "                if board.check_collision(current_piece):\n", "                    game_over = True\n", "\n", "        screen.fill(BLACK)\n", "        board.draw(screen)\n", "        current_piece.draw(screen)\n", "        pygame.display.flip()\n", "        clock.tick(60)\n", "\n", "    pygame.quit()\n", "\n", "if __name__ == \"__main__\":\n", "    main()"], "id": "5dec5789a649e7ca", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["pygame 2.6.1 (SDL 2.28.4, Python 3.10.9)\n", "Hello from the pygame community. https://www.pygame.org/contribute.html\n"]}], "execution_count": 1}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.6"}}, "nbformat": 4, "nbformat_minor": 5}