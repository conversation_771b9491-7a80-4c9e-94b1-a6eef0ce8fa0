import React, { useState, useEffect } from 'react';
import {
  BarChart,
  Bar,
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  <PERSON><PERSON>hart,
  Pie,
  Cell
} from 'recharts';
import {
  Globe,
  Box,
  ShoppingCart,
  Truck,
  CreditCard,
  Users,
  TrendingUp,
  FileText,
  Map,
  Flag,
  DollarSign,
  Package,
  Database,
  Layers,
  BarChart2,
  LineChart as LineChartIcon
} from 'lucide-react';
import { motion } from 'framer-motion';
import Mermaid from 'mermaid';

const CrossBorderEcommerceReport = () => {
  const [activeTab, setActiveTab] = useState('overview');
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  // 市场规模数据
  const marketSizeData = [
    { year: '2019', value: 10.5 },
    { year: '2020', value: 12.5 },
    { year: '2021', value: 14.2 },
    { year: '2022', value: 15.7 },
    { year: '2023', value: 16.85 },
    { year: '2024', value: 18.5 }
  ];

  // 进出口结构数据
  const importExportData = [
    { name: '出口', value: 77.6 },
    { name: '进口', value: 22.4 }
  ];

  // 品类增长数据
  const categoryData = [
    { name: '3C', value: 35 },
    { name: '美妆', value: 25 },
    { name: '服饰', value: 20 },
    { name: '家居', value: 15 },
    { name: '宠物', value: 5 }
  ];

  // 区域市场数据
  const regionData = [
    { name: '北美', value: 35 },
    { name: '欧洲', value: 30 },
    { name: '东南亚', value: 20 },
    { name: '拉美', value: 10 },
    { name: '中东', value: 5 }
  ];

  // 物流方式数据
  const logisticsData = [
    { name: '海运', cost: 0.5, time: 30 },
    { name: '空运', cost: 3, time: 5 },
    { name: '铁路', cost: 1.5, time: 15 }
  ];

  useEffect(() => {
    Mermaid.contentLoaded();
  }, []);

  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8'];

  return (
    <div className="min-h-screen bg-gray-50 text-gray-800 font-sans">
      {/* 导航栏 */}
      <nav className="bg-white shadow-sm sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex items-center">
              <Globe className="h-8 w-8 text-blue-600" />
              <span className="ml-2 text-xl font-bold text-gray-900">
                中国跨境电商发展报告(2019-2024)
              </span>
            </div>
            <div className="hidden md:flex items-center space-x-8">
              {[
                { id: 'overview', label: '概览', icon: <Globe size={18} /> },
                { id: 'market', label: '市场规模', icon: <BarChart2 size={18} /> },
                { id: 'industry', label: '产业链', icon: <Layers size={18} /> },
                { id: 'logistics', label: '物流支付', icon: <Truck size={18} /> },
                { id: 'competition', label: '竞争格局', icon: <Users size={18} /> },
                { id: 'trends', label: '未来趋势', icon: <TrendingUp size={18} /> }
              ].map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center px-1 pt-1 border-b-2 text-sm font-medium ${
                    activeTab === tab.id
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <span className="mr-1">{tab.icon}</span>
                  {tab.label}
                </button>
              ))}
            </div>
            <div className="md:hidden flex items-center">
              <button
                onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
                className="inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none"
              >
                <svg
                  className="h-6 w-6"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d={isMobileMenuOpen ? 'M6 18L18 6M6 6l12 12' : 'M4 6h16M4 12h16M4 18h16'}
                  />
                </svg>
              </button>
            </div>
          </div>
        </div>

        {/* 移动端菜单 */}
        {isMobileMenuOpen && (
          <div className="md:hidden bg-white shadow-lg">
            <div className="px-2 pt-2 pb-3 space-y-1">
              {[
                { id: 'overview', label: '概览', icon: <Globe size={18} /> },
                { id: 'market', label: '市场规模', icon: <BarChart2 size={18} /> },
                { id: 'industry', label: '产业链', icon: <Layers size={18} /> },
                { id: 'logistics', label: '物流支付', icon: <Truck size={18} /> },
                { id: 'competition', label: '竞争格局', icon: <Users size={18} /> },
                { id: 'trends', label: '未来趋势', icon: <TrendingUp size={18} /> }
              ].map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => {
                    setActiveTab(tab.id);
                    setIsMobileMenuOpen(false);
                  }}
                  className={`flex items-center w-full px-3 py-2 rounded-md text-base font-medium ${
                    activeTab === tab.id
                      ? 'bg-blue-50 text-blue-700'
                      : 'text-gray-600 hover:text-gray-800 hover:bg-gray-50'
                  }`}
                >
                  <span className="mr-2">{tab.icon}</span>
                  {tab.label}
                </button>
              ))}
            </div>
          </div>
        )}
      </nav>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* 概览部分 */}
        {activeTab === 'overview' && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5 }}
          >
            <div className="bg-white shadow rounded-lg p-6 mb-8">
              <h2 className="text-2xl font-bold flex items-center mb-6">
                <Globe className="mr-2 text-blue-600" />
                研究概览
              </h2>
              <div className="grid md:grid-cols-2 gap-8">
                <div>
                  <h3 className="text-lg font-semibold mb-4 flex items-center">
                    <FileText className="mr-2 text-blue-500" />
                    研究目的与意义
                  </h3>
                  <p className="text-gray-600 mb-4">
                    在全球化经济加速发展和互联网技术不断进步的背景下，跨境电商行业迅速崛起，成为国际贸易的重要组成部分。我国跨境电商作为现代外贸的新锐力量，正日益成为推动全球贸易发展的重要引擎。
                  </p>
                  <p className="text-gray-600">
                    从宏观经济层面来看，跨境电商的发展有助于我国外贸结构优化、规模稳定，是我国经济"新动能"的一部分。它能够促进我国制造业与全球市场的深度融合，推动更多具有地方特色的产品融入全球价值链，提升我国在全球产业链中的地位。
                  </p>
                </div>
                <div>
                  <h3 className="text-lg font-semibold mb-4 flex items-center">
                    <Database className="mr-2 text-blue-500" />
                    研究方法与数据来源
                  </h3>
                  <p className="text-gray-600 mb-4">
                    本研究综合运用了多种研究方法，包括文献研究法、数据分析方法和案例分析法等。数据来源主要包括国家统计局、中国海关、行业协会、上市公司公开报告以及相关的研究机构发布的报告等。
                  </p>
                  <div className="bg-blue-50 p-4 rounded-lg">
                    <h4 className="font-medium text-blue-800 mb-2">时间范围界定</h4>
                    <p className="text-blue-700">
                      本研究的时间范围界定为2019 - 2024年。这一时间段内，我国跨境电商经历了诸多重要的发展阶段和变化，包括市场规模的快速增长、政策环境的不断优化、技术创新的持续推进以及外部环境的复杂变化等。
                    </p>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-white shadow rounded-lg p-6 mb-8">
              <h2 className="text-2xl font-bold flex items-center mb-6">
                <Map className="mr-2 text-blue-600" />
                宏观环境分析
              </h2>
              <div className="grid md:grid-cols-2 gap-8">
                <div>
                  <h3 className="text-lg font-semibold mb-4 flex items-center">
                    <Globe className="mr-2 text-blue-500" />
                    全球与中国宏观经济环境
                  </h3>
                  <p className="text-gray-600 mb-4">
                    2022年全球GDP总量达到89.7万亿美元，同比增速放缓至3.1%。2022年全球进出口贸易总额达到50.5万亿美元，增速放缓至12.5%。全球跨境电商交易额从2019年的约2.2万亿美元增长至2023年的约2.8万亿美元，同比增长16.7%。
                  </p>
                  <p className="text-gray-600">
                    中国经济在2019 - 2024年期间总体保持了稳健的发展态势。2020年中国生产总值超过100万亿元，居民人均可支配收入持续增长，由2013年的1.83万元增加至2020年的3.22万元。
                  </p>
                </div>
                <div>
                  <h3 className="text-lg font-semibold mb-4 flex items-center">
                    <Flag className="mr-2 text-blue-500" />
                    政策与法规环境
                  </h3>
                  <ul className="space-y-3 text-gray-600">
                    <li className="flex items-start">
                      <span className="inline-flex items-center justify-center h-6 w-6 rounded-full bg-blue-100 text-blue-800 mr-2">
                        1
                      </span>
                      海关监管与跨境电商零售进口政策演进（"正面清单"更新、B2B出口监管改革等）
                    </li>
                    <li className="flex items-start">
                      <span className="inline-flex items-center justify-center h-6 w-6 rounded-full bg-blue-100 text-blue-800 mr-2">
                        2
                      </span>
                      跨境电商综试区政策与税收优惠（全国已设立165个跨境电商综合试验区）
                    </li>
                    <li className="flex items-start">
                      <span className="inline-flex items-center justify-center h-6 w-6 rounded-full bg-blue-100 text-blue-800 mr-2">
                        3
                      </span>
                      数据合规、平台合规与海外市场准入政策
                    </li>
                  </ul>
                </div>
              </div>
            </div>

            <div className="bg-white shadow rounded-lg p-6">
              <h2 className="text-2xl font-bold flex items-center mb-6">
                <TrendingUp className="mr-2 text-blue-600" />
                技术趋势与创新模式
              </h2>
              <div className="grid md:grid-cols-3 gap-6">
                <div className="bg-blue-50 p-4 rounded-lg">
                  <h3 className="font-medium text-blue-800 mb-2 flex items-center">
                    <Box className="mr-2" />
                    AI选品与智能定价
                  </h3>
                  <p className="text-blue-700">
                    AI驱动的选品工具通过大数据分析、机器学习和自然语言处理等技术，能够精准预测市场需求、优化库存管理并分析竞争环境。
                  </p>
                </div>
                <div className="bg-green-50 p-4 rounded-lg">
                  <h3 className="font-medium text-green-800 mb-2 flex items-center">
                    <Layers className="mr-2" />
                    跨境ERP与数字孪生供应链
                  </h3>
                  <p className="text-green-700">
                    跨境电商ERP集成了WMS、CRM等模块，能够帮助卖家轻松对采购、订单、发货、仓储、售后、统计等电商运营关键环节进行一站式管理。
                  </p>
                </div>
                <div className="bg-purple-50 p-4 rounded-lg">
                  <h3 className="font-medium text-purple-800 mb-2 flex items-center">
                    <CreditCard className="mr-2" />
                    Web3/区块链在溯源、支付中的探索
                  </h3>
                  <p className="text-purple-700">
                    区块链技术在跨境电商商品溯源方面具有重要应用价值，可以确保信息相互留存、相互印证、不可篡改，实现信息流、物流、资金流、关务流的全链条闭环互通互认。
                  </p>
                </div>
              </div>
            </div>
          </motion.div>
        )}

        {/* 市场规模部分 */}
        {activeTab === 'market' && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5 }}
          >
            <div className="bg-white shadow rounded-lg p-6 mb-8">
              <h2 className="text-2xl font-bold flex items-center mb-6">
                <BarChart2 className="mr-2 text-blue-600" />
                市场规模与增长趋势
              </h2>
              <div className="grid md:grid-cols-2 gap-8">
                <div>
                  <h3 className="text-lg font-semibold mb-4">市场交易规模（万亿元）</h3>
                  <div className="h-64">
                    <ResponsiveContainer width="100%" height="100%">
                      <LineChart data={marketSizeData}>
                        <CartesianGrid strokeDasharray="3 3" stroke="#e5e7eb" />
                        <XAxis dataKey="year" />
                        <YAxis />
                        <Tooltip />
                        <Legend />
                        <Line
                          type="monotone"
                          dataKey="value"
                          stroke="#3B82F6"
                          strokeWidth={2}
                          activeDot={{ r: 8 }}
                          name="市场规模"
                        />
                      </LineChart>
                    </ResponsiveContainer>
                  </div>
                  <table className="w-full mt-4">
                    <thead>
                      <tr className="bg-gray-50">
                        <th className="px-4 py-2 text-left text-sm font-medium text-gray-500">年份</th>
                        <th className="px-4 py-2 text-left text-sm font-medium text-gray-500">市场规模</th>
                        <th className="px-4 py-2 text-left text-sm font-medium text-gray-500">增速</th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-200">
                      {marketSizeData?.map((item, index) => (
                        <tr key={index}>
                          <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-500">{item?.year}</td>
                          <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-500">{item?.value}万亿</td>
                          <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-500">
                            {index > 0 ? `${(((item?.value - marketSizeData?.[index - 1]?.value) / marketSizeData?.[index - 1]?.value) * 100)?.toFixed(1)}%` : '-'}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
                <div>
                  <h3 className="text-lg font-semibold mb-4">进出口结构占比变化</h3>
                  <div className="h-64">
                    <ResponsiveContainer width="100%" height="100%">
                      <PieChart>
                        <Pie
                          data={importExportData}
                          cx="50%"
                          cy="50%"
                          labelLine={false}
                          outerRadius={80}
                          fill="#8884d8"
                          dataKey="value"
                          label={({ name, percent }) => `${name} ${(percent * 100)?.toFixed(0)}%`}
                        >
                          {importExportData?.map((entry, index) => (
                            <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                          ))}
                        </Pie>
                        <Tooltip />
                      </PieChart>
                    </ResponsiveContainer>
                  </div>
                  <div className="mt-4">
                    <h4 className="text-sm font-medium text-gray-500 mb-2">行业渗透率</h4>
                    <div className="bg-gray-100 rounded-full h-4">
                      <div
                        className="bg-blue-500 h-4 rounded-full"
                        style={{ width: '40.35%' }}
                      ></div>
                    </div>
                    <div className="flex justify-between text-xs text-gray-500 mt-1">
                      <span>2019: 33.29%</span>
                      <span>2024: 40.35%</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-white shadow rounded-lg p-6 mb-8">
              <h2 className="text-2xl font-bold flex items-center mb-6">
                <Package className="mr-2 text-blue-600" />
                主要品类增长亮点
              </h2>
              <div className="grid md:grid-cols-2 gap-8">
                <div>
                  <h3 className="text-lg font-semibold mb-4">品类占比</h3>
                  <div className="h-64">
                    <ResponsiveContainer width="100%" height="100%">
                      <PieChart>
                        <Pie
                          data={categoryData}
                          cx="50%"
                          cy="50%"
                          labelLine={false}
                          outerRadius={80}
                          fill="#8884d8"
                          dataKey="value"
                          label={({ name, percent }) => `${name} ${(percent * 100)?.toFixed(0)}%`}
                        >
                          {categoryData?.map((entry, index) => (
                            <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                          ))}
                        </Pie>
                        <Tooltip />
                        <Legend />
                      </PieChart>
                    </ResponsiveContainer>
                  </div>
                </div>
                <div>
                  <h3 className="text-lg font-semibold mb-4">品类特点</h3>
                  <ul className="space-y-4">
                    <li className="p-3 bg-blue-50 rounded-lg">
                      <h4 className="font-medium text-blue-800 mb-1">3C品类</h4>
                      <p className="text-blue-700 text-sm">
                        我国在电子制造领域具有强大的产业基础和技术优势，产品质量和性价比高，在全球市场上具有较强的竞争力。
                      </p>
                    </li>
                    <li className="p-3 bg-purple-50 rounded-lg">
                      <h4 className="font-medium text-purple-800 mb-1">美妆品类</h4>
                      <p className="text-purple-700 text-sm">
                        随着全球消费者对美的追求和消费观念的转变，美妆产品的市场需求不断增加。我国美妆产业近年来发展迅速，一些本土品牌逐渐崛起。
                      </p>
                    </li>
                    <li className="p-3 bg-green-50 rounded-lg">
                      <h4 className="font-medium text-green-800 mb-1">服饰品类</h4>
                      <p className="text-green-700 text-sm">
                        我国是全球最大的服装生产和出口国，拥有丰富的服装产业资源和成熟的供应链体系。我国的服饰产品款式多样、价格实惠。
                      </p>
                    </li>
                  </ul>
                </div>
              </div>
            </div>

            <div className="bg-white shadow rounded-lg p-6">
              <h2 className="text-2xl font-bold flex items-center mb-6">
                <Globe className="mr-2 text-blue-600" />
                区域市场分析
              </h2>
              <div className="h-96">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={regionData}>
                    <CartesianGrid strokeDasharray="3 3" stroke="#e5e7eb" />
                    <XAxis dataKey="name" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Bar dataKey="value" fill="#3B82F6" name="市场份额(%)" />
                  </BarChart>
                </ResponsiveContainer>
              </div>
            </div>
          </motion.div>
        )}

        {/* 产业链部分 */}
        {activeTab === 'industry' && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5 }}
          >
            <div className="bg-white shadow rounded-lg p-6 mb-8">
              <h2 className="text-2xl font-bold flex items-center mb-6">
                <Layers className="mr-2 text-blue-600" />
                产业链与价值链解析
              </h2>
              
              <div className="mb-8">
                <pre className="mermaid">
                  {`
                    graph TD
                      A[上游] -->|制造端与供应链数字化| B[中游]
                      A -->|供应链金融与SaaS赋能| B
                      B -->|平台型电商| C[下游]
                      B -->|独立站| C
                      B -->|服务商生态| C
                      C -->|海外消费者| D[市场需求]
                      C -->|本地化运营| D
                      C -->|社交电商模式| D
                  `}
                </pre>
              </div>

              <div className="grid md:grid-cols-3 gap-6">
                <div className="bg-blue-50 p-4 rounded-lg">
                  <h3 className="font-medium text-blue-800 mb-2 flex items-center">
                    <Box className="mr-2" />
                    上游
                  </h3>
                  <ul className="space-y-2 text-blue-700">
                    <li>制造端与供应链数字化</li>
                    <li>供应链金融与SaaS赋能</li>
                    <li>2023年供应链数字化服务收入约3.6万亿元</li>
                  </ul>
                </div>
                <div className="bg-green-50 p-4 rounded-lg">
                  <h3 className="font-medium text-green-800 mb-2 flex items-center">
                    <Layers className="mr-2" />
                    中游
                  </h3>
                  <ul className="space-y-2 text-green-700">
                    <li>平台型电商 vs. 独立站</li>
                    <li>服务商生态：运营代投、MCN、ERP、支付、物流、海外仓</li>
                    <li>2024年海外仓数量达到2500+</li>
                  </ul>
                </div>
                <div className="bg-purple-50 p-4 rounded-lg">
                  <h3 className="font-medium text-purple-800 mb-2 flex items-center">
                    <Users className="mr-2" />
                    下游
                  </h3>
                  <ul className="space-y-2 text-purple-700">
                    <li>海外消费者画像与需求变化</li>
                    <li>本地化运营、KOL/KOC及社交电商模式</li>
                    <li>跨境电商用户已达1.63亿</li>
                  </ul>
                </div>
              </div>
            </div>

            <div className="bg-white shadow rounded-lg p-6">
              <h2 className="text-2xl font-bold flex items-center mb-6">
                <ShoppingCart className="mr-2 text-blue-600" />
                平台类型对比
              </h2>
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">平台类型</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">代表平台</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">优势</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">劣势</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    <tr>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">平台型电商</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">阿里巴巴国际站、Temu、速卖通</td>
                      <td className="px-6 py-4 text-sm text-gray-500">流量大、用户多、知名度高</td>
                      <td className="px-6 py-4 text-sm text-gray-500">竞争激烈，受平台规则约束</td>
                    </tr>
                    <tr>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">独立站</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Shopify、Shoplazza</td>
                      <td className="px-6 py-4 text-sm text-gray-500">自主性强、品牌塑造能力强</td>
                      <td className="px-6 py-4 text-sm text-gray-500">运营成本高，需自行引流</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </motion.div>
        )}

        {/* 物流支付部分 */}
        {activeTab === 'logistics' && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5 }}
          >
            <div className="bg-white shadow rounded-lg p-6 mb-8">
              <h2 className="text-2xl font-bold flex items-center mb-6">
                <Truck className="mr-2 text-blue-600" />
                跨境物流分析
              </h2>
              <div className="grid md:grid-cols-2 gap-8">
                <div>
                  <h3 className="text-lg font-semibold mb-4">物流方式比较</h3>
                  <div className="h-64">
                    <ResponsiveContainer width="100%" height="100%">
                      <BarChart data={logisticsData}>
                        <CartesianGrid strokeDasharray="3 3" stroke="#e5e7eb" />
                        <XAxis dataKey="name" />
                        <YAxis yAxisId="left" orientation="left" stroke="#3B82F6" />
                        <YAxis yAxisId="right" orientation="right" stroke="#10B981" />
                        <Tooltip />
                        <Legend />
                        <Bar yAxisId="left" dataKey="cost" fill="#3B82F6" name="成本(美元/kg)" />
                        <Bar yAxisId="right" dataKey="time" fill="#10B981" name="时间(天)" />
                      </BarChart>
                    </ResponsiveContainer>
                  </div>
                </div>
                <div>
                  <h3 className="text-lg font-semibold mb-4">海外仓布局</h3>
                  <div className="bg-blue-50 p-4 rounded-lg mb-4">
                    <div className="flex justify-between items-center mb-2">
                      <span className="font-medium text-blue-800">海外仓数量</span>
                      <span className="text-blue-700 font-bold">2500+</span>
                    </div>
                    <div className="flex justify-between items-center mb-2">
                      <span className="font-medium text-blue-800">海外仓面积</span>
                      <span className="text-blue-700 font-bold">3000万+平方米</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="font-medium text-blue-800">专注跨境电商的海外仓</span>
                      <span className="text-blue-700 font-bold">1800+</span>
                    </div>
                  </div>
                  <div className="bg-green-50 p-4 rounded-lg">
                    <h4 className="font-medium text-green-800 mb-2">末端履约方案</h4>
                    <ul className="text-green-700 space-y-1">
                      <li>与当地物流服务商合作</li>
                      <li>智能物流技术应用</li>
                      <li>本土退货方案</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-white shadow rounded-lg p-6">
              <h2 className="text-2xl font-bold flex items-center mb-6">
                <CreditCard className="mr-2 text-blue-600" />
                跨境支付分析
              </h2>
              <div className="grid md:grid-cols-2 gap-8">
                <div>
                  <h3 className="text-lg font-semibold mb-4">主流支付渠道费率比较</h3>
                  <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">支付渠道</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">费率范围</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">特点</th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        <tr>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">PayPal</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2.9% - 3.9% + 固定费用</td>
                          <td className="px-6 py-4 text-sm text-gray-500">全球知名，安全性高</td>
                        </tr>
                        <tr>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">当地电子钱包</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">1.5% - 2.5%</td>
                          <td className="px-6 py-4 text-sm text-gray-500">本地化体验好</td>
                        </tr>
                        <tr>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">银行卡清算</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">1.8% - 2.5%</td>
                          <td className="px-6 py-4 text-sm text-gray-500">传统方式，接受度高</td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>
                <div>
                  <h3 className="text-lg font-semibold mb-4">外汇收结汇与风控痛点</h3>
                  <div className="space-y-4">
                    <div className="p-4 bg-red-50 rounded-lg">
                      <h4 className="font-medium text-red-800 mb-1">汇率风险</h4>
                      <p className="text-red-700 text-sm">
                        由于不同国家和地区的货币汇率波动较大，企业在进行外汇收结汇时可能会面临汇率风险。
                      </p>
                    </div>
                    <div className="p-4 bg-yellow-50 rounded-lg">
                      <h4 className="font-medium text-yellow-800 mb-1">反洗钱与反欺诈</h4>
                      <p className="text-yellow-700 text-sm">
                        跨境支付还面临着反洗钱、反欺诈等风控痛点。支付机构需要加强对交易的监测和审核。
                      </div>
                    <div className="p-4 bg-purple-50 rounded-lg">
                      <h4 className="font-medium text-purple-800 mb-1">监管合规</h4>
                      <p className="text-purple-700 text-sm">
                        监管部门加强了对跨境支付的监管，要求支付机构严格遵守相关法律法规，确保支付安全。
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        )}

        {/* 竞争格局部分 */}
        {activeTab === 'competition' && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5 }}
          >
            <div className="bg-white shadow rounded-lg p-6 mb-8">
              <h2 className="text-2xl font-bold flex items-center mb-6">
                <Users className="mr-2 text-blue-600" />
                竞争格局与企业案例
              </h2>
              
              <div className="mb-8">
                <h3 className="text-lg font-semibold mb-4">主流平台战略对比</h3>
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">平台</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">战略重点</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">优势</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">挑战</th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      <tr>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">亚马逊</td>
                        <td className="px-6 py-4 text-sm text-gray-500">用户体验优化，技术创新</td>
                        <td className="px-6 py-4 text-sm text-gray-500">庞大用户基础，完善物流</td>
                        <td className="px-6 py-4 text-sm text-gray-500">竞争激烈，平台规则严格</td>
                      </tr>
                      <tr>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">Temu</td>
                        <td className="px-6 py-4 text-sm text-gray-500">极致性价比，社交营销</td>
                        <td className="px-6 py-4 text-sm text-gray-500">低价策略，供应链整合</td>
                        <td className="px-6 py-4 text-sm text-gray-500">商品质量，政策合规</td>
                      </tr>
                      <tr>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">SHEIN</td>
                        <td className="px-6 py-4 text-sm text-gray-500">快时尚，快速响应供应链</td>
                        <td className="px-6 py-4 text-sm text-gray-500">大数据分析，精准定位</td>
                        <td className="px-6 py-4 text-sm text-gray-500">环保与可持续发展</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>

              <div className="mb-8">
                <h3 className="text-lg font-semibold mb-4">卖家类型比较</h3>
                <div className="grid md:grid-cols-3 gap-6">
                  <div className="bg-blue-50 p-4 rounded-lg">
                    <h4 className="font-medium text-blue-800 mb-2">工厂型（深圳）</h4>
                    <p className="text-blue-700 text-sm">
                      具有强大的生产能力和供应链优势，能够提供高质量、低成本的产品。
                    </p>
                  </div>
                  <div className="bg-green-50 p-4 rounded-lg">
                    <h4 className="font-medium text-green-800 mb-2">贸易型（义乌）</h4>
                    <p className="text-green-700 text-sm">
                      主要从事商品的采购和销售，具有丰富的商品资源和销售渠道。
                    </p>
                  </div>
                  <div className="bg-purple-50 p-4 rounded-lg">
                    <h4 className="font-medium text-purple-800 mb-2">品牌型（安克）</h4>
                    <p className="text-purple-700 text-sm">
                      注重品牌建设和产品创新，以品牌为核心开展业务。
                    </p>
                  </div>
                </div>
              </div>

              <div>
                <h3 className="text-lg font-semibold mb-4">典型企业案例</h3>
                <div className="space-y-6">
                  <div className="p-4 bg-white border border-gray-200 rounded-lg shadow-sm">
                    <h4 className="font-bold text-lg text-gray-900 mb-2">SHEIN</h4>
                    <p className="text-gray-600 mb-3">
                      SHEIN成立于2008年，最初以婚纱销售为主，后来逐渐转型为快时尚女装品牌。经过多年的发展，SHEIN已经成为全球知名的跨境电商品牌。
                    </p>
                    <div className="flex flex-wrap gap-2">
                      <span className="px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded-full">快速响应供应链</span>
                      <span className="px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full">大数据分析</span>
                      <span className="px-2 py-1 text-xs font-medium bg-purple-100 text-purple-800 rounded-full">社交营销</span>
                    </div>
                  </div>
                  <div className="p-4 bg-white border border-gray-200 rounded-lg shadow-sm">
                    <h4 className="font-bold text-lg text-gray-900 mb-2">Anker</h4>
                    <p className="text-gray-600 mb-3">
                      安克创新科技股份有限公司成立于2011年，是一家专注于智能充电、无线音频等领域的跨境电商品牌。
                    </p>
                    <div className="flex flex-wrap gap-2">
                      <span className="px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded-full">研发能力</span>
                      <span className="px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full">品牌建设</span>
                      <span className="px-2 py-1 text-xs font-medium bg-purple-100 text-purple-800 rounded-full">全球渠道</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        )}

        {/* 未来趋势部分 */}
        {activeTab === 'trends' && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5 }}
          >
            <div className="bg-white shadow rounded-lg p-6 mb-8">
              <h2 className="text-2xl font-bold flex items-center mb-6">
                <TrendingUp className="mr-2 text-blue-600" />
                机遇、挑战与风险
              </h2>
              
              <div className="grid md:grid-cols-3 gap-6 mb-8">
                <div className="bg-green-50 p-4 rounded-lg">
                  <h3 className="font-medium text-green-800 mb-2 flex items-center">
                    <Globe className="mr-2" />
                    机遇
                  </h3>
                  <ul className="text-green-700 space-y-2">
                    <li>全球线上化加速</li>
                    <li>政策扶持（165个跨境电商综试区）</li>
                    <li>供应链优势</li>
                  </ul>
                </div>
                <div className="bg-yellow-50 p-4 rounded-lg">
                  <h3 className="font-medium text-yellow-800 mb-2 flex items-center">
                    <FileText className="mr-2" />
                    挑战
                  </h3>
                  <ul className="text-yellow-700 space-y-2">
                    <li>合规成本上升</li>
                    <li>物流瓶颈</li>
                    <li>同质化竞争</li>
                  </ul>
                </div>
                <div className="bg-red-50 p-4 rounded-lg">
                  <h3 className="font-medium text-red-800 mb-2 flex items-center">
                    <Flag className="mr-2" />
                    风险
                  </h3>
                  <ul className="text-red-700 space-y-2">
                    <li>汇率波动</li>
                    <li>贸易摩擦</li>
                    <li>平台政策调整</li>
                  </ul>
                </div>
              </div>

              <div className="mb-8">
                <h3 className="text-lg font-semibold mb-4">ESG与可持续发展要求</h3>
                <div className="bg-blue-50 p-4 rounded-lg">
                  <p className="text-blue-700">
                    全球跨境电商行业正经历从高速增长向高质量发展转型的关键期，ESG（环境、社会、治理）理念已渗透至产业链每个环节。2024年数据显示，中国跨境电商进出口额达2.63万亿元，同比增长10.8%，但伴随规模扩张的碳足迹同步攀升，单件跨境包裹平均碳排放量达2.1千克。
                  </p>
                </div>
              </div>

              <div>
                <h3 className="text-lg font-semibold mb-4">未来发展趋势与策略建议</h3>
                <div className="space-y-4">
                  <div className="p-4 bg-white border border-gray-200 rounded-lg shadow-sm">
                    <h4 className="font-bold text-gray-900 mb-2">行业未来3-5年发展预测</h4>
                    <p className="text-gray-600">
                      预计到2025年，全球跨境电商市场规模将突破15万亿美元。东南亚、中东、拉美等新兴市场成为增长主力，其中泰国电商规模预计2027年达1.6万亿泰铢，墨西哥、巴西等站点依托人口红利与数字基建加速崛起。
                    </p>
                  </div>
                  <div className="p-4 bg-white border border-gray-200 rounded-lg shadow-sm">
                    <h4 className="font-bold text-gray-900 mb-2">企业出海战略路径</h4>
                    <ul className="text-gray-600 list-disc pl-5 space-y-1">
                      <li>多平台布局，降低单一平台依赖风险</li>
                      <li>多渠道营销，结合社交电商新模式</li>
                      <li>深度本土化运营，贴近目标市场需求</li>
                    </ul>
                  </div>
                  <div className="p-4 bg-white border border-gray-200 rounded-lg shadow-sm">
                    <h4 className="font-bold text-gray-900 mb-2">政策与行业协同建议</h4>
                    <p className="text-gray-600">
                      加强与国际组织的合作，推动跨境电商国际规则制定；完善跨境电商统计监测体系；支持跨境电商服务体系建设，包括物流、支付、售后等环节。
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        )}
      </main>

      <footer className="bg-gray-100 border-t border-gray-200 py-4">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center text-sm text-gray-500">
          <p>created by <a href="https://space.coze.cn" className="text-blue-600 hover:text-blue-800">coze space</a></p>
          <p>页面内容均由 AI 生成，仅供参考</p>
        </div>
      </footer>
    </div>
  );
};

export default CrossBorderEcommerceReport;