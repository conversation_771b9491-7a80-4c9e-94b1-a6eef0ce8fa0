<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="88bb9d62-68c7-4ab5-b910-4e378a0f0cae" name="更改" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="HTML File" />
        <option value="Python Script" />
        <option value="Jupyter Notebook" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/ecommerce_analysis/frontend" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 3
}</component>
  <component name="ProjectId" id="2sh0C4ww2p6dguogUOTyfVyaiYR" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;DefaultHtmlFileTemplate&quot;: &quot;HTML File&quot;,
    &quot;Node.js.中国跨境电商2019至2024年发展状况深度解析及展望.jsx.executor&quot;: &quot;Run&quot;,
    &quot;Python.app (1).executor&quot;: &quot;Run&quot;,
    &quot;Python.app.executor&quot;: &quot;Run&quot;,
    &quot;Python.app0 (1).executor&quot;: &quot;Run&quot;,
    &quot;Python.bailian_service.executor&quot;: &quot;Run&quot;,
    &quot;Python.bailian_test.executor&quot;: &quot;Run&quot;,
    &quot;Python.deepseek-qwen.executor&quot;: &quot;Run&quot;,
    &quot;Python.langchain.executor&quot;: &quot;Run&quot;,
    &quot;Python.langchain_app.executor&quot;: &quot;Run&quot;,
    &quot;Python.qwen-long.executor&quot;: &quot;Run&quot;,
    &quot;Python.qwen.executor&quot;: &quot;Run&quot;,
    &quot;Python.test.executor&quot;: &quot;Run&quot;,
    &quot;Python.video.executor&quot;: &quot;Run&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;git-widget-placeholder&quot;: &quot;master&quot;,
    &quot;ignore.virus.scanning.warn.message&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;D:/PythonProject/pythonProject&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;preferences.lookFeel&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  },
  &quot;keyToStringList&quot;: {
    &quot;DatabaseDriversLRU&quot;: [
      &quot;mysql&quot;
    ]
  }
}</component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="D:\PythonProject\pythonProject" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="D:\PythonProject\pythonProject" />
    </key>
  </component>
  <component name="RunAnythingCache">
    <myKeys>
      <visibility group="Grunt" flag="true" />
      <visibility group="Gulp" flag="true" />
      <visibility group="HTTP 请求" flag="true" />
      <visibility group="Node.js" flag="true" />
      <visibility group="npm" flag="true" />
      <visibility group="yarn" flag="true" />
      <visibility group="最近的项目" flag="true" />
      <visibility group="运行 Python 文件" flag="true" />
      <visibility group="运行 conda 命令" flag="true" />
      <visibility group="运行 pip 命令" flag="true" />
      <visibility group="运行配置" flag="true" />
    </myKeys>
  </component>
  <component name="RunManager" selected="Python.video">
    <configuration name="中国跨境电商2019至2024年发展状况深度解析及展望.jsx" type="NodeJSConfigurationType" temporary="true" nameIsGenerated="true" path-to-js-file="$PROJECT_DIR$/中国跨境电商2019至2024年发展状况深度解析及展望.jsx" working-dir="$PROJECT_DIR$">
      <method v="2" />
    </configuration>
    <configuration name="app" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="pythonProject" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/app.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="bailian_test" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="pythonProject" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/bailian_test.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="langchain" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="pythonProject" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/langchain.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="video" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="pythonProject" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/video.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Python.video" />
        <item itemvalue="Python.app" />
        <item itemvalue="Python.bailian_test" />
        <item itemvalue="Node.js.中国跨境电商2019至2024年发展状况深度解析及展望.jsx" />
        <item itemvalue="Python.langchain" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-1d06a55b98c1-74d2a5396914-JavaScript-PY-241.14494.241" />
        <option value="bundled-python-sdk-0509580d9d50-28c9f5db9ffe-com.jetbrains.pycharm.pro.sharedIndexes.bundled-PY-241.14494.241" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="88bb9d62-68c7-4ab5-b910-4e378a0f0cae" name="更改" comment="" />
      <created>1738892370308</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1738892370308</updated>
      <workItem from="1738892370310" duration="1474000" />
      <workItem from="1738893881874" duration="6149000" />
      <workItem from="1738909120702" duration="9512000" />
      <workItem from="1739151943375" duration="2227000" />
      <workItem from="1739154290141" duration="8965000" />
      <workItem from="1739171173577" duration="2186000" />
      <workItem from="1739239124464" duration="5531000" />
      <workItem from="1739331884965" duration="2000000" />
      <workItem from="1739409569114" duration="17169000" />
      <workItem from="1739495067635" duration="25616000" />
      <workItem from="1739754388809" duration="4281000" />
      <workItem from="1739758713395" duration="3275000" />
      <workItem from="1739762049899" duration="4399000" />
      <workItem from="1739771772056" duration="14325000" />
      <workItem from="1739840916607" duration="14528000" />
      <workItem from="1739928581449" duration="7117000" />
      <workItem from="1740031700952" duration="5633000" />
      <workItem from="1740039645276" duration="1538000" />
      <workItem from="1740044947049" duration="659000" />
      <workItem from="1740100636739" duration="22000" />
      <workItem from="1740470561945" duration="953000" />
      <workItem from="1740532698416" duration="3296000" />
      <workItem from="1740969673664" duration="8420000" />
      <workItem from="1741051604551" duration="21000" />
      <workItem from="1741072583466" duration="954000" />
      <workItem from="1741153842432" duration="6563000" />
      <workItem from="1741247013112" duration="22970000" />
      <workItem from="1741331131709" duration="7265000" />
      <workItem from="1741584924723" duration="82000" />
      <workItem from="1741590094526" duration="15000" />
      <workItem from="1741674356213" duration="9000" />
      <workItem from="1741675208484" duration="9961000" />
      <workItem from="1741742153237" duration="752000" />
      <workItem from="1741742931935" duration="921000" />
      <workItem from="1742021455527" duration="4631000" />
      <workItem from="1742043868566" duration="1081000" />
      <workItem from="1742529472640" duration="718000" />
      <workItem from="1742545298599" duration="911000" />
      <workItem from="1742782514440" duration="7592000" />
      <workItem from="1742866863092" duration="9103000" />
      <workItem from="1743043249191" duration="7522000" />
      <workItem from="1743143032323" duration="8741000" />
      <workItem from="1743226613426" duration="99000" />
      <workItem from="1743557273458" duration="3070000" />
      <workItem from="1743868432232" duration="12000" />
      <workItem from="1743868449891" duration="3000" />
      <workItem from="1743991231284" duration="7000" />
      <workItem from="1744080861537" duration="5458000" />
      <workItem from="1744165580786" duration="6000" />
      <workItem from="1744190884483" duration="1327000" />
      <workItem from="1744342581904" duration="12646000" />
      <workItem from="1744383753076" duration="34000" />
      <workItem from="1744383794300" duration="2573000" />
      <workItem from="1744386602547" duration="851000" />
      <workItem from="1744436930831" duration="1837000" />
      <workItem from="1744466867863" duration="910000" />
      <workItem from="1744467830061" duration="3385000" />
      <workItem from="1744476190336" duration="603000" />
      <workItem from="1744618285975" duration="2248000" />
      <workItem from="1744684359842" duration="5263000" />
      <workItem from="1744706623623" duration="3940000" />
      <workItem from="1745114510604" duration="941000" />
      <workItem from="1745294137496" duration="6827000" />
      <workItem from="1745372080567" duration="2718000" />
      <workItem from="1745375713667" duration="10014000" />
      <workItem from="1745480558457" duration="1342000" />
      <workItem from="1745721691767" duration="1582000" />
      <workItem from="1745723295547" duration="7991000" />
      <workItem from="1745742853755" duration="3113000" />
      <workItem from="1745841422317" duration="4553000" />
      <workItem from="1745897708212" duration="152000" />
      <workItem from="1745977483675" duration="1505000" />
      <workItem from="1746268039959" duration="22000" />
      <workItem from="1746495903124" duration="9320000" />
      <workItem from="1747708935693" duration="1364000" />
      <workItem from="1747726321371" duration="490000" />
      <workItem from="1748418229014" duration="15000" />
      <workItem from="1750324331636" duration="1429000" />
      <workItem from="1750382663580" duration="2980000" />
      <workItem from="1751523875049" duration="1766000" />
      <workItem from="1752461561990" duration="66000" />
      <workItem from="1752464016992" duration="4330000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="UnknownFeatures">
    <option featureType="com.intellij.fileTypeFactory" implementationName=".env" />
  </component>
  <component name="com.intellij.coverage.CoverageDataManagerImpl">
    <SUITE FILE_PATH="coverage/pythonProject$qwen.coverage" NAME="qwen 覆盖结果" MODIFIED="1738920646129" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/pythonProject$app__1_.coverage" NAME="app (1) 覆盖结果" MODIFIED="1739511611472" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/pythonProject$langchain_app.coverage" NAME="langchain_app 覆盖结果" MODIFIED="1741328414063" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/pythonProject$test.coverage" NAME="test 覆盖结果" MODIFIED="1739518940761" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/pythonProject$langchain.coverage" NAME="langchain 覆盖结果" MODIFIED="1741341082790" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/pythonProject$app.coverage" NAME="app 覆盖结果" MODIFIED="1750324418801" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/pythonProject$qwen_long.coverage" NAME="qwen-long 覆盖结果" MODIFIED="1739426906796" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/pythonProject$bailian_test.coverage" NAME="bailian_test 覆盖结果" MODIFIED="1745482223542" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/pythonProject$deepseek_qwen.coverage" NAME="deepseek-qwen 覆盖结果" MODIFIED="1738913785374" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/pythonProject$bailian_service.coverage" NAME="bailian_service 覆盖结果" MODIFIED="1739513422961" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/pythonProject$video.coverage" NAME="video 覆盖结果" MODIFIED="1751524204860" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/pythonProject$app0__1_.coverage" NAME="app0 (1) 覆盖结果" MODIFIED="1740039688201" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
  </component>
</project>