<!DOCTYPE html>
<html lang="zh">
<head>
  <meta charset="UTF-8">
  <title>RAG-LLM 知识库问答 Demo</title>
  <style>
    body {
      font-family: sans-serif;
      margin: 20px;
      padding: 0;
    }
    h1, h2 {
      color: #333;
    }
    .container {
      max-width: 600px;
      margin: 0 auto;
    }
    .section {
      border: 1px solid #ddd;
      padding: 16px;
      margin-bottom: 20px;
      border-radius: 8px;
      background: #f9f9f9;
    }
    .section h2 {
      margin-top: 0;
    }
    input[type="text"], textarea {
      width: 100%;
      padding: 10px;
      font-size: 1rem;
      margin-bottom: 10px;
      box-sizing: border-box;
    }
    button {
      padding: 10px 20px;
      font-size: 1rem;
      cursor: pointer;
      margin-right: 10px;
    }
    .message {
      margin-top: 10px;
      color: #555;
      white-space: pre-wrap;
    }
    .error {
      color: red;
    }
  </style>
</head>
<body>
<div class="container">
  <h1>RAG-LLM 知识库问答 Demo</h1>

  <!-- 文件上传及知识库构建区 -->
  <div class="section">
    <h2>1. 文件上传</h2>
    <form id="uploadForm">
      <label for="fileInput">选择文件（支持多选）:</label><br/>
      <input id="fileInput" type="file" name="files" multiple />
      <br/><br/>
      <button type="submit">上传文件</button>
    </form>
    <div class="message" id="uploadMsg"></div>

    <hr/>
    <h2>2. 构建知识库</h2>
    <button id="buildKBBtn">构建知识库</button>
    <div class="message" id="buildMsg"></div>
  </div>

  <!-- 问答区 -->
  <div class="section">
    <h2>3. 问答</h2>
    <input type="text" id="questionInput" placeholder="请输入您的问题..." />
    <br/>
    <button id="askBtn">询问</button>
    <div class="message" id="answerMsg"></div>
  </div>
</div>

<script>
  // 1. 文件上传
  const uploadForm = document.getElementById('uploadForm');
  const uploadMsg = document.getElementById('uploadMsg');

  uploadForm.addEventListener('submit', async (e) => {
    e.preventDefault();
    const fileInput = document.getElementById('fileInput');
    if (!fileInput.files.length) {
      uploadMsg.textContent = "请先选择文件。";
      return;
    }

    const formData = new FormData();
    // 这里的键名 'files' 必须与后端一致
    for (let i = 0; i < fileInput.files.length; i++) {
      formData.append('files', fileInput.files[i]);
    }

    uploadMsg.textContent = "正在上传，请稍候...";
    try {
      const response = await fetch('/upload', {
        method: 'POST',
        body: formData
      });
      const result = await response.json();
      if (response.ok) {
        uploadMsg.textContent = "上传成功！文件列表: " + result.filenames.join(', ');
      } else {
        uploadMsg.textContent = "上传失败: " + (result.error || JSON.stringify(result));
      }
    } catch (error) {
      uploadMsg.textContent = "请求错误: " + error;
    }
  });

  // 2. 构建知识库
  const buildKBBtn = document.getElementById('buildKBBtn');
  const buildMsg = document.getElementById('buildMsg');

  buildKBBtn.addEventListener('click', async () => {
    buildMsg.textContent = "正在构建知识库，请稍候...";
    try {
      const response = await fetch('/build_kb', {
        method: 'POST'
      });
      const result = await response.json();
      if (response.ok) {
        buildMsg.textContent = result.message;
      } else {
        buildMsg.textContent = "构建失败: " + (result.error || JSON.stringify(result));
      }
    } catch (error) {
      buildMsg.textContent = "请求错误: " + error;
    }
  });

  // 3. 问答
  const askBtn = document.getElementById('askBtn');
  const answerMsg = document.getElementById('answerMsg');
  const questionInput = document.getElementById('questionInput');

  askBtn.addEventListener('click', async () => {
    const question = questionInput.value.trim();
    if (!question) {
      answerMsg.textContent = "请输入问题。";
      return;
    }
    answerMsg.textContent = "正在思考，请稍候...";

    try {
      const response = await fetch('/ask', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ question: question })
      });
      const result = await response.json();
      if (response.ok) {
        answerMsg.textContent = result.answer;
      } else {
        answerMsg.textContent = "查询失败: " + (result.error || JSON.stringify(result));
      }
    } catch (error) {
      answerMsg.textContent = "请求错误: " + error;
    }
  });
</script>

</body>
</html>
