<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>创新科技 - 引领未来</title>
    <style>
        /* 全局样式 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
        }

        :root {
            --primary-color: #4361ee;
            --secondary-color: #3f37c9;
            --accent-color: #4cc9f0;
            --light-color: #f8f9fa;
            --dark-color: #212529;
            --success-color: #4caf50;
            --warning-color: #ff9800;
            --danger-color: #f44336;
        }

        html {
            scroll-behavior: smooth;
        }

        body {
            background-color: var(--light-color);
            color: var(--dark-color);
            line-height: 1.6;
        }

        /* 导航栏 */
        header {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            background-color: rgba(255, 255, 255, 0.95);
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            z-index: 1000;
            transition: all 0.3s ease;
        }

        .container {
            width: 90%;
            max-width: 1200px;
            margin: 0 auto;
        }

        nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1.5rem 0;
        }

        .logo {
            font-size: 1.8rem;
            font-weight: 700;
            color: var(--primary-color);
            text-decoration: none;
        }

        .nav-links {
            display: flex;
            list-style: none;
        }

        .nav-links li {
            margin-left: 2rem;
        }

        .nav-links a {
            color: var(--dark-color);
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s;
            position: relative;
        }

        .nav-links a:hover {
            color: var(--primary-color);
        }

        .nav-links a::after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 0;
            width: 0;
            height: 2px;
            background-color: var(--primary-color);
            transition: width 0.3s;
        }

        .nav-links a:hover::after {
            width: 100%;
        }

        .mobile-menu-btn {
            display: none;
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: var(--dark-color);
        }

        /* 英雄区域 */
        .hero {
            height: 100vh;
            display: flex;
            align-items: center;
            background: linear-gradient(135deg, rgba(67, 97, 238, 0.1) 0%, rgba(76, 201, 240, 0.1) 100%);
            padding-top: 80px;
        }

        .hero-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .hero-text {
            flex: 1;
            padding-right: 2rem;
        }

        .hero-image {
            flex: 1;
            text-align: center;
        }

        .hero-image img {
            max-width: 100%;
            height: auto;
            border-radius: 10px;
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
        }

        h1 {
            font-size: 3rem;
            margin-bottom: 1.5rem;
            color: var(--dark-color);
            line-height: 1.2;
        }

        .hero p {
            font-size: 1.2rem;
            margin-bottom: 2rem;
            color: #555;
            max-width: 600px;
        }

        .btn {
            display: inline-block;
            padding: 0.8rem 1.8rem;
            background-color: var(--primary-color);
            color: white;
            border-radius: 50px;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s;
            border: none;
            cursor: pointer;
            font-size: 1rem;
        }

        .btn:hover {
            background-color: var(--secondary-color);
            transform: translateY(-3px);
            box-shadow: 0 10px 20px rgba(67, 97, 238, 0.2);
        }

        .btn-outline {
            background-color: transparent;
            border: 2px solid var(--primary-color);
            color: var(--primary-color);
            margin-left: 1rem;
        }

        .btn-outline:hover {
            background-color: var(--primary-color);
            color: white;
        }

        /* 特性部分 */
        .features {
            padding: 6rem 0;
            background-color: white;
        }

        .section-title {
            text-align: center;
            margin-bottom: 4rem;
        }

        .section-title h2 {
            font-size: 2.5rem;
            color: var(--dark-color);
            margin-bottom: 1rem;
        }

        .section-title p {
            color: #666;
            max-width: 700px;
            margin: 0 auto;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
        }

        .feature-card {
            background-color: var(--light-color);
            border-radius: 10px;
            padding: 2rem;
            text-align: center;
            transition: transform 0.3s, box-shadow 0.3s;
        }

        .feature-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
        }

        .feature-icon {
            font-size: 3rem;
            color: var(--primary-color);
            margin-bottom: 1.5rem;
        }

        .feature-card h3 {
            font-size: 1.5rem;
            margin-bottom: 1rem;
        }

        /* 产品展示 */
        .products {
            padding: 6rem 0;
            background-color: #f5f7fa;
        }

        .products-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
        }

        .product-card {
            background-color: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            transition: transform 0.3s, box-shadow 0.3s;
        }

        .product-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
        }

        .product-image {
            height: 200px;
            overflow: hidden;
        }

        .product-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.5s;
        }

        .product-card:hover .product-image img {
            transform: scale(1.1);
        }

        .product-content {
            padding: 1.5rem;
        }

        .product-content h3 {
            font-size: 1.3rem;
            margin-bottom: 0.5rem;
        }

        .product-content p {
            color: #666;
            margin-bottom: 1rem;
        }

        .product-price {
            font-size: 1.2rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 1rem;
        }

        /* 客户评价 */
        .testimonials {
            padding: 6rem 0;
            background-color: white;
        }

        .testimonials-slider {
            max-width: 800px;
            margin: 0 auto;
            position: relative;
        }

        .testimonial {
            background-color: var(--light-color);
            padding: 2rem;
            border-radius: 10px;
            text-align: center;
            display: none;
        }

        .testimonial.active {
            display: block;
            animation: fadeIn 0.5s ease;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        .testimonial-content {
            font-size: 1.1rem;
            font-style: italic;
            color: #555;
            margin-bottom: 1.5rem;
        }

        .testimonial-author {
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .author-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            overflow: hidden;
            margin-right: 1rem;
        }

        .author-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .author-info h4 {
            font-size: 1.1rem;
            margin-bottom: 0.2rem;
        }

        .author-info p {
            color: #777;
            font-size: 0.9rem;
        }

        .slider-controls {
            display: flex;
            justify-content: center;
            margin-top: 2rem;
        }

        .slider-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background-color: #ddd;
            margin: 0 5px;
            cursor: pointer;
            transition: background-color 0.3s;
        }

        .slider-dot.active {
            background-color: var(--primary-color);
        }

        /* 联系我们 */
        .contact {
            padding: 6rem 0;
            background-color: #f5f7fa;
        }

        .contact-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 3rem;
        }

        .contact-info {
            display: flex;
            flex-direction: column;
        }

        .contact-info h3 {
            font-size: 1.5rem;
            margin-bottom: 1.5rem;
        }

        .contact-item {
            display: flex;
            align-items: flex-start;
            margin-bottom: 1.5rem;
        }

        .contact-icon {
            font-size: 1.5rem;
            color: var(--primary-color);
            margin-right: 1rem;
        }

        .contact-text h4 {
            font-size: 1.1rem;
            margin-bottom: 0.3rem;
        }

        .contact-text p {
            color: #666;
        }

        .contact-form {
            background-color: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
        }

        .form-control {
            width: 100%;
            padding: 0.8rem;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 1rem;
            transition: border-color 0.3s;
        }

        .form-control:focus {
            outline: none;
            border-color: var(--primary-color);
        }

        textarea.form-control {
            min-height: 150px;
            resize: vertical;
        }

        /* 页脚 */
        footer {
            background-color: var(--dark-color);
            color: white;
            padding: 4rem 0 2rem;
        }

        .footer-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }

        .footer-col h3 {
            font-size: 1.3rem;
            margin-bottom: 1.5rem;
            position: relative;
        }

        .footer-col h3::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 0;
            width: 50px;
            height: 2px;
            background-color: var(--primary-color);
        }

        .footer-col p {
            color: #bbb;
            margin-bottom: 1rem;
        }

        .footer-links {
            list-style: none;
        }

        .footer-links li {
            margin-bottom: 0.8rem;
        }

        .footer-links a {
            color: #bbb;
            text-decoration: none;
            transition: color 0.3s;
        }

        .footer-links a:hover {
            color: white;
        }

        .social-links {
            display: flex;
            margin-top: 1rem;
        }

        .social-links a {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
            background-color: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            margin-right: 1rem;
            color: white;
            text-decoration: none;
            transition: background-color 0.3s;
        }

        .social-links a:hover {
            background-color: var(--primary-color);
        }

        .copyright {
            text-align: center;
            padding-top: 2rem;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            color: #bbb;
            font-size: 0.9rem;
        }

        /* 返回顶部按钮 */
        .back-to-top {
            position: fixed;
            bottom: 2rem;
            right: 2rem;
            width: 50px;
            height: 50px;
            background-color: var(--primary-color);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            text-decoration: none;
            font-size: 1.5rem;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s;
            z-index: 999;
        }

        .back-to-top.show {
            opacity: 1;
            visibility: visible;
        }

        .back-to-top:hover {
            background-color: var(--secondary-color);
            transform: translateY(-5px);
        }

        /* 响应式设计 */
        @media (max-width: 992px) {
            h1 {
                font-size: 2.5rem;
            }

            .hero-content {
                flex-direction: column;
            }

            .hero-text {
                padding-right: 0;
                margin-bottom: 3rem;
                text-align: center;
            }

            .hero-image {
                width: 80%;
            }
        }

        @media (max-width: 768px) {
            .mobile-menu-btn {
                display: block;
            }

            .nav-links {
                position: fixed;
                top: 80px;
                left: -100%;
                width: 100%;
                height: calc(100vh - 80px);
                background-color: white;
                flex-direction: column;
                align-items: center;
                padding: 2rem 0;
                transition: left 0.3s;
            }

            .nav-links.active {
                left: 0;
            }

            .nav-links li {
                margin: 1rem 0;
            }

            .btn-outline {
                margin-left: 0;
                margin-top: 1rem;
            }
        }

        @media (max-width: 576px) {
            h1 {
                font-size: 2rem;
            }

            .section-title h2 {
                font-size: 2rem;
            }

            .hero-image {
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <header>
        <div class="container">
            <nav>
                <a href="#" class="logo">创新科技</a>
                <button class="mobile-menu-btn">☰</button>
                <ul class="nav-links">
                    <li><a href="#home">首页</a></li>
                    <li><a href="#features">产品特性</a></li>
                    <li><a href="#products">产品展示</a></li>
                    <li><a href="#testimonials">客户评价</a></li>
                    <li><a href="#contact">联系我们</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <!-- 英雄区域 -->
    <section class="hero" id="home">
        <div class="container">
            <div class="hero-content">
                <div class="hero-text">
                    <h1>引领未来的创新科技解决方案</h1>
                    <p>我们致力于为企业提供最前沿的技术解决方案，帮助您在数字化时代保持竞争优势。我们的产品结合了人工智能、大数据和云计算等尖端技术。</p>
                    <div class="hero-buttons">
                        <a href="#contact" class="btn">立即咨询</a>
                        <a href="#features" class="btn btn-outline">了解更多</a>
                    </div>
                </div>
                <div class="hero-image">
                    <img src="https://images.unsplash.com/photo-1551288049-bebda4e38f71?ixlib=rb-1.2.1&auto=format&fit=crop&w=1000&q=80" alt="科技产品展示">
                </div>
            </div>
        </div>
    </section>

    <!-- 特性部分 -->
    <section class="features" id="features">
        <div class="container">
            <div class="section-title">
                <h2>我们的核心优势</h2>
                <p>我们提供全方位的技术解决方案，满足您在不同业务场景下的需求</p>
            </div>
            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-icon">🚀</div>
                    <h3>高性能</h3>
                    <p>我们的系统采用分布式架构设计，能够处理海量数据，确保在高并发场景下的稳定运行。</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">🔒</div>
                    <h3>安全可靠</h3>
                    <p>多层安全防护机制，包括数据加密、访问控制和实时监控，确保您的数据安全无忧。</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">🔄</div>
                    <h3>智能分析</h3>
                    <p>内置AI算法，自动分析业务数据，提供可视化报表和智能决策建议，助力业务增长。</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">📱</div>
                    <h3>多端适配</h3>
                    <p>完美适配PC、平板和手机等多种终端设备，随时随地处理业务，提高工作效率。</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">⚙️</div>
                    <h3>易于集成</h3>
                    <p>提供标准API接口，轻松与现有系统集成，降低企业数字化转型成本。</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">📈</div>
                    <h3>持续更新</h3>
                    <p>专业团队持续优化产品功能，定期发布更新版本，确保您始终使用最先进的技术。</p>
                </div>
            </div>
        </div>
    </section>

    <!-- 产品展示 -->
    <section class="products" id="products">
        <div class="container">
            <div class="section-title">
                <h2>我们的产品系列</h2>
                <p>针对不同行业和业务场景的专业解决方案</p>
            </div>
            <div class="products-grid">
                <div class="product-card">
                    <div class="product-image">
                        <img src="https://images.unsplash.com/photo-1551434678-e076c223a692?ixlib=rb-1.2.1&auto=format&fit=crop&w=1000&q=80" alt="企业管理系统">
                    </div>
                    <div class="product-content">
                        <h3>企业智能管理系统</h3>
                        <p>一体化企业管理平台，整合财务、人事、供应链等核心业务流程。</p>
                        <div class="product-price">¥98,000起</div>
                        <a href="#contact" class="btn">咨询详情</a>
                    </div>
                </div>
                <div class="product-card">
                    <div class="product-image">
                        <img src="https://images.unsplash.com/photo-1522542550221-31fd19575a2d?ixlib=rb-1.2.1&auto=format&fit=crop&w=1000&q=80" alt="数据分析平台">
                    </div>
                    <div class="product-content">
                        <h3>商业智能分析平台</h3>
                        <p>强大的数据分析和可视化工具，帮助企业从数据中发现商业价值。</p>
                        <div class="product-price">¥68,000起</div>
                        <a href="#contact" class="btn">咨询详情</a>
                    </div>
                </div>
                <div class="product-card">
                    <div class="product-image">
                        <img src="https://images.unsplash.com/photo-1522071820081-009f0129c71c?ixlib=rb-1.2.1&auto=format&fit=crop&w=1000&q=80" alt="客户关系管理">
                    </div>
                    <div class="product-content">
                        <h3>智能客户关系管理</h3>
                        <p>基于AI的CRM系统，自动化销售流程，提升客户满意度和转化率。</p>
                        <div class="product-price">¥58,000起</div>
                        <a href="#contact" class="btn">咨询详情</a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 客户评价 -->
    <section class="testimonials" id="testimonials">
        <div class="container">
            <div class="section-title">
                <h2>客户评价</h2>
                <p>听听我们的客户怎么说</p>
            </div>
            <div class="testimonials-slider">
                <div class="testimonial active">
                    <div class="testimonial-content">
                        "创新科技的解决方案帮助我们实现了业务流程的全面数字化，效率提升了40%，客户满意度显著提高。他们的技术支持团队响应迅速，非常专业。"
                    </div>
                    <div class="testimonial-author">
                        <div class="author-avatar">
                            <img src="https://randomuser.me/api/portraits/women/65.jpg" alt="张女士">
                        </div>
                        <div class="author-info">
                            <h4>张女士</h4>
                            <p>某大型零售企业CIO</p>
                        </div>
                    </div>
                </div>
                <div class="testimonial">
                    <div class="testimonial-content">
                        "我们与创新科技合作开发了定制化的数据分析平台，系统上线后，我们的决策速度提高了3倍，数据准确性达到99.9%，这对我们的业务发展起到了关键作用。"
                    </div>
                    <div class="testimonial-author">
                        <div class="author-avatar">
                            <img src="https://randomuser.me/api/portraits/men/32.jpg" alt="王先生">
                        </div>
                        <div class="author-info">
                            <h4>王先生</h4>
                            <p>某金融机构数据分析总监</p>
                        </div>
                    </div>
                </div>
                <div class="testimonial">
                    <div class="testimonial-content">
                        "作为一家快速发展的创业公司，我们需要灵活高效的IT解决方案。创新科技的产品完美契合了我们的需求，而且他们的订阅模式让我们能够根据业务增长灵活扩展。"
                    </div>
                    <div class="testimonial-author">
                        <div class="author-avatar">
                            <img src="https://randomuser.me/api/portraits/women/44.jpg" alt="李女士">
                        </div>
                        <div class="author-info">
                            <h4>李女士</h4>
                            <p>某科技创业公司CEO</p>
                        </div>
                    </div>
                </div>
                <div class="slider-controls">
                    <div class="slider-dot active" data-index="0"></div>
                    <div class="slider-dot" data-index="1"></div>
                    <div class="slider-dot" data-index="2"></div>
                </div>
            </div>
        </div>
    </section>

    <!-- 联系我们 -->
    <section class="contact" id="contact">
        <div class="container">
            <div class="section-title">
                <h2>联系我们</h2>
                <p>我们随时准备为您提供专业咨询和服务</p>
            </div>
            <div class="contact-container">
                <div class="contact-info">
                    <h3>联系方式</h3>
                    <div class="contact-item">
                        <div class="contact-icon">📍</div>
                        <div class="contact-text">
                            <h4>公司地址</h4>
                            <p>北京市海淀区中关村科技园创新大厦15层</p>
                        </div>
                    </div>
                    <div class="contact-item">
                        <div class="contact-icon">📞</div>
                        <div class="contact-text">
                            <h4>联系电话</h4>
                            <p>400-888-9999</p>
                        </div>
                    </div>
                    <div class="contact-item">
                        <div class="contact-icon">✉️</div>
                        <div class="contact-text">
                            <h4>电子邮箱</h4>
                            <p><EMAIL></p>
                        </div>
                    </div>
                    <div class="contact-item">
                        <div class="contact-icon">🕒</div>
                        <div class="contact-text">
                            <h4>工作时间</h4>
                            <p>周一至周五: 9:00 - 18:00</p>
                        </div>
                    </div>
                </div>
                <div class="contact-form">
                    <h3>发送消息</h3>
                    <form id="contactForm">
                        <div class="form-group">
                            <label for="name">您的姓名</label>
                            <input type="text" id="name" class="form-control" required>
                        </div>
                        <div class="form-group">
                            <label for="email">电子邮箱</label>
                            <input type="email" id="email" class="form-control" required>
                        </div>
                        <div class="form-group">
                            <label for="phone">联系电话</label>
                            <input type="tel" id="phone" class="form-control">
                        </div>
                        <div class="form-group">
                            <label for="message">您的需求</label>
                            <textarea id="message" class="form-control" required></textarea>
                        </div>
                        <button type="submit" class="btn">发送消息</button>
                    </form>
                </div>
            </div>
        </div>
    </section>

    <!-- 页脚 -->
    <footer>
        <div class="container">
            <div class="footer-container">
                <div class="footer-col">
                    <h3>关于我们</h3>
                    <p>创新科技成立于2010年，是一家专注于企业数字化转型解决方案的高科技公司。我们拥有超过200名技术专家，服务客户遍布全国。</p>
                    <div class="social-links">
                        <a href="#"><span>微</span></a>
                        <a href="#"><span>微</span></a>
                        <a href="#"><span>知</span></a>
                        <a href="#"><span>抖</span></a>
                    </div>
                </div>
                <div class="footer-col">
                    <h3>快速链接</h3>
                    <ul class="footer-links">
                        <li><a href="#home">首页</a></li>
                        <li><a href="#features">产品特性</a></li>
                        <li><a href="#products">产品展示</a></li>
                        <li><a href="#testimonials">客户评价</a></li>
                        <li><a href="#contact">联系我们</a></li>
                    </ul>
                </div>
                <div class="footer-col">
                    <h3>产品服务</h3>
                    <ul class="footer-links">
                        <li><a href="#">企业管理系统</a></li>
                        <li><a href="#">数据分析平台</a></li>
                        <li><a href="#">客户关系管理</a></li>
                        <li><a href="#">定制开发服务</a></li>
                        <li><a href="#">技术咨询服务</a></li>
                    </ul>
                </div>
                <div class="footer-col">
                    <h3>订阅资讯</h3>
                    <p>订阅我们的电子通讯，获取最新产品资讯和技术文章。</p>
                    <form class="subscribe-form">
                        <input type="email" placeholder="您的邮箱地址" class="form-control" required>
                        <button type="submit" class="btn" style="width: 100%; margin-top: 0.5rem;">订阅</button>
                    </form>
                </div>
            </div>
            <div class="copyright">
                <p>&copy; 2023 创新科技有限公司. 保留所有权利. | 京ICP备12345678号</p>
            </div>
        </div>
    </footer>

    <!-- 返回顶部按钮 -->
    <a href="#" class="back-to-top">↑</a>

    <!-- JavaScript -->
    <script>
        // 移动端菜单切换
        const mobileMenuBtn = document.querySelector('.mobile-menu-btn');
        const navLinks = document.querySelector('.nav-links');

        mobileMenuBtn.addEventListener('click', () => {
            navLinks.classList.toggle('active');
        });

        // 导航栏滚动效果
        window.addEventListener('scroll', () => {
            const header = document.querySelector('header');
            if (window.scrollY > 50) {
                header.style.padding = '1rem 0';
            } else {
                header.style.padding = '1.5rem 0';
            }
        });

        // 返回顶部按钮
        const backToTopBtn = document.querySelector('.back-to-top');

        window.addEventListener('scroll', () => {
            if (window.scrollY > 300) {
                backToTopBtn.classList.add('show');
            } else {
                backToTopBtn.classList.remove('show');
            }
        });

        // 平滑滚动
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function(e) {
                e.preventDefault();

                if (this.getAttribute('href') === '#') return;

                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    window.scrollTo({
                        top: target.offsetTop - 80,
                        behavior: 'smooth'
                    });

                    // 关闭移动端菜单
                    navLinks.classList.remove('active');
                }
            });
        });

        // 客户评价轮播
        const testimonials = document.querySelectorAll('.testimonial');
        const dots = document.querySelectorAll('.slider-dot');
        let currentIndex = 0;

        function showTestimonial(index) {
            testimonials.forEach(testimonial => testimonial.classList.remove('active'));
            dots.forEach(dot => dot.classList.remove('active'));

            testimonials[index].classList.add('active');
            dots[index].classList.add('active');
            currentIndex = index;
        }

        dots.forEach(dot => {
            dot.addEventListener('click', () => {
                const index = parseInt(dot.getAttribute('data-index'));
                showTestimonial(index);
            });
        });

        // 自动轮播
        setInterval(() => {
            currentIndex = (currentIndex + 1) % testimonials.length;
            showTestimonial(currentIndex);
        }, 5000);

        // 表单提交
        const contactForm = document.getElementById('contactForm');

        contactForm.addEventListener('submit', function(e) {
            e.preventDefault();

            // 获取表单数据
            const formData = {
                name: document.getElementById('name').value,
                email: document.getElementById('email').value,
                phone: document.getElementById('phone').value,
                message: document.getElementById('message').value
            };

            // 这里可以添加AJAX提交逻辑
            console.log('表单已提交:', formData);

            // 显示成功消息
            alert('感谢您的留言！我们会尽快与您联系。');

            // 重置表单
            contactForm.reset();
        });
    </script>
</body>
</html>
