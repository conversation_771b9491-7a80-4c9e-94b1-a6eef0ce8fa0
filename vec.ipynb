{"cells": [{"metadata": {"ExecuteTime": {"end_time": "2025-03-12T09:49:25.488296Z", "start_time": "2025-03-12T09:49:22.864196Z"}}, "cell_type": "code", "source": ["import pandas as pd\n", "import jieba\n", "import numpy as np\n", "from sklearn.feature_extraction.text import TfidfVectorizer\n", "from sklearn.metrics.pairwise import cosine_similarity\n", "from gensim.models import Word2Vec\n", "import faiss\n", "import pickle\n", "import os\n", "\n", "# --- 从 CSV 文件加载数据 ---\n", "data_file = \"会议知识库.xlsx\"  # 请确保文件路径正确\n", "conferences = pd.read_excel(data_file)\n", "\n", "# 检查数据是否正确加载\n", "print(\"加载的会议数据预览：\")\n", "print(conferences.head())\n", "\n", "# --- 保存和加载预计算结果的路径 ---\n", "PRECOMPUTED_DIR = \"precomputed\"\n", "TFIDF_MATRIX_PATH = os.path.join(PRECOMPUTED_DIR, \"tfidf_matrix.pkl\")\n", "FEATURE_NAMES_PATH = os.path.join(PRECOMPUTED_DIR, \"feature_names.pkl\")\n", "W2V_MODEL_PATH = os.path.join(PRECOMPUTED_DIR, \"w2v_model\")\n", "FAISS_INDEX_PATH = os.path.join(PRECOMPUTED_DIR, \"faiss_index.index\")\n", "\n", "# 创建保存目录\n", "if not os.path.exists(PRECOMPUTED_DIR):\n", "    os.makedirs(PRECOMPUTED_DIR)\n", "\n", "# --- 离线预计算 ---\n", "\n", "def preprocess_conferences(df):\n", "    \"\"\"预计算会议的关键词、TF-IDF 和向量表示，并保存到磁盘。\"\"\"\n", "    # 如果预计算文件已存在，直接加载\n", "    if (os.path.exists(TFIDF_MATRIX_PATH) and \n", "        os.path.exists(FEATURE_NAMES_PATH) and \n", "        os.path.exists(W2V_MODEL_PATH) and \n", "        os.path.exists(FAISS_INDEX_PATH)):\n", "        print(\"加载预计算结果...\")\n", "        with open(TFIDF_MATRIX_PATH, 'rb') as f:\n", "            tfidf_matrix = pickle.load(f)\n", "        with open(FEATURE_NAMES_PATH, 'rb') as f:\n", "            feature_names = pickle.load(f)\n", "        w2v_model = Word2Vec.load(W2V_MODEL_PATH)\n", "        faiss_index = faiss.read_index(FAISS_INDEX_PATH)\n", "        return tfidf_matrix, feature_names, w2v_model, faiss_index, df\n", "\n", "# 否则进行预计算\n", "    print(\"执行预计算...\")\n", "    # 合并名称、关键词、描述、征稿主题和会议介绍作为文本内容，处理可能的 NaN 值\n", "    df['关键词'] = df['关键词'].fillna('')\n", "    df['描述'] = df['描述'].fillna('')\n", "    df['征稿主题'] = df['征稿主题'].fillna('')\n", "    df['会议介绍'] = df['会议介绍'].fillna('')\n", "    corpus = df.apply(lambda row: f\"{row['名称']} {row['关键词']} {row['描述']} {row['征稿主题']} {row['会议介绍']}\", axis=1).tolist()\n", "    \n", "    # 1. TF-IDF 关键词索引\n", "    vectorizer = TfidfVectorizer(tokenizer=jieba.lcut)\n", "    tfidf_matrix = vectorizer.fit_transform(corpus)\n", "    feature_names = vectorizer.get_feature_names_out()\n", "    \n", "    # 2. Word2Vec 生成向量表示\n", "    sentences = [jieba.lcut(text) for text in corpus]\n", "    w2v_model = Word2Vec(sentences, vector_size=100, window=5, min_count=1, workers=4)\n", "    vectors = np.array([np.mean([w2v_model.wv[word] for word in sentence if word in w2v_model.wv], \n", "                                axis=0) for sentence in sentences])\n", "    \n", "    # 3. FAISS 索引用于快速向量搜索\n", "    index = faiss.IndexFlatL2(100)  # 100 维向量\n", "    index.add(vectors)\n", "    \n", "    # 保存预计算结果\n", "    print(\"保存预计算结果到磁盘...\")\n", "    with open(TFIDF_MATRIX_PATH, 'wb') as f:\n", "        pickle.dump(tfidf_matrix, f)\n", "    with open(FEATURE_NAMES_PATH, 'wb') as f:\n", "        pickle.dump(feature_names, f)\n", "    w2v_model.save(W2V_MODEL_PATH)\n", "    faiss.write_index(index, FAISS_INDEX_PATH)\n", "    \n", "    return tfidf_matrix, feature_names, w2v_model, index, df\n", "\n", "# 执行离线预计算或加载\n", "tfidf_matrix, feature_names, w2v_model, faiss_index, preprocessed_conferences = preprocess_conferences(conferences)\n", "\n", "# --- 在线计算 ---\n", "\n", "def process_user_input(title, abstract):\n", "    \"\"\"从用户输入中提取关键词和向量表示。\"\"\"\n", "    user_text = f\"{title} {abstract}\"\n", "    \n", "    # 1. 使用 TF-IDF 提取关键词\n", "    user_keywords = jieba.lcut(user_text)\n", "    user_tfidf = TfidfVectorizer(tokenizer=jieba.lcut, vocabulary=feature_names).fit_transform([user_text])\n", "    \n", "    # 2. 使用 Word2Vec 生成向量表示\n", "    user_vector = np.mean([w2v_model.wv[word] for word in user_keywords if word in w2v_model.wv], \n", "                          axis=0).reshape(1, -1)\n", "    \n", "    return user_tfidf, user_vector\n", "\n", "def match_conferences(user_tfidf, user_vector, tfidf_matrix, faiss_index, conferences):\n", "    \"\"\"匹配用户输入与会议，并计算最终得分。\"\"\"\n", "    # 1. 关键词匹配（TF-IDF 余弦相似度）\n", "    keyword_similarities = cosine_similarity(user_tfidf, tfidf_matrix).flatten()\n", "    \n", "    # 2. 语义相似度（FAISS 向量搜索）\n", "    D, I = faiss_index.search(user_vector, len(conferences))\n", "    semantic_similarities = 1 / (1 + <PERSON>.flatten())  # 将距离转换为相似度\n", "    \n", "    # 3. 计算最终匹配得分\n", "    alpha, beta = 0.6, 0.4  # 权重系数\n", "    final_scores = []\n", "    for i, row in conferences.iterrows():\n", "        keyword_score = keyword_similarities[i]\n", "        semantic_score = semantic_similarities[i]\n", "        score = alpha * keyword_score + beta * semantic_score\n", "        final_scores.append((row[\"会议编号\"], row[\"名称\"], score))\n", "    \n", "    # 4. 排序并返回前 5 个结果\n", "    final_scores.sort(key=lambda x: x[2], reverse=True)\n", "    return final_scores[:5]\n", "\n", "# 示例使用\n", "user_title = \"知识密集型自然语言处理任务的检索增强生成方法\"\n", "user_abstract = '''大型预训练语言模型已被证明在其参数中存储了事实知识，并在微调下游自然语言处理（NLP）任务时取得了最先进的结果。然而，它们访问和精确操控知识的能力仍然有限，因此在知识密集型任务上，其性能仍落后于特定任务的架构。此外，为它们的决策提供出处以及更新其世界知识仍然是开放的研究问题。迄今为止，带有显式非参数化记忆的可微分访问机制的预训练模型仅在抽取型下游任务中得到了研究。我们探索了一种通用的微调方法，用于检索增强生成（RAG）——这种模型结合了预训练的参数化记忆和非参数化记忆以进行语言生成。我们提出了RAG模型，其中参数化记忆是一个预训练的序列到序列（seq2seq）模型，而非参数化记忆是一个密集向量索引的维基百科，通过一个预训练的神经检索器访问。我们比较了两种RAG公式，一种是在整个生成序列中基于相同检索到的段落进行条件化，另一种可以为每个标记使用不同的段落。我们在一系列知识密集型NLP任务上对模型进行了微调和评估，并在三个开放域问答任务中达到了最先进的水平，超越了纯参数化的seq2seq模型和特定任务的检索-抽取架构。对于语言生成任务，我们发现RAG模型生成的语言比最先进的纯参数化seq2seq基线模型更加具体、多样且富含事实信息。'''\n", "user_tfidf, user_vector = process_user_input(user_title, user_abstract)\n", "matched_conferences = match_conferences(user_tfidf, user_vector, tfidf_matrix, faiss_index, preprocessed_conferences)\n", "\n", "# 输出结果\n", "print(\"\\n最佳匹配会议：\")\n", "for conf_id, conf_name, score in matched_conferences:\n", "    print(f\"会议编号: {conf_id}, 名称: {conf_name}, 得分: {score:.4f}\")"], "id": "302100a1bfc5f0e3", "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Building prefix dict from the default dictionary ...\n", "Loading model from cache C:\\Users\\<USER>\\AppData\\Local\\Temp\\jieba.cache\n"]}, {"name": "stdout", "output_type": "stream", "text": ["加载的会议数据预览：\n", "     序号    会议编号                                                 名称  \\\n", "0  3231  A77BER              2025年智能电力系统与数据驱动创新国际学术会议（IPSDDI 2025）   \n", "1  3287  R3EVI2   【IEEE-人工智能重点会议】第五届人工智能、大数据与算法国际学术会议(CAIBDA 2025)   \n", "2  3301  AMVUNQ  【IEEE出版，EI稳定，湖南大学主办】第二届智能电网与人工智能国际学术会议（SGAI 2025)   \n", "3  3311  6B7VF2           IEEE出版、南航主办-第五届传感器与信息技术国际学术会议（ICSI 2025）   \n", "4  3316  RVUYVZ     【IEEE|见刊后1个月检索】第二届电气技术与自动化工程国际学术会议 (ETAE 2025)   \n", "\n", "                                                英文名称  \\\n", "0  2025 International Conference on Intelligent P...   \n", "1  2025 5th International Conference on Artificia...   \n", "2  2025 2nd International Conference on Smart Gri...   \n", "3  2025 5th International Conference on Sensors a...   \n", "4  The 2nd International Conference on Electrical...   \n", "\n", "                           领域                          检索类型        开始时间  \\\n", "0       动力与电气工程,人工智能,计算机科学与技术                     EI,Scopus  2025-11-07   \n", "1     人工智能,计算机科学与技术,信息科学与系统科学         EI,Scopus,IEEE Xplore  2025-06-13   \n", "2      动力与电气工程,人工智能,信息科学与系统科学                     EI,Scopus  2025-03-21   \n", "3  信息科学与系统科学,计算机科学与技术,电子与通信技术  EI,Scopus,IEEE Xplore,Inspec  2025-03-21   \n", "4        动力与电气工程,机械工程,电子与通信技术                     EI,Scopus  2025-04-25   \n", "\n", "         结束时间  截稿时间  国家   省份   城市          地点  \\\n", "0  2025-11-09   NaN  中国  辽宁省  沈阳市          沈阳   \n", "1  2025-06-15   NaN  中国  北京市  北京市          北京   \n", "2  2025-03-23   NaN  中国  湖南省  长沙市        湖南大学   \n", "3  2025-03-23   NaN  中国  江苏省  南京市  南京-南京天丰大酒店   \n", "4  2025-04-27   NaN  中国  广东省  广州市         广州市   \n", "\n", "                                    关键词  \\\n", "0  2024年智能电力系统与数据驱动创新国际研讨会（IPSDDI 2024）   \n", "1    第五届人工智能，大数据与算法国际学术会议 (CAIBDA 2025)   \n", "2         第二届智能电网与人工智能国际学术会议（SGAI 2025)   \n", "3          第五届传感器与信息技术国际学术会议（ICSI 2025）   \n", "4       第二届电气技术与自动化工程国际学术会议 (ETAE 2025)   \n", "\n", "                                                  描述  \\\n", "0  为了落实国家创新驱动发展战略，顺应电力物联网的建设和智能传感器技术的不断发展，2024年智能...   \n", "1  第五届人工智能，大数据与算法国际学术会议 (CAIBDA 2025)将于2025年6月20-...   \n", "2  第二届智能电网与人工智能国际学术会议（SGAI 2025)将于2025年3月21-23日在长...   \n", "3  第五届传感器与信息技术国际学术会议（ICSI 2025）将于2024年3月21-23日召开。...   \n", "4  第二届电气技术与自动化工程国际学术会议 (ETAE 2025) 将于2025年4月25-27...   \n", "\n", "                                                征稿主题  \\\n", "0                                                NaN   \n", "1  提交检索：IEEE Xplore, EI Compendex, Scopus,人工智能   ...   \n", "2  可再生能源\\n智能电网\\n电动汽车\\n储能技术\\n太阳能光伏\\n风能\\n绿色氢能\\n碳中和\\...   \n", "3  传感器状态监测故障诊断路径规划避障避险雷达定位机器人感知人机交互通信工程图像处理信号处理GP...   \n", "4                                                NaN   \n", "\n", "                                                会议介绍  排序  其他信息  \n", "0  2025年智能电力系统与数据驱动创新国际学术会议（IPSDDI 2025）2025 Inte...   2   NaN  \n", "1  第五届人工智能、大数据与算法国际学术会议 (CAIBDA 2025)2025 5th Int...   1   NaN  \n", "2  第二届智能电网与人工智能国际学术会议（SGAI 2025)2025 2nd Internat...   5   NaN  \n", "3  第五届传感器与信息技术国际学术会议（ICSI 2025）2025 5th&nbsp;Inte...   5   NaN  \n", "4  ✅早鸟优惠-3月11号前投稿可减免400元！✅IEEE出版-EI检索稳定且快速-对学生超友好...   2   NaN  \n", "加载预计算结果...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Loading model cost 0.376 seconds.\n", "Prefix dict has been built successfully.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "最佳匹配会议：\n", "会议编号: A77BER, 名称: 2025年智能电力系统与数据驱动创新国际学术会议（IPSDDI 2025）, 得分: 0.3102\n", "会议编号: R3EVI2, 名称: 【IEEE-人工智能重点会议】第五届人工智能、大数据与算法国际学术会议(CAIBDA 2025), 得分: 0.2687\n", "会议编号: FMNVJR, 名称: 【ACM出版|往届会后3个月EI检索】第二届计算机与多媒体技术国际学术会议（ICCMT 2025）, 得分: 0.2604\n", "会议编号: 2AQZ3Q, 名称: 【ACM出版高录用 | 快速见刊检索】2025年人工智能与基础模型国际学术会议（AIFM 2025）, 得分: 0.2584\n", "会议编号: AMVUNQ, 名称: 【IEEE出版，EI稳定，湖南大学主办】第二届智能电网与人工智能国际学术会议（SGAI 2025), 得分: 0.2531\n"]}, {"name": "stderr", "output_type": "stream", "text": ["D:\\PythonProject\\pythonProject\\.venv\\lib\\site-packages\\sklearn\\feature_extraction\\text.py:517: UserWarning: The parameter 'token_pattern' will not be used since 'tokenizer' is not None'\n", "  warnings.warn(\n"]}], "execution_count": 1}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": "", "id": "1832661f5acbe0df"}, {"metadata": {"ExecuteTime": {"end_time": "2025-03-21T05:33:10.913737Z", "start_time": "2025-03-21T05:33:00.587655Z"}}, "cell_type": "code", "source": ["def calculate_net_salary(w, p):\n", "    \"\"\"\n", "    计算到手工资的函数\n", "    :param w: 税前工资（float）\n", "    :param p: 住房公积金缴纳比例（float，例如12表示12%）\n", "    :return: 到手工资（float）\n", "    \"\"\"\n", "    # 五险一金个人缴纳比例\n", "    pension_rate = 0.08  # 养老保险\n", "    medical_rate = 0.02  # 医疗保险\n", "    unemployment_rate = 0.01  # 失业保险\n", "    housing_fund_rate = p / 100  # 住房公积金\n", "\n", "    # 计算个人五险一金缴纳总额\n", "    social_insurance = w * (pension_rate + medical_rate + unemployment_rate)\n", "    housing_fund = w * housing_fund_rate\n", "    total_deduction = social_insurance + housing_fund\n", "\n", "    # 应纳税所得额\n", "    taxable_income = w - total_deduction - 5000  # 起征点为5000元\n", "\n", "    # 根据税率表计算个人所得税\n", "    if taxable_income <= 0:\n", "        tax = 0\n", "    elif taxable_income <= 3000:\n", "        tax = taxable_income * 0.03\n", "    elif taxable_income <= 12000:\n", "        tax = taxable_income * 0.10 - 210\n", "    elif taxable_income <= 25000:\n", "        tax = taxable_income * 0.20 - 1410\n", "    elif taxable_income <= 35000:\n", "        tax = taxable_income * 0.25 - 2660\n", "    elif taxable_income <= 55000:\n", "        tax = taxable_income * 0.30 - 4410\n", "    elif taxable_income <= 80000:\n", "        tax = taxable_income * 0.35 - 7160\n", "    else:\n", "        tax = taxable_income * 0.45 - 15160\n", "\n", "    # 到手工资\n", "    net_salary = w - total_deduction - tax\n", "    return net_salary\n", "\n", "\n", "# 用户输入税前工资和住房公积金比例\n", "if __name__ == \"__main__\":\n", "    try:\n", "        w = float(input(\"请输入税前工资（元）：\"))\n", "        p = float(input(\"请输入住房公积金缴纳比例（%）：\"))\n", "        net_salary = calculate_net_salary(w, p)\n", "        print(f\"您的到手工资为：{net_salary:.2f} 元\")\n", "    except ValueError:\n", "        print(\"输入有误，请输入数字！\")"], "id": "48dbf36603bb0d36", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["您的到手工资为：11375.00 元\n"]}], "execution_count": 2}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": "", "id": "a9d9b15cd7c35735"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": "", "id": "d08662fd8bf2bb33"}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.6"}}, "nbformat": 4, "nbformat_minor": 5}