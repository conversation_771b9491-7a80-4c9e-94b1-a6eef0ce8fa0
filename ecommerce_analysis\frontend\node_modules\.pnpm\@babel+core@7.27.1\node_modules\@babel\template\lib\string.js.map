{"version": 3, "names": ["_options", "require", "_parse", "_populate", "stringTemplate", "formatter", "code", "opts", "metadata", "arg", "replacements", "normalizeReplacements", "parseAndBuildMetadata", "unwrap", "populatePlaceholders"], "sources": ["../src/string.ts"], "sourcesContent": ["import type { Formatter } from \"./formatters.ts\";\nimport type { TemplateOpts } from \"./options.ts\";\nimport type { Metadata } from \"./parse.ts\";\nimport { normalizeReplacements } from \"./options.ts\";\nimport parseAndBuildMetadata from \"./parse.ts\";\nimport populatePlaceholders from \"./populate.ts\";\n\nexport default function stringTemplate<T>(\n  formatter: Formatter<T>,\n  code: string,\n  opts: TemplateOpts,\n): (arg?: unknown) => T {\n  code = formatter.code(code);\n\n  let metadata: Metadata;\n\n  return (arg?: unknown) => {\n    const replacements = normalizeReplacements(arg);\n\n    if (!metadata) metadata = parseAndBuildMetadata(formatter, code, opts);\n\n    return formatter.unwrap(populatePlaceholders(metadata, replacements));\n  };\n}\n"], "mappings": ";;;;;;AAGA,IAAAA,QAAA,GAAAC,OAAA;AACA,IAAAC,MAAA,GAAAD,OAAA;AACA,IAAAE,SAAA,GAAAF,OAAA;AAEe,SAASG,cAAcA,CACpCC,SAAuB,EACvBC,IAAY,EACZC,IAAkB,EACI;EACtBD,IAAI,GAAGD,SAAS,CAACC,IAAI,CAACA,IAAI,CAAC;EAE3B,IAAIE,QAAkB;EAEtB,OAAQC,GAAa,IAAK;IACxB,MAAMC,YAAY,GAAG,IAAAC,8BAAqB,EAACF,GAAG,CAAC;IAE/C,IAAI,CAACD,QAAQ,EAAEA,QAAQ,GAAG,IAAAI,cAAqB,EAACP,SAAS,EAAEC,IAAI,EAAEC,IAAI,CAAC;IAEtE,OAAOF,SAAS,CAACQ,MAAM,CAAC,IAAAC,iBAAoB,EAACN,QAAQ,EAAEE,YAAY,CAAC,CAAC;EACvE,CAAC;AACH", "ignoreList": []}