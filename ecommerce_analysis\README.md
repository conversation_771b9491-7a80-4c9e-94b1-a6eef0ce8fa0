# 基于 Scrapy-Redis 的多平台电商竞品智能分析系统

## 系统概述

本系统是一个基于Scrapy-Redis的分布式爬虫框架，专门用于多平台电商竞品数据的采集、分析和可视化。系统支持京东、淘宝/天猫、亚马逊中国等主流电商平台，能够自动采集商品价格、促销、评价等多维度数据，并提供竞品分析和可视化功能。

## 系统架构

系统采用前后端分离架构：

- **后端**：基于Python + Flask + Scrapy-Redis + MySQL + Redis
- **前端**：基于React + TypeScript + Tailwind CSS

### 核心组件

1. **分布式爬虫系统**：基于Scrapy-Redis实现的分布式爬虫，支持多节点部署和任务分发
2. **任务调度中心**：负责爬虫任务的创建、分发、监控和重试
3. **数据处理模块**：负责数据清洗、转换和存储
4. **API服务**：提供RESTful API接口，支持前端数据获取和操作
5. **前端管理界面**：提供数据展示、任务管理、竞品分析等功能

## 目录结构

```
ecommerce_analysis/
├── scrapy_app/              # 爬虫应用
│   ├── spiders/             # 爬虫实现
│   ├── middlewares/         # 中间件
│   ├── pipelines/           # 数据处理管道
│   ├── utils/               # 工具函数
│   ├── items.py             # 数据模型
│   └── settings.py          # 爬虫配置
├── scheduler/               # 任务调度器
│   └── task_scheduler.py    # 调度器实现
├── api/                     # API服务
│   ├── routes/              # 路由定义
│   ├── services/            # 业务逻辑
│   └── app.py               # API入口
├── frontend/                # 前端应用
│   ├── src/                 # 源代码
│   │   ├── components/      # 组件
│   │   ├── pages/           # 页面
│   │   ├── hooks/           # 自定义钩子
│   │   └── App.tsx          # 应用入口
│   └── public/              # 静态资源
├── db_init.sql              # 数据库初始化脚本
├── requirements.txt         # 后端依赖
├── deploy.sh                # 部署脚本
└── integration_test.js      # 集成测试脚本
```

## 功能特性

### 1. 分布式爬虫

- 支持京东、淘宝/天猫、亚马逊中国等主流电商平台
- 基于Scrapy-Redis实现分布式架构，支持水平扩展
- 智能反爬策略，包括IP代理池、请求延迟、User-Agent轮换等
- 自动处理登录、验证码等复杂场景

### 2. 任务调度

- 支持多频率任务调度（高频、中频、低频）
- 任务状态监控与失败重试机制
- 新商品URL自动识别与添加

### 3. 数据处理

- 商品基础信息、价格、促销、评价等多维度数据清洗
- 数据去重与更新策略
- 历史数据存储与趋势分析

### 4. 竞品分析

- 价格对比与历史趋势分析
- 评价情感分析与关键词提取
- 市场份额估算与可视化
- 多维度竞品对比分析

### 5. 前端管理界面

- 响应式设计，支持桌面和移动设备
- 实时数据展示与可视化
- 任务管理与监控
- 用户权限管理

## 安装与部署

### 系统要求

- Python 3.11+
- Node.js 20+
- MySQL 8.0+
- Redis 6.0+

### 安装步骤

1. **克隆代码库**

```bash
git clone https://github.com/yourusername/ecommerce-analysis.git
cd ecommerce-analysis
```

2. **安装后端依赖**

```bash
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate
pip install -r requirements.txt
```

3. **初始化数据库**

```bash
mysql -u root -p < db_init.sql
```

4. **安装前端依赖**

```bash
cd frontend
npm install
```

5. **构建前端**

```bash
npm run build
```

### 使用部署脚本

系统提供了自动化部署脚本，可以一键完成部署：

```bash
chmod +x deploy.sh
./deploy.sh
```

对于开发环境，可以使用：

```bash
./deploy.sh --dev
```

## 使用指南

### 1. 登录系统

访问 `http://localhost:5000`，使用默认管理员账号登录：
- 用户名：admin
- 密码：admin123

### 2. 添加监控商品

1. 进入"任务管理"页面
2. 点击"创建新任务"
3. 输入商品URL和选择平台
4. 提交任务

### 3. 查看商品数据

1. 进入"商品管理"页面
2. 使用筛选功能查找特定商品
3. 点击商品查看详情

### 4. 进行竞品分析

1. 进入"竞品分析"页面
2. 选择要对比的商品
3. 查看多维度对比分析结果

## 性能与扩展性

系统设计支持高性能和良好的扩展性：

- **水平扩展**：可通过增加爬虫节点线性提升性能
- **任务优先级**：支持基于重要性的任务优先级调度
- **资源控制**：可配置爬虫并发度、请求频率等参数
- **容错机制**：自动处理网络错误、爬取失败等异常情况

详细性能测试结果请参考 `performance_test_report.md`。

## 常见问题

1. **Q: 系统支持哪些电商平台？**
   A: 目前支持京东、淘宝、天猫和亚马逊中国，可以通过开发新的爬虫模块扩展支持其他平台。

2. **Q: 如何调整爬虫的抓取频率？**
   A: 在 `scheduler/task_scheduler.py` 中可以配置高、中、低频任务的调度间隔。

3. **Q: 系统如何处理反爬机制？**
   A: 系统实现了多种反爬策略，包括IP代理池、请求延迟、User-Agent轮换等，可在 `scrapy_app/middlewares.py` 中进行配置。

4. **Q: 如何备份数据？**
   A: 可以使用MySQL的标准备份工具，如mysqldump进行数据备份。

## 贡献与开发

欢迎贡献代码或提出建议。开发新功能或修复bug时，请遵循以下步骤：

1. Fork代码库
2. 创建功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add some amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 创建Pull Request

## 许可证

本项目采用MIT许可证 - 详情请参见 LICENSE 文件。

## 联系方式

如有任何问题或建议，请联系：

- 项目维护者：Your Name
- 邮箱：<EMAIL>
