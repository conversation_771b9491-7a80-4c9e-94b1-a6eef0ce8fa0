import cv2
import json
import time
import os
import base64
import threading
import dashscope
from dashscope import MultiModalConversation

# 忽略 libpng 警告
os.environ["OPENCV_IO_IGNORE_ICC_PROFILE"] = "1"
# 设置你的阿里云 API Key
dashscope.api_key = os.getenv("DASHSCOPE_API_KEY")


class RealTimeDetection:
    def __init__(self):
        self.cap = cv2.VideoCapture(0)
        self.latest_bboxes = []
        self.running = True
        self.frame = None
        self.lock = threading.Lock()
        self.full_response = ""

    def draw_bboxes(self, frame, bboxes):
        """绘制边界框和标签"""
        for item in bboxes:
            try:
                x1, y1, x2, y2 = map(int, item["bbox_2d"])  # 转为整数坐标
                label = item["label"]
                # 画框
                cv2.rectangle(frame, (x1, y1), (x2, y2), (0, 255, 0), 2)
                # 画标签
                cv2.putText(frame, label, (x1, y1 - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.9, (0, 255, 0), 2)
            except Exception as e:
                print("绘制错误:", e)
        return frame

    def detect_objects(self):
        """流式调用API检测物体"""
        while self.running:
            time.sleep(1)  # 每秒检测1次

            with self.lock:
                if self.frame is None:
                    continue
                frame = self.frame.copy()

            # 编码当前帧
            _, buffer = cv2.imencode('.jpg', frame)
            base64_image = base64.b64encode(buffer).decode('utf-8')

            messages = [{
                "role": "user",
                "content": [
                    {"image": f"data:image/jpeg;base64,{base64_image}"},
                    {"text": (
                        "Identify the 3 to 5 most prominent objects in the image. "
                        "Return only a valid JSON array, where each item is in the format: "
                        "{'bbox_2d': [x1, y1, x2, y2], 'label': 'object_name'}. "
                        "'bbox_2d' should be the bounding box in pixel coordinates "
                        "([top-left x, y, bottom-right x, y]), and 'label' should be a short, lowercase English noun. "
                        "Do not include any explanation or text outside the JSON array. "
                        "The object names are not limited to 'man', 'cup', or 'monitor' — use whatever is most relevant in the image."
                    )}
                ]
            }]

            try:
                # 流式请求
                self.full_response = ""  # 清空上一次的结果
                response = MultiModalConversation.call(
                    model="qwen-omni-turbo-latest",
                    messages=messages,
                    stream=True
                )

                # 处理流式数据
                for chunk in response:
                    if chunk.status_code == 200:
                        for content in chunk.output.choices[0].message.content:
                            if "text" in content:
                                self.full_response += content["text"]
                    else:
                        print("分块请求失败:", chunk.code)

                # 提取JSON部分（去掉可能的Markdown符号）
                json_str = self.full_response.strip().strip('```json').strip('```').strip()
                print("原始响应:", json_str)  # 调试用

                # 解析JSON
                try:
                    bboxes = json.loads(json_str)
                    if isinstance(bboxes, list):  # 确保是列表
                        with self.lock:
                            self.latest_bboxes = bboxes
                except json.JSONDecodeError:
                    print("JSON解析失败，原始数据:", json_str)

            except Exception as e:
                print("检测异常:", e)

    def run(self):
        # 启动检测线程
        detect_thread = threading.Thread(target=self.detect_objects)
        detect_thread.daemon = True
        detect_thread.start()

        try:
            while self.running:
                ret, frame = self.cap.read()
                if not ret:
                    break

                # 更新当前帧
                with self.lock:
                    self.frame = frame.copy()
                    display_frame = frame.copy()
                    if self.latest_bboxes:
                        display_frame = self.draw_bboxes(display_frame, self.latest_bboxes)

                # 显示画面
                cv2.imshow('Real-time Detection (Streaming)', display_frame)
                if cv2.waitKey(1) & 0xFF == ord('q'):
                    self.running = False
        finally:
            self.cap.release()
            cv2.destroyAllWindows()


if __name__ == "__main__":
    detector = RealTimeDetection()
    detector.run()