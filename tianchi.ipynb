{"cells": [{"cell_type": "code", "id": "initial_id", "metadata": {"collapsed": true, "ExecuteTime": {"end_time": "2025-04-15T09:33:59.769200Z", "start_time": "2025-04-15T09:33:59.523990Z"}}, "source": ["import jieba # 中文分词\n", "import re\n", "import json\n", "import calendar\n", "import pandas as pd\n", "from collections import Counter\n", "\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from sklearn.model_selection import train_test_split, TimeSeriesSplit # 或 KFold\n", "import lightgbm as lgb # 或 XGBoost\n", "from sklearn.feature_extraction.text import TfidfVectorizer\n", "from sklearn.preprocessing import StandardScaler, OneHotEncoder # 等\n", "from sklearn.metrics import mean_absolute_error\n", "\n", "\n", "plt.rcParams['font.family'] = 'SimHei'\n", "plt.rcParams['axes.unicode_minus'] = False\n", "\n", "# --- 1. 加载数据 ---\n", "# df_train = pd.read_csv('训练数据路径')\n", "# df_test = pd.read_csv('测试数据路径') # 测试集后续提供\n", "# target = 'interaction_cnt'\n", "\n", "# --- 2/3. 预处理与特征工程 ---\n", "# 时间特征提取...\n", "# 清洗 fans_cnt, coin_cnt, age 等...\n", "# 文本清洗与 TF-IDF/Embedding ...\n", "# 类别特征编码 (OneHotEncoder, TargetEncoder等)...\n", "# 数值特征缩放/变换...\n", "# 合并所有特征为 X_train, X_test\n", "\n", "# --- 4. 目标变量转换 ---\n", "# y_train_log = np.log1p(df_train[target])\n", "\n", "# --- 5. 模型训练 ---\n", "# model = lgb.LGBMRegressor(objective='mae', metric='mae', random_state=42) # 使用 MAE 目标函数\n", "# model.fit(X_train, y_train_log, ...) # 可能需要加入验证集进行早停\n", "\n", "# --- 6. 预测与评估 (在验证集上) ---\n", "# y_pred_log_val = model.predict(X_val)\n", "# y_pred_val = np.expm1(y_pred_log_val)\n", "# y_pred_val[y_pred_val < 0] = 0 # 确保非负\n", "# mae_score = mean_absolute_error(y_val, y_pred_val) # 计算 MAE\n", "# sum_abs_error = np.sum(np.abs(y_val - y_pred_val)) # 计算比赛得分 SAE\n", "# print(f\"验证集 MAE: {mae_score}\")\n", "# print(f\"验证集 SAE (比赛得分): {sum_abs_error}\")\n", "\n", "# --- 8. 最终预测 ---\n", "# # 在完整训练集上训练模型...\n", "# y_pred_log_test = model.predict(X_test)\n", "# y_pred_test = np.expm1(y_pred_log_test)\n", "# y_pred_test[y_pred_test < 0] = 0\n", "# # 生成提交文件...\n"], "outputs": [], "execution_count": 1}, {"metadata": {"ExecuteTime": {"end_time": "2025-04-15T09:36:49.685042Z", "start_time": "2025-04-15T09:33:59.770192Z"}}, "cell_type": "code", "source": ["df = pd.read_excel(r\"C:\\Users\\<USER>\\Downloads\\智慧赢销dataset\\train.xlsx\")\n", "df.head()\n"], "id": "86353238390ee2b4", "outputs": [{"data": {"text/plain": ["                         id  update_time  publish_time  site_id title  \\\n", "0  503864075537828120240527     20240527      20240527        1   NaN   \n", "1  503859033984486120240527     20240527      20240527        1   NaN   \n", "2  503868055486634820240527     20240527      20240527        1   NaN   \n", "3  503858709319246220240527     20240527      20240527        1   NaN   \n", "4  503894276702418820240531     20240531      20240528        1   NaN   \n", "\n", "                                             content  \\\n", "0  六一的风，已经吹动了多少个宝宝？5月27日-6月7日上饿了么搜「折个儿童节」，童年快乐不只6...   \n", "1  童年的纸飞机，现在终于飞回我手里！\\n亲爱的大朋友小朋友们，\\n5月27日-6月7日上饿了么...   \n", "2  童年的纸飞机，现在终于飞回我手里！[纸飞机][纸飞机]\\n亲爱的大朋友小朋友们，5月27日-...   \n", "3                            #卡卡深夜落地贵阳#童年的纸飞机终于飞回我手里   \n", "4  童年的纸飞机，现在终于飞回我手里！\\n亲爱的大朋友、小朋友们\\n5月27日-6月7日上@饿了...   \n", "\n", "                                uid gender     age city fans_cnt  \\\n", "0  b2c2096f1d4ce21dec32d21f6894d11f      女   60岁以上  NaN    10000   \n", "1  a6332fd2b8428dbb54cc5b0836d5fd14      女  26-30岁  NaN    12000   \n", "2  370b636aa0656c6ebc1ba3a14b86a8e4      男   60岁以上  NaN     9000   \n", "3  252618759dd4f949000c1effd20c7dc0      女  31-40岁   贵阳        0   \n", "4  52852e67be1ebfbf4c4051a55929fb25      女   60岁以上  NaN   230000   \n", "\n", "                         topics  video_cnt coin_cnt post_type  \\\n", "0              \"[\"\"饿了么折个儿童节\"\"]\"        NaN    10000      常规图文   \n", "1              \"[\"\"饿了么折个儿童节\"\"]\"        NaN     4000      常规图文   \n", "2  \"[\"\"饿了么折个儿童节\"\",\"\"花小新疆炒米粉\"\"]\"        NaN     7000      常规图文   \n", "3              \"[\"\"卡卡深夜落地贵阳\"\"]\"        NaN      NaN      常规图文   \n", "4              \"[\"\"饿了么折个儿童节\"\"]\"        NaN  1110000      常规图文   \n", "\n", "                                   cover_ocr_content video_content  \\\n", "0  ①C·饿了么\\n节\\n斤个心\\n上个我了么放心点\\ncococean\\n\\n童年快乐不只6....           NaN   \n", "1  ①C·饿了么\\n眉州东坡\\n\\n斤个儿\\n上个我了么放心点\\n眉州东坡\\n\\n童年快乐准时达...           NaN   \n", "2  ①HUAXIAOXIAO\\n·饿了么\\n花\\n\\n\\n来一碗燃起来\\n节\\n斤个儿\\n上个我...           NaN   \n", "3                                                NaN           NaN   \n", "4  ①C饿了么×棒!约翰\\n可和\\n节\\n个\\n上个饿了么放心点\\n棒约翰\\n童年快乐准时达\\n...           NaN   \n", "\n", "   interaction_cnt  \n", "0                0  \n", "1                0  \n", "2                0  \n", "3                0  \n", "4                5  "], "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>update_time</th>\n", "      <th>publish_time</th>\n", "      <th>site_id</th>\n", "      <th>title</th>\n", "      <th>content</th>\n", "      <th>uid</th>\n", "      <th>gender</th>\n", "      <th>age</th>\n", "      <th>city</th>\n", "      <th>fans_cnt</th>\n", "      <th>topics</th>\n", "      <th>video_cnt</th>\n", "      <th>coin_cnt</th>\n", "      <th>post_type</th>\n", "      <th>cover_ocr_content</th>\n", "      <th>video_content</th>\n", "      <th>interaction_cnt</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>503864075537828120240527</td>\n", "      <td>20240527</td>\n", "      <td>20240527</td>\n", "      <td>1</td>\n", "      <td>NaN</td>\n", "      <td>六一的风，已经吹动了多少个宝宝？5月27日-6月7日上饿了么搜「折个儿童节」，童年快乐不只6...</td>\n", "      <td>b2c2096f1d4ce21dec32d21f6894d11f</td>\n", "      <td>女</td>\n", "      <td>60岁以上</td>\n", "      <td>NaN</td>\n", "      <td>10000</td>\n", "      <td>\"[\"\"饿了么折个儿童节\"\"]\"</td>\n", "      <td>NaN</td>\n", "      <td>10000</td>\n", "      <td>常规图文</td>\n", "      <td>①C·饿了么\\n节\\n斤个心\\n上个我了么放心点\\ncococean\\n\\n童年快乐不只6....</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>503859033984486120240527</td>\n", "      <td>20240527</td>\n", "      <td>20240527</td>\n", "      <td>1</td>\n", "      <td>NaN</td>\n", "      <td>童年的纸飞机，现在终于飞回我手里！\\n亲爱的大朋友小朋友们，\\n5月27日-6月7日上饿了么...</td>\n", "      <td>a6332fd2b8428dbb54cc5b0836d5fd14</td>\n", "      <td>女</td>\n", "      <td>26-30岁</td>\n", "      <td>NaN</td>\n", "      <td>12000</td>\n", "      <td>\"[\"\"饿了么折个儿童节\"\"]\"</td>\n", "      <td>NaN</td>\n", "      <td>4000</td>\n", "      <td>常规图文</td>\n", "      <td>①C·饿了么\\n眉州东坡\\n\\n斤个儿\\n上个我了么放心点\\n眉州东坡\\n\\n童年快乐准时达...</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>503868055486634820240527</td>\n", "      <td>20240527</td>\n", "      <td>20240527</td>\n", "      <td>1</td>\n", "      <td>NaN</td>\n", "      <td>童年的纸飞机，现在终于飞回我手里！[纸飞机][纸飞机]\\n亲爱的大朋友小朋友们，5月27日-...</td>\n", "      <td>370b636aa0656c6ebc1ba3a14b86a8e4</td>\n", "      <td>男</td>\n", "      <td>60岁以上</td>\n", "      <td>NaN</td>\n", "      <td>9000</td>\n", "      <td>\"[\"\"饿了么折个儿童节\"\",\"\"花小新疆炒米粉\"\"]\"</td>\n", "      <td>NaN</td>\n", "      <td>7000</td>\n", "      <td>常规图文</td>\n", "      <td>①HUAXIAOXIAO\\n·饿了么\\n花\\n\\n\\n来一碗燃起来\\n节\\n斤个儿\\n上个我...</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>503858709319246220240527</td>\n", "      <td>20240527</td>\n", "      <td>20240527</td>\n", "      <td>1</td>\n", "      <td>NaN</td>\n", "      <td>#卡卡深夜落地贵阳#童年的纸飞机终于飞回我手里</td>\n", "      <td>252618759dd4f949000c1effd20c7dc0</td>\n", "      <td>女</td>\n", "      <td>31-40岁</td>\n", "      <td>贵阳</td>\n", "      <td>0</td>\n", "      <td>\"[\"\"卡卡深夜落地贵阳\"\"]\"</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>常规图文</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>503894276702418820240531</td>\n", "      <td>20240531</td>\n", "      <td>20240528</td>\n", "      <td>1</td>\n", "      <td>NaN</td>\n", "      <td>童年的纸飞机，现在终于飞回我手里！\\n亲爱的大朋友、小朋友们\\n5月27日-6月7日上@饿了...</td>\n", "      <td>52852e67be1ebfbf4c4051a55929fb25</td>\n", "      <td>女</td>\n", "      <td>60岁以上</td>\n", "      <td>NaN</td>\n", "      <td>230000</td>\n", "      <td>\"[\"\"饿了么折个儿童节\"\"]\"</td>\n", "      <td>NaN</td>\n", "      <td>1110000</td>\n", "      <td>常规图文</td>\n", "      <td>①C饿了么×棒!约翰\\n可和\\n节\\n个\\n上个饿了么放心点\\n棒约翰\\n童年快乐准时达\\n...</td>\n", "      <td>NaN</td>\n", "      <td>5</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "execution_count": 2}, {"metadata": {}, "cell_type": "markdown", "source": "# 数据清洗", "id": "b1782a09ad058ea6"}, {"metadata": {"ExecuteTime": {"end_time": "2025-04-15T09:36:53.361527Z", "start_time": "2025-04-15T09:36:49.685042Z"}}, "cell_type": "code", "source": ["df['publish_datetime'] = pd.to_datetime(df['publish_time'], format='%Y%m%d', errors='coerce')\n", "\n", "# 提取年、月、日、星期几 (0=周一, 6=周日)\n", "df['publish_year'] = df['publish_datetime'].dt.year\n", "df['publish_month'] = df['publish_datetime'].dt.month\n", "df['publish_day'] = df['publish_datetime'].dt.day\n", "df['publish_weekday'] = df['publish_datetime'].dt.weekday\n", "\n", "print(\"时间特征提取完成: 年, 月, 日, 星期已添加。\")\n", "print(\"处理后的DataFrame信息概览:\")\n", "print(df[['publish_year', 'publish_month', 'publish_day', 'publish_weekday']].info())\n", "\n", "# ----------------------------\n", "# 定义一个通用函数，判断日期是否落在给定区间内\n", "def in_holiday(date, start, end):\n", "    if pd.isnull(date):\n", "        return 0\n", "    return int(start <= date <= end)\n", "\n", "# 定义各促销/节假日期间（按照你提供的规则）\n", "# 双十一促销期：提前15天至活动后一周 → 2024-10-27 至 2024-11-18\n", "double11_start = pd.Timestamp('2024-10-27')\n", "double11_end   = pd.Timestamp('2024-11-18')\n", "\n", "# 618年中大促促销期：2024-06-01 至 2024-06-25\n", "_618_start = pd.Timestamp('2024-06-01')\n", "_618_end   = pd.Timestamp('2024-06-25')\n", "\n", "# 双十二促销期：2024-11-27 至 2024-12-19\n", "double12_start = pd.Timestamp('2024-11-27')\n", "double12_end   = pd.Timestamp('2024-12-19')\n", "\n", "# 国庆促销期：提前一周至活动后一周 → 2024-09-24 至 2024-10-10\n", "national_start = pd.Timestamp('2024-09-24')\n", "national_end   = pd.Timestamp('2024-10-10')\n", "\n", "# 定义函数，判断日期是否属于任一促销/节假日期间\n", "def is_holiday(date):\n", "    if pd.isnull(date):\n", "        return 0\n", "    if in_holiday(date, double11_start, double11_end):\n", "        return 1\n", "    if in_holiday(date, _618_start, _618_end):\n", "        return 1\n", "    if in_holiday(date, double12_start, double12_end):\n", "        return 1\n", "    if in_holiday(date, national_start, national_end):\n", "        return 1\n", "    return 0\n", "\n", "# 生成新的二值特征列：holiday\n", "df['holiday'] = df['publish_datetime'].apply(is_holiday)\n", "selected_cols = ['publish_datetime', 'holiday']\n", "\n", "df[selected_cols].head()"], "id": "fa78d2d5b2c4cb1d", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["时间特征提取完成: 年, 月, 日, 星期已添加。\n", "处理后的DataFrame信息概览:\n", "<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 585276 entries, 0 to 585275\n", "Data columns (total 4 columns):\n", " #   Column           Non-Null Count   Dtype\n", "---  ------           --------------   -----\n", " 0   publish_year     585276 non-null  int32\n", " 1   publish_month    585276 non-null  int32\n", " 2   publish_day      585276 non-null  int32\n", " 3   publish_weekday  585276 non-null  int32\n", "dtypes: int32(4)\n", "memory usage: 8.9 MB\n", "None\n"]}, {"data": {"text/plain": ["  publish_datetime  holiday\n", "0       2024-05-27        0\n", "1       2024-05-27        0\n", "2       2024-05-27        0\n", "3       2024-05-27        0\n", "4       2024-05-28        0"], "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>publish_datetime</th>\n", "      <th>holiday</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2024-05-27</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2024-05-27</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2024-05-27</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2024-05-27</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2024-05-28</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "execution_count": 3}, {"metadata": {"ExecuteTime": {"end_time": "2025-04-15T09:36:53.377601Z", "start_time": "2025-04-15T09:36:53.365226Z"}}, "cell_type": "code", "source": ["# --- 2. 清洗 fans_cnt, coin_cnt, age 等 ---\n", "\n", "# 定义清洗函数 (通用性考虑)\n", "def clean_numeric_categorical(value, less_than_map_value=50, nan_impute_value=0):\n", "    \"\"\"\n", "    清洗可能包含数字、'小于X'、NaN等混合格式的列。\n", "    :param value: 输入值\n", "    :param less_than_map_value: 将 '小于X' 映射到的数值 (例如 50)\n", "    :param nan_impute_value: 将 NaN 映射到的数值 (例如 0)\n", "    :return: 清洗后的数值\n", "    \"\"\"\n", "    if pd.isna(value):\n", "        return nan_impute_value\n", "    if isinstance(value, str):\n", "        # 处理 '小于X' 的情况，例如 '小于100'\n", "        if '小于' in value:\n", "            # 可以尝试提取数字，但简单起见，直接映射\n", "            return less_than_map_value\n", "        # 尝试直接转换字符串中的数字\n", "        try:\n", "            # 移除非数字字符，以防 '10000+' 这样的情况 (如果存在)\n", "            cleaned_value = re.sub(r'[^\\d.]', '', value)\n", "            if cleaned_value: # 确保清理后还有内容\n", "                 return float(cleaned_value)\n", "            else:\n", "                 return nan_impute_value # 如果清理后为空，也按NaN处理\n", "        except ValueError:\n", "            # 如果字符串不能转为数字且不是 '小于X' 格式\n", "            return nan_impute_value\n", "    # 如果本身就是数字类型\n", "    try:\n", "        return float(value)\n", "    except ValueError:\n", "        return nan_impute_value"], "id": "499cbebcb171684b", "outputs": [], "execution_count": 4}, {"metadata": {"ExecuteTime": {"end_time": "2025-04-15T09:36:54.667235Z", "start_time": "2025-04-15T09:36:53.379003Z"}}, "cell_type": "code", "source": ["# -- 清洗 'fans_cnt' --\n", "if 'fans_cnt' in df.columns:\n", "    print(\"清洗 'fans_cnt' 列...\")\n", "    # 对 '小于100' 映射为 50，NaN 映射为 0 (可以根据数据分布调整)\n", "    df['fans_cnt_cleaned'] = df['fans_cnt'].apply(lambda x: clean_numeric_categorical(x, less_than_map_value=50, nan_impute_value=0))\n", "    # 可以考虑进行对数变换，因为粉丝数往往分布不均\n", "    df['fans_cnt_log1p'] = np.log1p(df['fans_cnt_cleaned'])\n", "    print(\"'fans_cnt' 清洗完成，并添加了 log1p 变换列。\")\n", "    print(df[['fans_cnt', 'fans_cnt_cleaned', 'fans_cnt_log1p']].head())\n", "else:\n", "    print(\"警告: DataFrame 中未找到 'fans_cnt' 列。\")"], "id": "c25ef33bf7dc8da2", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["清洗 'fans_cnt' 列...\n", "'fans_cnt' 清洗完成，并添加了 log1p 变换列。\n", "  fans_cnt  fans_cnt_cleaned  fans_cnt_log1p\n", "0    10000           10000.0        9.210440\n", "1    12000           12000.0        9.392745\n", "2     9000            9000.0        9.105091\n", "3        0               0.0        0.000000\n", "4   230000          230000.0       12.345839\n"]}], "execution_count": 5}, {"metadata": {"ExecuteTime": {"end_time": "2025-04-15T09:36:55.728881Z", "start_time": "2025-04-15T09:36:54.668237Z"}}, "cell_type": "code", "source": ["# -- 清洗 'coin_cnt' --\n", "if 'coin_cnt' in df.columns:\n", "    print(\"清洗 'coin_cnt' 列...\")\n", "    # 同样处理 '小于100' 和 NaN\n", "    df['coin_cnt_cleaned'] = df['coin_cnt'].apply(lambda x: clean_numeric_categorical(x, less_than_map_value=50, nan_impute_value=0))\n", "    # 也可以考虑对数变换\n", "    df['coin_cnt_log1p'] = np.log1p(df['coin_cnt_cleaned'])\n", "    print(\"'coin_cnt' 清洗完成，并添加了 log1p 变换列。\")\n", "    print(df[['coin_cnt', 'coin_cnt_cleaned', 'coin_cnt_log1p']].head())\n", "else:\n", "    print(\"警告: DataFrame 中未找到 'coin_cnt' 列。\")"], "id": "10d83b2df4c664d1", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["清洗 'coin_cnt' 列...\n", "'coin_cnt' 清洗完成，并添加了 log1p 变换列。\n", "  coin_cnt  coin_cnt_cleaned  coin_cnt_log1p\n", "0    10000           10000.0        9.210440\n", "1     4000            4000.0        8.294300\n", "2     7000            7000.0        8.853808\n", "3      NaN               0.0        0.000000\n", "4  1110000         1110000.0       13.919871\n"]}], "execution_count": 6}, {"metadata": {"ExecuteTime": {"end_time": "2025-04-15T09:36:57.504613Z", "start_time": "2025-04-15T09:36:55.730014Z"}}, "cell_type": "code", "source": ["# -- 清洗 'age' --\n", "# 年龄数据格式为 '26-30岁', '60岁以上', NaN 等\n", "def clean_age(age_str):\n", "    \"\"\"\n", "    将年龄段字符串转换为数值表示 (例如，取中值)。\n", "    \"\"\"\n", "    if pd.isna(age_str):\n", "        return np.nan # 先保留 NaN，后续统一填充\n", "    if isinstance(age_str, str):\n", "        # 处理 'XX-XX岁' 格式\n", "        match = re.match(r'(\\d+)-(\\d+)岁', age_str)\n", "        if match:\n", "            min_age = int(match.group(1))\n", "            max_age = int(match.group(2))\n", "            return (min_age + max_age) / 2\n", "        # 处理 'XX岁以上' 格式\n", "        match_above = re.match(r'(\\d+)岁以上', age_str)\n", "        if match_above:\n", "            # 可以设定一个估计值，比如 65 或 70\n", "            return 65\n", "        # 处理 \"未知\" 等其他无法解析的字符串\n", "        return np.nan # 无法解析的视为 NaN\n", "    # 如果已经是数字，直接返回\n", "    if isinstance(age_str, (int, float)):\n", "        return float(age_str)\n", "    return np.nan # 其他情况视为 NaN\n", "\n", "if 'age' in df.columns:\n", "    print(\"清洗 'age' 列...\")\n", "    df['age_cleaned'] = df['age'].apply(clean_age)\n", "\n", "    # 对清洗后的 age 列中的 NaN 进行填充 (例如，用中位数填充)\n", "    age_median = df['age_cleaned'].median()\n", "    df['age_cleaned'].fillna(age_median, inplace=True)\n", "    print(f\"'age' 清洗完成，并将 NaN 填充为中位数 ({age_median:.2f})。\")\n", "    print(df[['age', 'age_cleaned']].head())\n", "    print(df['age_cleaned'].describe())\n", "else:\n", "    print(\"警告: DataFrame 中未找到 'age' 列。\")\n"], "id": "2da2e7fa31d12422", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["清洗 'age' 列...\n", "'age' 清洗完成，并将 NaN 填充为中位数 (65.00)。\n", "      age  age_cleaned\n", "0   60岁以上         65.0\n", "1  26-30岁         28.0\n", "2   60岁以上         65.0\n", "3  31-40岁         35.5\n", "4   60岁以上         65.0\n", "count    585276.000000\n", "mean         58.555364\n", "std          14.960968\n", "min           2.500000\n", "25%          65.000000\n", "50%          65.000000\n", "75%          65.000000\n", "max          65.000000\n", "Name: age_cleaned, dtype: float64\n"]}, {"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_35648\\2320303604.py:34: FutureWarning: A value is trying to be set on a copy of a DataFrame or Series through chained assignment using an inplace method.\n", "The behavior will change in pandas 3.0. This inplace method will never work because the intermediate object on which we are setting values always behaves as a copy.\n", "\n", "For example, when doing 'df[col].method(value, inplace=True)', try using 'df.method({col: value}, inplace=True)' or df[col] = df[col].method(value) instead, to perform the operation inplace on the original object.\n", "\n", "\n", "  df['age_cleaned'].fillna(age_median, inplace=True)\n"]}], "execution_count": 7}, {"metadata": {"ExecuteTime": {"end_time": "2025-04-15T09:37:08.435761Z", "start_time": "2025-04-15T09:36:57.505862Z"}}, "cell_type": "code", "source": ["#合并 'title' 和 'content' 列...\n", "# 用空字符串填充缺失值，然后在中间加个空格\n", "df['combined_text'] = df['title'].fillna('') + \" \" + df['content'].fillna('')\n", "\n", "# 选择合并后的文本列作为待处理列\n", "text_column = 'combined_text'\n", "print(f\"处理 '{text_column}' 列...\")\n", "\n", "# 填充空字符串，防止存在 NaN 值\n", "df[text_column].fillna(\"\", inplace=True)\n", "\n", "def clean_chinese_text(text):\n", "    \"\"\"针对营销内容的中文文本清洗\"\"\"\n", "    if not isinstance(text, str):\n", "        return \"\"\n", "    # 移除表情符号（如 [哭惹R]、[纸飞机]）\n", "    text = re.sub(r'\\[.*?\\]', '', text)\n", "    # 移除所有话题标签 #xxx# （保留话题词的话可以微调）\n", "    text = re.sub(r'#.*?#', '', text)\n", "    # 移除 @用户名 提及\n", "    text = re.sub(r'@\\S+', '', text)\n", "    # 移除网址链接\n", "    text = re.sub(r\"http\\S+|www\\.\\S+\", '', text)\n", "    # 移除换行符、制表符、回车符\n", "    text = re.sub(r'[\\n\\t\\r]', ' ', text)\n", "    # 移除多余空格\n", "    text = re.sub(r'\\s+', ' ', text).strip()\n", "    # 移除品牌名和商品名（形如【品牌名】+【商品名】），但保留内部内容\n", "    text = re.sub(r'【|】', '', text)\n", "    # 移除英文、数字、特殊符号，只保留中文和常见中文标点\n", "    text = re.sub(r\"[^\\u4e00-\\u9fa5！？。，；：、]\", '', text)\n", "    # 再次去除连续空格\n", "    text = re.sub(r'\\s+', '', text)\n", "    return text\n", "\n", "# 应用清洗函数，对合并后的文本进行处理，并保存到新列\n", "df[text_column + '_cleaned'] = df[text_column].apply(clean_chinese_text)\n", "\n", "print(\"文本预处理完成！\")\n", "print(df[[text_column, text_column+'_cleaned']].head())\n"], "id": "2655fa50bc5c2e83", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["处理 'combined_text' 列...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_35648\\2118303312.py:10: FutureWarning: A value is trying to be set on a copy of a DataFrame or Series through chained assignment using an inplace method.\n", "The behavior will change in pandas 3.0. This inplace method will never work because the intermediate object on which we are setting values always behaves as a copy.\n", "\n", "For example, when doing 'df[col].method(value, inplace=True)', try using 'df.method({col: value}, inplace=True)' or df[col] = df[col].method(value) instead, to perform the operation inplace on the original object.\n", "\n", "\n", "  df[text_column].fillna(\"\", inplace=True)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["文本预处理完成！\n", "                                       combined_text  \\\n", "0   六一的风，已经吹动了多少个宝宝？5月27日-6月7日上饿了么搜「折个儿童节」，童年快乐不只...   \n", "1   童年的纸飞机，现在终于飞回我手里！\\n亲爱的大朋友小朋友们，\\n5月27日-6月7日上饿了...   \n", "2   童年的纸飞机，现在终于飞回我手里！[纸飞机][纸飞机]\\n亲爱的大朋友小朋友们，5月27日...   \n", "3                            #卡卡深夜落地贵阳#童年的纸飞机终于飞回我手里   \n", "4   童年的纸飞机，现在终于飞回我手里！\\n亲爱的大朋友、小朋友们\\n5月27日-6月7日上@饿...   \n", "\n", "                               combined_text_cleaned  \n", "0  六一的风，已经吹动了多少个宝宝？月日月日上饿了么搜折个儿童节，童年快乐不只折！放心点整颗椰子...  \n", "1  童年的纸飞机，现在终于飞回我手里！亲爱的大朋友小朋友们，月日月日上饿了么搜折个儿童节，放心点...  \n", "2  童年的纸飞机，现在终于飞回我手里！亲爱的大朋友小朋友们，月日月日上饿了么搜折个儿童节，放心点...  \n", "3                                      童年的纸飞机终于飞回我手里  \n", "4  童年的纸飞机，现在终于飞回我手里！亲爱的大朋友、小朋友们月日月日上搜折个儿童节放心点棒约翰起...  \n"]}], "execution_count": 8}, {"metadata": {}, "cell_type": "markdown", "source": "# EDA", "id": "f7a2bf6e8c91092b"}, {"metadata": {"jupyter": {"is_executing": true}, "ExecuteTime": {"start_time": "2025-04-15T09:37:08.437222Z"}}, "cell_type": "code", "source": ["# 指定要分析的数值变量\n", "num_cols = ['video_cnt', 'interaction_cnt', \n", "            'publish_day', 'publish_month', 'fans_cnt_cleaned', 'coin_cnt_cleaned', 'age_cleaned']\n", "\n", "for col in num_cols:\n", "    plt.figure(figsize=(12, 4))\n", "    \n", "    # 绘制直方图及核密度估计\n", "    plt.subplot(1, 2, 1)\n", "    sns.histplot(df[col].dropna(), color='skyblue')\n", "    plt.title(f\"{col} Distribution\")\n", "    \n", "    # 绘制箱线图判断异常值情况\n", "    plt.subplot(1, 2, 2)\n", "    sns.boxplot(x=df[col].dropna(), color='lightgreen')\n", "    plt.title(f\"{col} Boxplot\")\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "# 4. 对所有类别型变量绘制计数图\n", "cat_cols = df.select_dtypes(include=['object']).columns.tolist()\n", "print(\"分类变量：\", cat_cols)\n", "\n", "for col in cat_cols:\n", "    plt.figure(figsize=(8, 4))\n", "    # 使用 value_counts 排序，较多类别先显示\n", "    order = df[col].value_counts().index\n", "    sns.countplot(data=df, x=col, order=order, palette='pastel')\n", "    plt.title(f\"{col} Value Counts\")\n", "    plt.xticks(rotation=45)\n", "    plt.tight_layout()\n", "    plt.show()"], "id": "331b0edc82c92245", "outputs": [], "execution_count": null}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": "", "id": "7f188c0146f7bdd3"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": "", "id": "679e9b76ea1c33e4"}, {"metadata": {"ExecuteTime": {"end_time": "2025-04-12T14:26:12.833499Z", "start_time": "2025-04-12T14:25:19.288217Z"}}, "cell_type": "code", "source": ["\n", "\n", "# 3.2 中文分词函数\n", "def chinese_tokenizer(text):\n", "    \"\"\"使用 jieba 进行中文分词\"\"\"\n", "    # 可以在这里加载自定义词典 jieba.load_userdict('path/to/your/dict.txt')\n", "    return list(jieba.cut(text))\n", "\n", "def load_stopwords(filepath=\"StopWords.txt\"):\n", "    with open(filepath, \"r\", encoding=\"utf-8\") as f:\n", "        stopwords = [line.strip() for line in f if line.strip()]\n", "    return stopwords\n", "\n", "stopwords = load_stopwords(\"StopWords.txt\")\n", "\n", "# 应用清洗 (如果需要) - 注意：清洗可能影响 TF-IDF 效果，按需使用\n", "df[f'{text_column}_cleaned'] = df[text_column].apply(clean_chinese_text)\n", "text_to_process = df[f'{text_column}_cleaned'] # 使用清洗后的文本\n", "#text_to_process = df[text_column] # 或者直接使用原始填充NaN后的文本\n", "# 1. 预处理：去掉content中的空值\n", "corpus = text_to_process\n", "\n", "# 3. 分词并统计词频\n", "all_tokens = []\n", "for text in corpus:\n", "    tokens = chinese_tokenizer(text)\n", "    tokens = [token for token in tokens if token not in stopwords and len(token) > 1]\n", "    all_tokens.extend(tokens)\n", "\n", "counter = Counter(all_tokens)\n", "\n", "# 4. 打印最常见的前20个词\n", "print(\"最常见的20个词：\")\n", "for word, freq in counter.most_common(20):\n", "    print(f\"{word}: {freq}\")\n", "\n", "# 5. 画出词频分布图\n", "def plot_word_freq(counter, top_n=50):\n", "    most_common = counter.most_common(top_n)\n", "    words = [word for word, freq in most_common]\n", "    freqs = [freq for word, freq in most_common]\n", "\n", "    plt.figure(figsize=(12, 6))\n", "    plt.bar(range(len(words)), freqs)\n", "    plt.xticks(range(len(words)), words, rotation=60, ha='right')\n", "    plt.xlabel(\"词语\")\n", "    plt.ylabel(\"出现次数\")\n", "    plt.title(f\"Top {top_n} 词频分布图\")\n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "plot_word_freq(counter, top_n=50)\n", "\n", "# 6. 自动建议 max_features\n", "def suggest_max_features(counter, coverage_ratio=0.9):\n", "    \"\"\"\n", "    根据词频分布，自动建议 max_features\n", "    coverage_ratio: 需要覆盖的总词频比例，默认90%\n", "    \"\"\"\n", "    total_freq = sum(counter.values())\n", "    sorted_freqs = np.array(sorted(counter.values(), reverse=True))\n", "    cumulative_freq = np.cumsum(sorted_freqs)\n", "    coverage_point = np.argmax(cumulative_freq >= coverage_ratio * total_freq)\n", "    suggested_features = coverage_point + 1  # 因为index从0开始\n", "    return suggested_features\n", "\n", "suggested_features = suggest_max_features(counter)\n", "print(f\"\\n✅ 建议设置 max_features ≈ {suggested_features}\")\n"], "id": "eeb8dae9924c7b7e", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["开始处理文本特征...\n", "处理 'content' 列...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_22604\\1190864957.py:17: FutureWarning: A value is trying to be set on a copy of a DataFrame or Series through chained assignment using an inplace method.\n", "The behavior will change in pandas 3.0. This inplace method will never work because the intermediate object on which we are setting values always behaves as a copy.\n", "\n", "For example, when doing 'df[col].method(value, inplace=True)', try using 'df.method({col: value}, inplace=True)' or df[col] = df[col].method(value) instead, to perform the operation inplace on the original object.\n", "\n", "\n", "  df[text_column].fillna(\"\", inplace=True) # 用空字符串填充 NaN\n", "Building prefix dict from the default dictionary ...\n", "Loading model from cache C:\\Users\\<USER>\\AppData\\Local\\Temp\\jieba.cache\n", "Loading model cost 0.290 seconds.\n", "Prefix dict has been built successfully.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["最常见的20个词：\n", "一起: 148095\n", "轻松: 122838\n", "送礼: 122241\n", "疯省: 97276\n", "秘籍: 97072\n", "亲子鉴定: 92641\n", "免单: 36862\n", "奶茶: 31463\n", "吃货: 23686\n", "确认: 19084\n", "中山: 18957\n", "秋天: 18577\n", "机构: 18420\n", "鉴定: 18056\n", "第一杯: 17970\n", "中国: 16379\n", "费用: 15395\n", "超级: 15349\n", "外卖: 15242\n", "司法: 14587\n"]}, {"data": {"text/plain": ["<Figure size 1200x600 with 1 Axes>"], "image/png": "iVBORw0KGgoAAAANSUhEUgAABKUAAAJOCAYAAABm7rQwAAAAOnRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjEwLjAsIGh0dHBzOi8vbWF0cGxvdGxpYi5vcmcvlHJYcgAAAAlwSFlzAAAPYQAAD2EBqD+naQAAv9tJREFUeJzs3Qm8ndO9+P+ViARVkkhLYwxCuabbSG4uP7NWi0QNbQ1tFWmpKrdISRWlSGiNLSmCtMZLykVJzUNptJUSUrPSqhQxNBIhMZz/67Pu/3vuc3b23mefIU9Ocj7v1+uQc/Y5z36GNXzX91nP2j2ampqakiRJkiRJklSinmW+mSRJkiRJkgSTUpIkSZIkSSqdSSlJkiRJkiSVzqSUJEmSJEmSSmdSSpIkSZIkSaUzKSVJkiRJkqTSmZSSJEmSJElS6UxKSZIkSZIkqXQmpSRJkiRJklQ6k1KSJEld2HPPPZe22267NGfOnPz9hx9+mObNm5eampravK077rgj/elPf+rwPv3jH/9o/vesWbPSz372s7xPbfXVr341HXnkkQ397nvvvZf+9a9/tenr7bffbvM+SZKk8vQq8b0kSZLURgMHDkzPP/98+tGPfpR++tOfpt/97nc5SVXPpz71qTRjxowFfk7yaKONNkpDhw7N3x977LFp7ty5qXfv3i1+7913303Dhw9PX/va1xbYxmuvvZbWX3/9dN5556WDDjooJ6OOOeaYtMwyy6RvfvObDR8XSaNf//rX6aijjmro988444x04oknprYYMGBAmjlzZpv+RpIklceZUpIkabFy7733ph49elT9InFTpjXXXHOBfZgwYULz62+88Ubae++904orrpj+7d/+Le97Wy233HLp5JNPTpdcckl655130n/+53+mf/7zn3nbb7311gJf559/fk4QhSeeeCL16tUrJ5OWXnrpPNOKv+X71VdfPQ0aNCitttpq6cwzz0zLLrts/tnaa6+dEzrVnH322Xl7X/ziF/P3n/zkJ9Nhhx2Wjj/++PT6669X/RuSak8//XT661//ml588cX8RVLr/fffT7vttlvzz+LrhRdeSM8++2x68sknm7fBew4ZMiTPEIuvBx54IJ+X+fPnt/g5X5dffvkCyTZJktS1OFNKkiQtVkhMxCNoJ510UnrmmWfSlVde2TyrqCwkYP7+97+n3/zmN2nllVdu/vlaa63V/O899tgjJ1gmTpyYEzIjRoxIjz32WE4EtQUzlrbYYov0sY99LM9iisf3SIIFEjwf//jHcxKrZ8//u+9IYoZEVJ8+ffLvn3vuuemCCy7ICbXp06fn3/nb3/6WRo8enRNL9RI5L730Uk56ff/7308rrbRS88+PO+64fA2+9a1v5dlPxf3CwQcfnO66666q2xw2bFjN92MfSVKBhFoRx88+cx123nnn5kTcUkstlc9D/FuSJHVdJqUkSdJihYTD5ptvnv9NYoTZPfF9mf785z+n5ZdfPidEKpMwuP3229P999+f/vCHPzQnXqZNm5YfwSOx0xYkV9Zbb738b7a3/fbb5yRN8X0/+OCDvO2+ffsu8LeIRBWP7MWMMmZW3Xjjjempp55K/fr1S1dddVXz333+859Pq6yySott8Xhe//79F3jkjplgzFjiXHzve9/Ls6mK+3bzzTfn/WC2E/vB+5MY432ZsXXNNdekE044oUUyrVJlUurCCy9MU6ZMaX5cMRx66KHN57fe9iRJ0qJnTy1JktQOU6dOTZ/5zGeqJqTAzKB11123xUwgHnm78847G34PEkOrrrpqfryOZAu22Wab5sXOWfw7vkhKHX744QvMJmIGFYr7yc/5+1dffTUdcMAB+VE5El2//e1v8xdrRbHAeuV6VCyUzqwvEoHV9vUXv/hF/r299torJ7wCv88MLJJEJKLGjRuXzjrrrJzgYibZj3/84zYlkEjMkfwiGfXyyy/n4yERxgy00047reHtSJKkRcuklCRJWiLdeuutaZNNNsmPrW266aY52RKYqcOC3z/4wQ/yzKI11lgjnXrqqemjjz5q00wpHmcjEULSheTTbbfd1vw6yRLev4i1mkjCkFRqxKhRo/J6UjwSGJ8kF2tXVVOZ2CHpxKLkMZvpf/7nf9Ipp5ySZyzxuBvJKDDLidlK8cWspOK6VPwdSSAewyORxFpPJJcqv7beeuu8NtXkyZPThhtu2OJ8gGPYfffd07bbbpu/+BQ/Fjxnf/h38avWJ+eRZDvwwAPTIYcckr7+9a/n9xw7dmz+YtYXxylJkhYPJqUkSdIS5+67787rN7EOE8kpFgffddddWyw0zgLg/N61116bZxiRqPr5z3/e8Hv88Y9/TCussEKemUPShgW/eU+SM2Dtp8pH6Xjcj6QKiZhGMOOIWUvMuCqu9fTDH/5wgQXWr7jiigX+nrWuWAScJBhf7OuvfvWr/G9mVpGYC1/+8pfzrKlKPF5HcmzffffNa3aR4CPhtMEGG1T9YkHz3//+9/lxQ2aSBT7lj4QU54dkFQuq88V22cf4Pr6Y+VQNCTOuIzOt+NvZs2fn5CIztYqP8UmSpK7PpJQkSVrisAD6lltumR8n22GHHfL/+Z6fBxI5zAr63Oc+l44++uic4ODRs0YxK+e+++5L++yzT9ppp51yYorH7C6++OL8OjO0KhfajhlOJKzaqjgLiplZ+++/f/Mnzf3Hf/xHi5lNxfcjiUPy6brrrsufVkcyjsXDmRlGQieQLGNblUi2sbD8pZdemhc4J4EUibeHHnqoxSfeMROMhNJmm22Wz80nPvGJ/HssRs55/stf/pJGjhyZv3g/vkhQca7ie744Hn5WDTPQWFT9C1/4Qr6mJMxIUjFrjfPPulY+widJ0uLBpJQkSVriPPzww3mNpKIdd9yx+VP7wFpNxU/KGzp0aE7WMIOoESReio+K8Qgaj/A9+uijzckcHkMreuONN/L/+RS9jmjLp8r97ne/y8fF+WC2Fft5/fXX58QOM7eKCSzOSTU8osjf8fckuUgukSRjxlTRjBkzcmKoiMQX54U1qlhPa/Dgwfm92F4sfI74nq/4vhoScnzCH8lGZmVxLD/5yU/SEUccke655560zjrrLLAouiRJ6ppMSkmSpCVOtRk/lT+v/B3WkyJZ0siC2++8805e9LsSSScWHQePufGIXyw0Houjk1SpfKyvPZixxGOAfPE4Xq31sE4//fQ8q4pPKgQLkJPEOfLII1ucg5kzZ+bH80K9da+YFcYnHvJJiJXHzkypIn6H2WN8EmHxccH2Yk0rEm2jR4/OiT8SVCTT9t5773w8zHbjNUmS1PWZlJIkSUscZj0xa6aI9aP4efExsOInzPGJbjx+1khSikfFeGSPR+ECC5iTLBk+fHj+nvWlmCUUj/ORRIrHCWstVN6aBx98MI0fPz4njK6++urUr1+//MXMsGLyq4gZUXy6XeDvma3EYuFsZ80118yfxMfC7cz+CpFcq8RMMB57ZK2rIs4nKpNS4JhZYwqNLCbfyO+wEDprSPFIITOxHn/88byGmCRJWnyYlJIkSUucE044Ia+fdOihh+ZkFP8nYcRi5sVH4FgP6vbbb09nnHFG+u///u/8CFgjPv3pT+fHAb/4xS+m888/P51zzjn50+R4LO+//uu/8u8MGDAgHX/88XmbLCJO4oTH3ti3tmIGEEkj1lGaNWtWTkBVrilVKynFI4bsy/PPP58TNyRyJk2alB9xI1HEo30ky1gUnZlMzDwiIbXNNtsssK1p06alXXbZJQ0ZMmSBpBSP0qHy8b1KJMBq4VE8Zjk99thjzetR1Zq5xaOHN998c74WLPz+b//2b/laFrXl0xQlSVL5qj+sL0mStBhjZg4Ji2OPPTZNmDAhJy5+85vf5MRRWH/99fNspq985Ss5QcXvHnzwwQ2/BzOVvvvd76YxY8bkBA9JqlNOOaXFTCGSJXx/4YUX5sQQi38XZ2s16plnnkmPPPJIOvfcc/MnBTJDqziTiVlhsY5S5SLqzOq65JJL0k9/+tP8SCELnvPYWyRtSKiNHTs2LzhOMugb3/hGuuWWW3KyjUcNMWfOnHwMJ598cl6Hi0XeY12rm266Kc/gYrtsv9qC6/WSUuxDPEbIY5EXXHBBnoVGwq+Itb742+LsNs7pIYcckhc7nzx5clpjjTWaX3/11VcbXh9MkiQtGj2aai26IEmStIRixhSzhaZPn566OtaMYqHxo446Kie5amH9KhJLv/zlL3NyhhliJKRI0LHeE58wyFckr2644YZ04oknpr///e/p2muvzZ+OB2Zz7brrrnmdJtaB4nFG1qU67rjj0ne+8538qX3FBdKZgbbHHnvkR+fGjRuXNthggzYdH8kk3o+ZUHzxftUeb+QT9diHtmC21Wuvvdamv5EkSeVxppQkSVIXxqLoU6ZMybO96mHW0EUXXZS22mqr5jWkmKXF7CySSMVFyUGy6qGHHsqPFxYXOOcxOP6GxwVjfa1jjjkm7bnnnmnddddd4H15PxZJby9mPzFbillNtT5xD7zOY4Osn9WIK664Is8qkyRJXZczpSRJktTlvfnmm2nu3LmtrlklSZIWHyalJEmSJEmSVDo/fU+SJEmSJEmlMyklSZIkSZKk0pmUkiRJkiRJUun89L0O4JNiZsyYkT/NptpHF0uSJEmSJHU3TU1Nafbs2fkTfuPTfKsxKdUBJKT4qGVJkiRJkiS19NJLL9X95FyTUh3ADKk4ySussMKi3h1JkiRJkqRF7u23386TeCJvUotJqQ6IR/ZISJmUkiRJkiRJ+j+tLXXkQueSJEmSJEkqnUkpSZIkSZIklc6klCRJkiRJkkpnUkqSJEmSJEmlMyklSZIkSZKk0pmUkiRJkiRJUulMSkmSJEmSJKl0JqUkSZIkSZJUOpNSkiRJkiRJKp1JKUmSJEmSJJXOpJQkSZIkSZJKZ1JKkiRJkiRJpTMpJUmSJEmSpNKZlJIkSZIkSVLpTEpJkiRJkiSpdCalJEmSJEmSVDqTUpIkSZIkSSqdSSlJkiRJkiSVzqSUJEmSJEmSSter/LdUV7TWsbd06O9fHLdLp+2LJEmSJEla8jlTSpIkSZIkSaUzKSVJkiRJkqTSmZSSJEmSJElS6UxKSZIkSZIkqXQmpSRJkiRJklQ6k1KSJEmSJEkqnUkpSZIkSZIklc6klCRJkiRJkkpnUkqSJEmSJEmlMyklSZIkSZKk0pmUkiRJkiRJUulMSkmSJEmSJKl0JqUkSZIkSZJUOpNSkiRJkiRJKp1JKUmSJEmSJJXOpJQkSZIkSZJKZ1JKkiRJkiRJpTMpJUmSJEmSpNKZlJIkSZIkSVL3Skq9/vrradCgQenFF1+s+vrnP//5NHHixObv77vvvrTBBhukAQMGpLPOOqvF706aNCmtueaaaeDAgenqq69u8dr555+fVl555bT22munu+++u8Vrxx13XOrXr1/aZJNN0mOPPdapxydJkiRJkqQulpQiIbXrrrvWTEhdeeWV6bbbbmv+fubMmWnkyJFpn332SVOmTMmv33PPPfm16dOnp/322y8df/zx+W9OOOGE9PTTT+fX+P7oo49OF110UbriiivSqFGj0htvvJFfu/DCC/PXTTfdlE455ZS09957p/nz55dy/JIkSZIkSd3ZIktKkQDad999q7725ptvpqOOOiqtv/76zT8jCcUsKBJPgwcPzomnSy65JL82YcKEtN122+WE08Ybb5wOO+ywdPnll+fXxo8fn/bff/+02267pS222CL//4Ybbmh+jYTVVlttlRNevN/9999fyvFLkiRJkiR1Z4ssKXXxxRenww8/vOprJKR23333NHz48OafTZs2LSeeevTokb8fNmxYmjp1avNr22+/ffPvNvJaU1NTevzxx2v+nSRJkiRJkpbApBRrSVXDI3l33XVXOuOMM1r8/O23327xNyussEKaMWNGu1+bM2dO+uijj2r+XTXz5s3L2yt+SZIkSZIkaTH/9L333nsvHXzwwfmxuo9//OMtXuvVq1fq06dP8/fLLLNMmjt3brtf4+eo9XfVjB07Nq244orNX6uvvnqnHLckSZIkSVJ306WSUj/+8Y/T0KFD0y677LLAa/3798+LnYfZs2en3r17t/u1ZZddNn/V+rtqxowZk2bNmtX89dJLL3XCUUuSJEmSJHU//ztdqIu46qqrcpKob9+++XtmLV177bXpj3/8Y05W8Xp45JFH0qqrrpr/zWt8It9BBx1U87Uddthhgdc233zz/No666zT/FpxcfVKzKoqzqySJEmSJEnSEjBT6ne/+12aPn16evTRR/MXn4h38skn5y/+/eCDD6Y777wzvf/++3nNqZ122in/3Z577pmuueaavHA5a0Wdd955za/ttdde6YILLkgvv/xyevXVV/Mn9hVfO/300/PaUM8880yaNGlS82uSJEmSJEnqJjOlVltttRbfL7/88mnAgAH5C2effXbaeeed88+ZTTVx4sT880033TQdccQReeYT60INHjw4HXroofm1ESNGpOuuuy7/DMyY2mOPPfK/Wb/qxhtvzO/LIuajRo1KQ4YMKfmoJUmSJEmSup8eTU1NTWkx8sILL6SnnnoqbbXVVjk5VfTEE0/kGVHbbLPNAmtD/elPf0rvvPNOfq1Hjx7NP+cT+JiBxWN5w4YNa9O+MMOKBc9ZX4pP7lucrXXsLR36+xfHLbgOmCRJkiRJ6n7ebjBf0qVmSjVi0KBB+auaDTfcMH9Vw9pS1fTs2TMnuCRJkiRJktRN15SSJEmSJElS92BSSpIkSZIkSaUzKSVJkiRJkqTSmZSSJEmSJElS6UxKSZIkSZIkqXQmpSRJkiRJklQ6k1KSJEmSJEkqnUkpSZIkSZIklc6klCRJkiRJkkpnUkqSJEmSJEmlMyklSZIkSZKk0pmUkiRJkiRJUulMSkmSJEmSJKl0JqUkSZIkSZJUOpNSkiRJkiRJKp1JKUmSJEmSJJXOpJQkSZIkSZJKZ1JKkiRJkiRJpTMpJUmSJEmSpNKZlJIkSZIkSVLpTEpJkiRJkiSpdCalJEmSJEmSVDqTUpIkSZIkSSqdSSlJkiRJkiSVzqSUJEmSJEmSSmdSSpIkSZIkSaUzKSVJkiRJkqTSmZSSJEmSJElS6UxKSZIkSZIkqXQmpSRJkiRJklQ6k1KSJEmSJEkqnUkpSZIkSZIklc6klCRJkiRJkkpnUkqSJEmSJEmlMyklSZIkSZKk0pmUkiRJkiRJUulMSkmSJEmSJKl0JqUkSZIkSZJUOpNSkiRJkiRJKp1JKUmSJEmSJJXOpJQkSZIkSZJKZ1JKkiRJkiRJpTMpJUmSJEmSpNKZlJIkSZIkSVLpepX/luoO1jr2lg79/Yvjdum0fZEkSZIkSV2PM6UkSZIkSZJUOmdKabHgzCtJkiRJkpYszpSSJEmSJElS90pKvf7662nQoEHpxRdfbP7ZjTfemNZee+3Uq1evtNlmm6Unn3yy+bXp06enoUOHpn79+qXRo0enpqam5tfuu+++tMEGG6QBAwaks846q8X7TJo0Ka255ppp4MCB6eqrr27x2vnnn59WXnnl/J533333Qj1eSZIkSZIkLeKkFAmpXXfdtUVC6vnnn08HHHBAGjduXHr55ZfTeuutl0aNGpVfmzdvXhoxYkQaMmRIevjhh9MTTzyRJk6cmF+bOXNmGjlyZNpnn33SlClT0pVXXpnuueee5kTWfvvtl44//vh02223pRNOOCE9/fTT+TW+P/roo9NFF12Urrjiivxeb7zxxiI5H5IkSZIkSd3JIktK7b333mnfffdt8TNmRZGQ+vKXv5xnL337299OjzzySH5t8uTJadasWXkW1DrrrJNOO+20dMkll+TXSEIxC4rE0+DBg3PiKV6bMGFC2m677XLCaeONN06HHXZYuvzyy/Nr48ePT/vvv3/abbfd0hZbbJH/f8MNN5R+LiRJkiRJkrqbRZaUuvjii9Phhx/e4mfMnPrWt77V/D0zmkgyYdq0aWn48OFpueWWy99vsskmebZUvEbiqUePHvn7YcOGpalTpza/tv322zdvs9HXqmG21ttvv93iS5IkSZIkSYtRUoq1pOqZP39+OvPMM9MhhxySvycBVPwbElBLLbVUeuuttxZ4bYUVVkgzZsyo+neNvlbN2LFj04orrtj8tfrqq7fr2CVJkiRJkrq7LvvpeyeeeGL62Mc+1rymFAuf9+nTp8XvLLPMMmnu3LkLvBY/r/Z3jb5WzZgxY/IjhPH10ksvdeIRS5IkSZIkdR+9UhfEp+DxqXgPPfRQWnrppfPP+vfvnxctL5o9e3bq3bt3fo3Fzit/Hn/XnteqIYFVmRiTJEmSJEnSEjBT6oUXXsifokdSasMNN2z++dChQ/Mn6xV/jzWeSCxVvsbi6KuuumrVv2v0NUmSJEmSJHWTpNS7776bFzvnU/B23333NGfOnPzV1NSUtt5667wG1GWXXZZ/l0/f23HHHfO6UiNHjkwPPvhguvPOO9P777+fzjjjjLTTTjvl39tzzz3TNddckx5//PG8rfPOO6/5tb322itdcMEF6eWXX06vvvpq/sS+eE2SJEmSJEnd5PG922+/PX+iHl98Ol9xVtRaa62VJkyYkGdRjR49OvXs2TPde++9+fUBAwaks88+O+28885p+eWXT3379k0TJ07Mr2266abpiCOOSJtvvnleM4pP8zv00EPzayNGjEjXXXdd8yf87bDDDmmPPfZYJMcuSZIkSZLUnfRoYhrSYuSVV15JU6dOTcOHD08rrbRSi9dIXj311FNpq622ysmpIhJdzIjaZpttFlg36k9/+lN655138mt8ql+jmLnFp/Cx6Dmf3Lc4W+vYWzr09y+O22Wx2p4kSZIkSVo4Gs2XdKmZUo1YZZVV0i67VE8wDBo0KH9Vw/pUxTWqilhbSpIkSZIkSd10TSlJkiRJkiR1DyalJEmSJEmSVDqTUpIkSZIkSSqdSSlJkiRJkiSVzqSUJEmSJEmSSmdSSpIkSZIkSaUzKSVJkiRJkqTSmZSSJEmSJElS6UxKSZIkSZIkqXQmpSRJkiRJklQ6k1KSJEmSJEkqnUkpSZIkSZIklc6klCRJkiRJkkpnUkqSJEmSJEmlMyklSZIkSZKk0pmUkiRJkiRJUulMSkmSJEmSJKl0JqUkSZIkSZJUOpNSkiRJkiRJKp1JKUmSJEmSJJXOpJQkSZIkSZJKZ1JKkiRJkiRJpTMpJUmSJEmSpNKZlJIkSZIkSVLpTEpJkiRJkiSpdCalJEmSJEmSVDqTUpIkSZIkSSqdSSlJkiRJkiSVzqSUJEmSJEmSSmdSSpIkSZIkSaUzKSVJkiRJkqTSmZSSJEmSJElS6UxKSZIkSZIkqXQmpSRJkiRJklQ6k1KSJEmSJEkqnUkpSZIkSZIklc6klCRJkiRJkkpnUkqSJEmSJEmlMyklSZIkSZKk0pmUkiRJkiRJUulMSkmSJEmSJKl0JqUkSZIkSZJUOpNSkiRJkiRJKp1JKUmSJEmSJJXOpJQkSZIkSZJKZ1JKkiRJkiRJpTMpJUmSJEmSpNKZlJIkSZIkSVL3Skq9/vrradCgQenFF19s/tn06dPT0KFDU79+/dLo0aNTU1NT82v33Xdf2mCDDdKAAQPSWWed1WJbkyZNSmuuuWYaOHBguvrqq1u8dv7556eVV145rb322unuu+9u8dpxxx2X32uTTTZJjz322EI7VkmSJEmSJHWBpBQJqV133bVFQmrevHlpxIgRaciQIenhhx9OTzzxRJo4cWJ+bebMmWnkyJFpn332SVOmTElXXnlluueee5oTWfvtt186/vjj02233ZZOOOGE9PTTT+fX+P7oo49OF110UbriiivSqFGj0htvvJFfu/DCC/PXTTfdlE455ZS09957p/nz5y+S8yFJkiRJktSdLLKkFAmgfffdt8XPJk+enGbNmpVnQa2zzjrptNNOS5dcckl+jSQUs6BIPA0ePDgnnuK1CRMmpO222y4nnDbeeON02GGHpcsvvzy/Nn78+LT//vun3XbbLW2xxRb5/zfccEPzaySsttpqq5zwWn/99dP9999f+rmQJEmSJEnqbhZZUuriiy9Ohx9+eIufTZs2LQ0fPjwtt9xy+XseqWO2VLxG4qlHjx75+2HDhqWpU6c2v7b99ts3b6eR13gs8PHHH6/5d9Uwk+vtt99u8SVJkiRJkqTFKCnFWlKVSPIUf04CaqmllkpvvfXWAq+tsMIKacaMGVX/rpHX5syZkz766KOaf1fN2LFj04orrtj8tfrqq3foHEiSJEmSJHVXXerT93r16pX69OnT4mfLLLNMmjt37gKvxc+r/V0jr/Fz1Pq7asaMGZMfL4yvl156qVOOW5IkSZIkqbv538xMF9G/f/+8aHnR7NmzU+/evfNrLHZe+fP4u7a+tuyyy+YvXmOGVOXfVUMCqzJpJkmSJEmSpMV8ptTQoUPzJ+uFF154Ia/jRGKp8rVHHnkkrbrqqlX/rtHXNt9885qvSZIkSZIkqZskpbbeeuu8BtRll12Wv+fT93bccce8rhSfjvfggw+mO++8M73//vvpjDPOSDvttFP+vT333DNdc801eeFy1oo677zzml/ba6+90gUXXJBefvnl9Oqrr+ZP7Cu+dvrpp+f3fOaZZ9KkSZOaX5MkSZIkSVI3eXyPdZ4mTJiQ9tlnnzR69OjUs2fPdO+99+bXBgwYkM4+++y08847p+WXXz717ds3TZw4Mb+26aabpiOOOCLPfGJdqMGDB6dDDz00vzZixIh03XXX5Z9hhx12SHvssUf+98EHH5xuvPHGtNpqq+UZWaNGjUpDhgxZZMcvSZIkSZLUXfRoampqSl3MK6+8kqZOnZqGDx+eVlpppRav8UjfU089lbbaaqucnCp64okn8oyobbbZZoG1of70pz+ld955J7/Gp/oFPoGPGVisFTVs2LA27SczrPgUPhY9j3WpFldrHXtLh/7+xXG7LFbbkyRJkiRJC0ej+ZIuNVMqrLLKKmmXXaonEQYNGpS/qtlwww3zVzWsLVUNs7FIcEmSJEmSJKk8XTIpJS1szrySJEmSJGnRMikldQKTXJIkSZIkLcafvidJkiRJkqTuwaSUJEmSJEmSSmdSSpIkSZIkSaUzKSVJkiRJkqTSmZSSJEmSJElS6UxKSZIkSZIkqXQmpSRJkiRJklQ6k1KSJEmSJEkqnUkpSZIkSZIklc6klCRJkiRJkkpnUkqSJEmSJEmlMyklSZIkSZKk0vUq/y0ltWatY2/p0N+/OG6XTtsXSZIkSZIWBmdKSZIkSZIkqXQmpSRJkiRJklQ6k1KSJEmSJEkqnUkpSZIkSZIklc6klCRJkiRJkkpnUkqSJEmSJEmlMyklSZIkSZKk0pmUkiRJkiRJUulMSkmSJEmSJKl0JqUkSZIkSZJUOpNSkiRJkiRJKp1JKUmSJEmSJJXOpJQkSZIkSZJKZ1JKkiRJkiRJpTMpJUmSJEmSpNKZlJIkSZIkSVLpTEpJkiRJkiSpdCalJEmSJEmSVDqTUpIkSZIkSer6SakPPvggTZw4se7v3H///Wn+/Pkd2S9JkiRJkiQtwXq19Q969OiRjjzyyPTmm2+mgQMHpvXWWy9tuummaamllsqvv/rqq2nvvfdO5513Xtprr70Wxj5LkiRJkiSpu82UiuTTa6+9lm699dZ0+OGHp1VWWSXtscce6eabb0477rhj2m233UxISZIkSZIkqeMzpaZNm5YGDx6clltuudS3b980bty45tdmzZqV9txzz5yM2nbbbdP48eMb3awkSZIkSZK6oYZnSn37299OK620Utpmm23SnDlz0j333JMuvvji9I1vfCNttNFG6ZOf/GT+Ga9dccUVC3evJUmSJEmS1D1mSv3+979Pr7/+errrrrvSLbfckmdFvfvuu+noo49OTzzxRPr4xz+ef+/Xv/51Gj58eJ4xtdpqqy3MfZckSZIkSdKSPlPq0ksvTb/97W/Thx9+mNeVevnll9OXvvSlNHPmzHTRRRfl7zF27Ng0atSodP311y/M/ZYkSZIkSVJ3mCm12WabpZ///Ofpo48+Sssss0w6+OCD06c+9al05plnpo997GP50/b4FD5mT51zzjmpd+/eC3fPJUmSJEmStOTPlHrggQfS0KFD0/LLL5+eeuqp/LgeC583NTXl9aSeffbZ9P7776c333wzffDBBwt3ryVJkiRJktQ9klJ8+h5fL774Yrr//vvT888/n5588sm05ZZbpvfeey9/zZ49O+2www5pzJgxC3evJUmSJEmS1D2SUuPHj8+P6X3uc59Lw4YNSy+99FL+RL4zzjgjzZo1Kz/exwypcePG5cXQ//GPfyzcPZckSZIkSdKSn5Q6/fTT0/z581O/fv3SBhtskCZMmJAXOmcdqf79+6epU6emU089NfXs2TN99atfTb/61a8W7p5LkiRJkiRpyV/o/LjjjssJJ2ZAkZTafPPN09FHH50/ie+II47IySoe3cN+++2X+vbtuzD3W5IkSZIkSd0hKUVCCquttlr+wv7775//P3r06Ba/u/rqq3fuXkqSJEmSJKl7Pr4XZsyYUff1t956K332s59Nr7zySrt3ikcDSWwtt9xyadttt01//etf88+nT5+ePwGQWVkkwvjkv3DfffflGVwDBgxIZ511VovtTZo0Ka255ppp4MCB6eqrr27x2vnnn59WXnnltPbaa6e777673fssSZIkSZKkhZiU2mijjdK///u/pxNOOCF/El8lPnmPRdBJHLUHn+p38sknpxtvvDE99dRTaZ111knf+MY30rx589KIESPSkCFD0sMPP5yeeOKJNHHixPw3M2fOTCNHjkz77LNPmjJlSrryyivTPffc05zI4nHC448/Pt122215v59++un8Gt/zCOJFF12UrrjiijRq1Kj0xhtvtGu/JUmSJEmStBCTUsxOYn0pElIsbl502WWX5YTQNddck/r06ZPa45FHHknDhw9Pn/nMZ9Iaa6yRDjzwwPTcc8+lyZMn50/5YxYUiarTTjstXXLJJflveE9mQZF4Gjx4cE48xWvMutpuu+1ywmnjjTdOhx12WLr88subP1GQRxB32223tMUWW+T/33DDDe3ab0mSJEmSJC3EpFSPHj3SXnvtlT9db88992xOVJ155pnpmGOOSXfccUfabLPNUnttuOGG+TG6Rx99NCehLrjggvw44LRp03Kyikf6sMkmm+TZUuA1Ek/sG4YNG9acMOO17bffvnn7jb5WDbO13n777RZfkiRJkiRJWogLnVcaN25cXj+KxNHtt9+ettxyy5zQ6egi5ySlSHrxiCAGDRqU/vCHP+T349+BBBSf/Mc+kBzi78IKK6zQvPYVrxX/rtHXqhk7dmw66aSTOnR8kiRJkiRJanCmFDOETjzxxPTmm282/4zZUXPnzk2vvvpqmj17dl6LiQRRR/3xj39MN998c3rooYfSv/71r7xO1M4775x69eq1wCOByyyzTN6Hytfi52jva9WwXhZJuPhi7SxJkiRJkiQtpKTU3/72t/T73/8+f0LdnDlz8owoEjQ/+9nP8hpMfNLeHnvskXbaaad0/fXXp47g0/H23nvv9B//8R9pxRVXTKecckpe/Lx///55QfMikmG9e/de4LX4Odr7WjUksJhNVfySJEmSJEnSQkpKrbfeenmtqGeffTb9+Mc/TrvuumtOHMVMIR6jYyHx3/zmN+lb3/pWeuCBB1J7ffTRR+m1115rkSiK2VB8sl544YUX8gwuEktDhw5t8RqLpa+66qr53+19TZIkSZIkSV1kofNPfOITeTHzv/71r3ntqMcffzx9+OGHza8PGTIkfzoej9wxo6o9ttpqqzzb6uyzz05XXXVV+uIXv5hWWWWVdPjhh+c1oPiEP/DpezvuuGNOiI0cOTI9+OCD6c4770zvv/9+OuOMM/KsLbAYO58GyL6yT+edd17za6xdxULqL7/8cn4MkU/si9ckSZIkSZLUhRY6f/jhh3NiisQRj69tvvnm+VPxeMyuX79+6etf/3r69a9/nR/pW3fdddu8QySRnnzyyXTOOeekf/7zn2mjjTbKjwguvfTSacKECTnhNXr06NSzZ89077335r8ZMGBATmKx9tTyyy+f+vbtmyZOnJhf23TTTdMRRxyR95M1owYPHpwOPfTQ/NqIESPSddddl3+GHXbYIT+GKEmSJEmSpC40U2r8+PFp2223zV/LLbdc/gQ8ZiHxiN0GG2yQX+fxuxtvvLFdCSmwzeOPPz6vYzV//vz05z//ufmT+JgRxfpSv/zlL3PiqviJe4ccckh6+umn05VXXpkee+yxtPLKKze/duqpp+ZH8yZNmpTXxlp22WWb3+vyyy9P9913X7r11lvTTTfdlH8mSZIkSZKkLjJTiiTQ2LFj0+2335622GKL5p8zy+hXv/pVTh4deOCB+XG4//mf/0nrrLPOQtlhHuXbZZddqr42aNCg/FUNCaxiEquItaUkSZIkSZLUBZNSzIR67rnnan463Wc+85n00EMP5U/kq5UYkiRJkiRJktr8+F6thFRg0fGDDz44r/ckSZIkSZIkdXim1OTJk/NaTJUJJ9Z94vG3FVdcMX9a3qhRo9LRRx+dH/WTJEmSJEmSOpSU4lPxWOC8qampxc9ZGJxPyiMptf/++6ctt9wyf5mUkiRJkiRJUoeTUnyaHZ9QVw2favfUU0/lf7/zzjt59pQkSZIkSZLU4aQUM6Jwww03pLPPPjutueaa+RP21l133Twrin8zi4rf4/E9SZIkSZIkqcNJqbDRRhulQw45JH344Yd5VtSf//zn/PO33nor/eQnP0nDhw9v6yYlSZIkSZLUzTSclHr//ffTs88+m2dDbb755s0/33777dMpp5ySfv/736cDDzwwHXrooemwww5bWPsrSZIkSZKk7pSU6tOnT9pjjz1Sr17/9ycfffRRmjdvXpowYULacccd00MPPZQXOV911VXT7rvvvrD2WZIkSZIkSd0lKfXMM8+kX//61+lLX/pS88/+/ve/pzXWWCP/++67707rr79++uUvf5lGjBiRPvvZz6bll19+4ey1JEmSJEmSFms92/LL3/jGN5r/zSfsffrTn27+/p577kkXX3xx+sxnPpPGjRtnQkqSJEmSJEkdnynVs2fPvLj5ySefnL9nbSke34vvX3jhhfTXv/41/ehHP0pf+9rXGt2sJEmSJEmSuqE2zZTq0aNHWmaZZfL6UvyfRBX/5mvjjTdOTz31VJoxY8bC21tJkiRJkiR1r5lS7777bk5Kff/7329+fO+kk05KxxxzTPPv3H777enSSy9NP/zhDxfO3kqSJEmSJKl7JaV69+6dHnjggRbfP/rooy1+56ijjkoDBw7s3D2UJEmSJElS901KLbXUUnkR86L11lsvvfLKK+m9997Lj/JttNFG+efPPfdcWnfddTt/byVJkiRJktS9klK4+eab07XXXpsf4wPrSrH4+W9+85uclMLMmTPTJz/5SdeWkiRJkiRJUuckpVZbbbW09dZb5wQUn773gx/8IL322mstfqdfv34mpCRJkiRJktR5Sal///d/z19hzJgxC/xOzKKSJEmSJEmSavnfZ+7ayQSUJEmSJEmSFupMqfHjx6fllluuRSJq3rx56Ve/+lXz9zzSN3/+/HbtiCRJkiRJkrqPhpNSt99+e+rTp88CSanJkye3+L0PPvigc/dQkiRJkiRJ3TcpdcMNNyzws0984hPp6quvbvGz/v37d86eSZIkSZIkaYnlmlKSJEmSJEnq2p++9/rrr6fZs2enpZZaKq8fJUmSJEmSJC30pNSECRPSWWedlZNS6Nu3b7r00kvTc889l78nUfXee++1a0ckSZIkSZLUfbTp8b1jjz02vfbaa+mf//xn/nrmmWfS0ksvnXr37p0XQV9mmWXS9773vYW3t5IkSZIkSep+M6Wq+drXvtY5eyJJkiRJkqRuo0MLnUuSJEmSJEntYVJKkiRJkiRJpTMpJUmSJEmSpNKZlJIkSZIkSVLpTEpJkiRJkiSpdCalJEmSJEmSVDqTUpIkSZIkSSqdSSlJkiRJkiSVzqSUJEmSJEmSSmdSSpIkSZIkSaUzKSVJkiRJkqTSmZSSJEmSJElS6UxKSZIkSZIkqXQmpSRJkiRJklQ6k1KSJEmSJEkqnUkpSZIkSZIklc6klCRJkiRJkkpnUkqSJEmSJEmlMyklSZIkSZKk0nXppNQxxxyTRowY0fz99OnT09ChQ1O/fv3S6NGjU1NTU/Nr9913X9pggw3SgAED0llnndViO5MmTUprrrlmGjhwYLr66qtbvHb++eenlVdeOa299trp7rvvLuGoJEmSJEmS1GWTUo899li64IIL0rnnnpu/nzdvXk5QDRkyJD388MPpiSeeSBMnTsyvzZw5M40cOTLts88+acqUKenKK69M99xzT3Mia7/99kvHH398uu2229IJJ5yQnn766fwa3x999NHpoosuSldccUUaNWpUeuONNxbhUUuSJEmSJHUPXTIp9dFHH6Vvfetb6Xvf+16ewYTJkyenWbNm5VlQ66yzTjrttNPSJZdckl8jCcUsKBJPgwcPzomneG3ChAlpu+22ywmnjTfeOB122GHp8ssvz6+NHz8+7b///mm33XZLW2yxRf7/DTfcsAiPXJIkSZIkqXvokkmpX/ziF+nxxx9Pa621VrrpppvS/Pnz07Rp09Lw4cPTcsstl39nk002ybOlwGsknnr06JG/HzZsWJo6dWrza9tvv33ztht9TZIkSZIkSd0oKTVnzpx04okn5hlSf/vb39LZZ5+d/t//+3/p7bffToMGDWr+PRJQSy21VHrrrbcWeG2FFVZIM2bMyP9u72vV8Aghf1P8kiRJkiRJ0hKQlLr++uvTO++8k9eEOumkk9Idd9yRZs+enS699NLUp0+fFr+7zDLLpLlz56ZevXq1eC1+jva+Vs3YsWPTiiuu2Py1+uqrd+qxS5IkSZIkdRddLin1j3/8Iz+mx6foReKIR/X+9a9/5QXNi0hW9e7dO/Xv37/Fa/FztPe1asaMGZPXtYqvl156qROPXJIkSZIkqfvockmp1VZbLb377rstfsZjfOecc07+ZL3wwgsv5MfpSCwNHTq0xWuPPPJIWnXVVfO/2/taNcyq4hG/4pckSZIkSZKWgKTULrvskhcwZ7FzZk2dd955eUHyPfbYI6/hdNlll+Xf49P3dtxxx7yu1MiRI9ODDz6Y7rzzzvT++++nM844I+2000759/bcc890zTXX5IXTWa+K7cVre+21V7rgggvSyy+/nF599dX8iX3xmiRJkiRJkhaeXqmLWWmlldKtt96ajj766HTkkUemT33qU+naa6/N6zdNmDAh7bPPPmn06NGpZ8+e6d57781/w6N+LIi+8847p+WXXz717ds3TZw4Mb+26aabpiOOOCJtvvnmec2owYMHp0MPPTS/NmLEiHTdddfln2GHHXbIyS9JkiRJkiR1s6QUttxyyxaP1QVmRD3//PNp6tSped0pEljhkEMOybOcnnrqqbTVVlvl5FQ49dRT03777ZdnRG2zzTbN60bxCX6XX355Ovzww/Pi6rzGzyRJkiRJktQNk1L1rLLKKvkRv2oGDRqUv6rZcMMN81c1rC0lSZIkSZKkbrymlCRJkiRJkpZ8JqUkSZIkSZJUOpNSkiRJkiRJKp1JKUmSJEmSJJXOpJQkSZIkSZJKZ1JKkiRJkiRJpTMpJUmSJEmSpNKZlJIkSZIkSVLpTEpJkiRJkiSpdCalJEmSJEmSVDqTUpIkSZIkSSqdSSlJkiRJkiSVzqSUJEmSJEmSSmdSSpIkSZIkSaUzKSVJkiRJkqTSmZSSJEmSJElS6UxKSZIkSZIkqXQmpSRJkiRJklQ6k1KSJEmSJEkqnUkpSZIkSZIklc6klCRJkiRJkkpnUkqSJEmSJEmlMyklSZIkSZKk0pmUkiRJkiRJUulMSkmSJEmSJKl0JqUkSZIkSZJUOpNSkiRJkiRJKp1JKUmSJEmSJJXOpJQkSZIkSZJKZ1JKkiRJkiRJpTMpJUmSJEmSpNKZlJIkSZIkSVLpTEpJkiRJkiSpdCalJEmSJEmSVDqTUpIkSZIkSSqdSSlJkiRJkiSVzqSUJEmSJEmSSmdSSpIkSZIkSaUzKSVJkiRJkqTSmZSSJEmSJElS6UxKSZIkSZIkqXQmpSRJkiRJklQ6k1KSJEmSJEkqnUkpSZIkSZIklc6klCRJkiRJkkpnUkqSJEmSJEmlMyklSZIkSZKk0pmUkiRJkiRJUum6fFLq85//fJo4cWL+93333Zc22GCDNGDAgHTWWWe1+L1JkyalNddcMw0cODBdffXVLV47//zz08orr5zWXnvtdPfdd7d47bjjjkv9+vVLm2yySXrsscdKOCJJkiRJkiR16aTUlVdemW677bb875kzZ6aRI0emffbZJ02ZMiW/ds899+TXpk+fnvbbb790/PHH598/4YQT0tNPP51f4/ujjz46XXTRRemKK65Io0aNSm+88UZ+7cILL8xfN910UzrllFPS3nvvnebPn78Ij1iSJEmSJKl76LJJqTfffDMdddRRaf3118/fk4RiFhSJp8GDB+fE0yWXXJJfmzBhQtpuu+1ywmnjjTdOhx12WLr88svza+PHj0/7779/2m233dIWW2yR/3/DDTc0v0bCaquttsoJL97r/vvvX4RHLUmSJEmS1D102aQUCandd989DR8+PH8/bdq0nHjq0aNH/n7YsGFp6tSpza9tv/32zX/byGtNTU3p8ccfr/l31cybNy+9/fbbLb4kSZIkSZK0hCSleCzvrrvuSmeccUbzz0gADRo0qPn7FVZYIc2YMaPdr82ZMyd99NFHNf+umrFjx6YVV1yx+Wv11VfvxKOWJEmSJEnqPrpcUuq9995LBx98cH607uMf/3jzz3v16pX69OnT/P0yyyyT5s6d2+7X+Dlq/V01Y8aMSbNmzWr+eumllzrtuCVJkiRJkrqT/83MdCE//vGP09ChQ9Muu+zS4uf9+/fPi52H2bNnp969e7f7tWWXXTZ/8RozpCr/rhoSWMUkliRJkiRJkpaQpNRVV12VE0V9+/bN3zNz6dprr83/ZqHy8Mgjj6RVV101/5skFp/Id9BBB9V8bYcddljgtc033zy/ts466zS/FgurS5IkSZIkqRs9vve73/0uTZ8+PT366KP5i0/FO/nkk9Pf//739OCDD6Y777wzvf/++3m9qZ122in/zZ577pmuueaavHA5a0Wdd955za/ttdde6YILLkgvv/xyevXVV/Mn9hVfO/300/O6U88880yaNGlS82uSJEmSJEnqRjOlVltttRbfL7/88mnAgAH56+yzz04777xz/hkzqSZOnJh/Z9NNN01HHHFEnvnEulCDBw9Ohx56aH5txIgR6brrrss/AzOm9thjj/xv1q668cYb83vyyXqjRo1KQ4YMKf2YJUmSJEmSupsul5SqFIknHHLIIXkm01NPPZW22mqrnJwKp556atpvv/3yjKhtttmmeW2oHj16pMsvvzwdfvjh6Z133smv8TOwPtQdd9yRZ2Dx72HDhi2CI5QkSZIkSep+unxSqtKgQYPyVzUbbrhh/qqGtaWq6dmzZ05wSZIkSZIkqRuvKSVJkiRJkqQln0kpSZIkSZIklW6xe3xPUtutdewtHfr7F8ft0mn7IkmSJEkSnCklSZIkSZKk0pmUkiRJkiRJUulMSkmSJEmSJKl0JqUkSZIkSZJUOpNSkiRJkiRJKp1JKUmSJEmSJJXOpJQkSZIkSZJKZ1JKkiRJkiRJpTMpJUmSJEmSpNKZlJIkSZIkSVLpTEpJkiRJkiSpdCalJEmSJEmSVDqTUpIkSZIkSSqdSSlJkiRJkiSVzqSUJEmSJEmSSter/LeUtLhb69hbOvT3L47bpdP2RZIkSZK0eHKmlCRJkiRJkkpnUkqSJEmSJEmlMyklSZIkSZKk0pmUkiRJkiRJUulMSkmSJEmSJKl0JqUkSZIkSZJUOpNSkiRJkiRJKp1JKUmSJEmSJJXOpJQkSZIkSZJKZ1JKkiRJkiRJpTMpJUmSJEmSpNKZlJIkSZIkSVLpTEpJkiRJkiSpdCalJEmSJEmSVDqTUpIkSZIkSSqdSSlJkiRJkiSVzqSUJEmSJEmSSmdSSpIkSZIkSaUzKSVJkiRJkqTSmZSSJEmSJElS6UxKSZIkSZIkqXQmpSRJkiRJklS6XuW/pSS1tNaxt3R4Gy+O26VT9kWSJEmSVA5nSkmSJEmSJKl0JqUkSZIkSZJUOpNSkiRJkiRJKp1JKUmSJEmSJJXOpJQkSZIkSZJKZ1JKkiRJkiRJpeuSSakbb7wxrb322qlXr15ps802S08++WT++fTp09PQoUNTv3790ujRo1NTU1Pz39x3331pgw02SAMGDEhnnXVWi+1NmjQprbnmmmngwIHp6quvbvHa+eefn1ZeeeX8fnfffXdJRyhJkiRJktS9dbmk1PPPP58OOOCANG7cuPTyyy+n9dZbL40aNSrNmzcvjRgxIg0ZMiQ9/PDD6YknnkgTJ07MfzNz5sw0cuTItM8++6QpU6akK6+8Mt1zzz3Niaz99tsvHX/88em2225LJ5xwQnr66afza3x/9NFHp4suuihdccUV+X3eeOONRXr8kiRJkiRJ3UGXS0oxK4qE1Je//OU8g+nb3/52euSRR9LkyZPTrFmz8iyoddZZJ5122mnpkksuyX9DEopZUCSeBg8enBNP8dqECRPSdtttlxNOG2+8cTrssMPS5Zdfnl8bP3582n///dNuu+2Wtthii/z/G264YZEevyRJkiRJUnfQ5ZJSu+66a/rWt77V/D2zmkg0TZs2LQ0fPjwtt9xy+eebbLJJni0FXiPx1KNHj/z9sGHD0tSpU5tf23777Zu31+hr1TBb6+23327xJUmSJEmSpCUgKVU0f/78dOaZZ6ZDDjkkJ4AGDRrU/BoJqKWWWiq99dZbC7y2wgorpBkzZuR/t/e1asaOHZtWXHHF5q/VV1+9049ZkiRJkiSpO+jSSakTTzwxfexjH8uP3rHoeZ8+fVq8vswyy6S5c+cu8Fr8HO19rZoxY8bkRwjj66WXXurU45UkSZIkSeoueqUuik/C45PxHnroobT00kun/v3750XLi2bPnp169+6dX2Ox88qfo72vVUMCqzIxJkmSJEmSpCUkKfXCCy/kT9IjKbXhhhvmnw0dOjRdfPHFLX6HNZ5ILPHaVVdd1fwaC6OvuuqqzX/HJ/IddNBBNV/bYYcdFnhN0uJtrWNv6dDfvzhul4W6PUmSJEnq7rpcUurdd9/Ni53zSXi77757mjNnTv75VlttldeAuuyyy9IBBxyQP31vxx13zOtKjRw5Mn3nO99Jd955Z9pmm23SGWeckXbaaaf8d3vuuWfacsst0xFHHJHXjzrvvPPSV7/61fzaXnvtlderYns8yscn9p177rmL9PgldQ8muSRJkiR1d10uKXX77bfnT9Xjq3Jm1IQJE/IMqtGjR6eePXume++9N782YMCAdPbZZ6edd945Lb/88qlv375p4sSJ+bVNN900J6Q233zzvGYUn+R36KGH5tdGjBiRrrvuuvwzMGNqjz32WCTHLUkdYZJLkiRJ0uKmyyWlmCHV1NRU9bW11lorPf/882nq1Klp+PDhaaWVVmp+jRlPzI566qmn8qwqklPh1FNPTfvtt196+eWX80yqWDeKT/C7/PLL0+GHH57eeeed/Bo/k6TuzscfJUmSJHW7pFRrVllllbTLLtUHJzyex1c1rE0V61NVYm0pSZIkSZIkladnie8lSZIkSZIkZSalJEmSJEmSVDqTUpIkSZIkSSrdYremlCRJLpwuSZIkLf6cKSVJkiRJkqTSmZSSJEmSJElS6Xx8T5LU7XX244BdfXuSJElSV+BMKUmSJEmSJJXOmVKSJHUzzrySJElSV2BSSpIkLdIkF0x0SZIkdT8mpSRJUpfjbC5JkqQln2tKSZIkSZIkqXQmpSRJkiRJklQ6H9+TJElLPB8HlCRJ6npMSkmSJLWRSS5JkqSO8/E9SZIkSZIklc6klCRJkiRJkkrn43uSJEmLmI8DSpKk7siklCRJ0hLGJJckSVoc+PieJEmSJEmSSmdSSpIkSZIkSaXz8T1JkiTV5eOAkiRpYTApJUmSpMU6yWXSTJKkxZNJKUmSJGkxSpp1te0tjG2aKJSk7sGklCRJkqQuzSSXJC2ZTEpJkiRJ6la62mwzZ5pJ6q5MSkmSJEnSEqyrJ826+vYkLTwmpSRJkiRJqmFxSJqZiNPiyqSUJEmSJElabBJx3W17S7Kei3oHJEmSJEmS1P2YlJIkSZIkSVLpTEpJkiRJkiSpdCalJEmSJEmSVDqTUpIkSZIkSSqdSSlJkiRJkiSVzqSUJEmSJEmSSmdSSpIkSZIkSaUzKSVJkiRJkqTSmZSSJEmSJElS6UxKSZIkSZIkqXQmpSRJkiRJklQ6k1KSJEmSJEkqnUkpSZIkSZIklc6klCRJkiRJkkpnUkqSJEmSJEmlMyklSZIkSZKk0pmUkiRJkiRJUulMSkmSJEmSJKl0JqVSStOnT09Dhw5N/fr1S6NHj05NTU2LepckSZIkSZKWaN0+KTVv3rw0YsSINGTIkPTwww+nJ554Ik2cOHFR75YkSZIkSdISrdsnpSZPnpxmzZqVzjrrrLTOOuuk0047LV1yySWLerckSZIkSZKWaL1SNzdt2rQ0fPjwtNxyy+XvN9lkkzxbqtasKr4CySy8/fbbaXH30by5Hfr7ynPg9txemdtbGNt0e27P7ZW3vYWxTbfn9txeedtbGNt0e27P7TW+vYWxTbfXtba3OIpjaG15pB5N3XwBpaOOOiq999576fzzz2/+2Sc+8Yn0zDPP5DWmin70ox+lk046aRHspSRJkiRJ0uLlpZdeSquttlrN17v9TKlevXqlPn36tPjZMsssk+bOnbtAUmrMmDHpyCOPbP7+o48+Sm+++WZaaaWVUo8ePdKSigzn6quvngvTCius4PbcXqnbWxz20e25Pbe3eO+j23N7bm/x3ke35/bc3uK9j25vycT8p9mzZ6eBAwfW/b1un5Tq379//vS9Ik5c7969F/hdkleVCay+ffum7oIK05mVxu25vUW9Tbfn9txeedtbGNt0e27P7ZW3vYWxTbfn9txeedtbGNt0e11re13Riiuu2OrvdPuFzocOHZqmTJnS/P0LL7yQ140iWSVJkiRJkqSFo9snpbbeeus8fe6yyy7L3/PpezvuuGNaaqmlFvWuSZIkSZIkLbG6/eN7rCk1YcKEtM8++6TRo0ennj17pnvvvXdR71aXwiOLJ5544gKPLro9t1fG9hbGNt2e23N75W1vYWzT7bk9t1fe9hbGNt2e23N75W1vYWzT7XWt7S3uuv2n74VXXnklTZ06NQ0fPjwvXC5JkiRJkqSFx6SUJEmSJEmSStft15SSJEmSJElS+UxKSZIkSZIkqXQmpSSpBAvrSWmfwJakJd/i0tYvLvspSbZXXYdJqW7o/fffX9S7IHWrjuCjjz5KPXr06NTjnz9/fvrggw+at8t7dAWxH7fcckt66623UlfVncrf4sJrorbo7n1IV70WXXk/u3sZ7Mj+MnYg5ugu1+a9995L77zzTqftj7qWOXPm5Bg12qvOrsuLW9vQFZiU6mbefffd9Otf/zqNGTMmzZw5c1HvzmInGpkHHnggJwViAN6ZjU9XSS4s7Ma1MnDtisfdGaZPn55OOeWUXF46M3D/3e9+l3bYYYd0wQUX5O979uy5yDtC3pv9mDFjRvrOd76T5s6d2ynbZXu//OUv09/+9rdO2d6HH37YKYFI/C1lNxJwnXH+F2ZwRNtFP9BeL7zwQnrjjTea62tH623sG//vjATrv/71r/Tb3/4217eFrasFnWUkgbvSMZ900knpjjvu6PT+oysdI1577bV05JFHpj/96U/NNxXnzZu3UN6rvccedfe0007L7UNnqHZN23udF8Y17ax+pKhyOx3dLvtY1JG444wzzsjjh866vp1Zb2M7t912W24TitemvY455ph0zjnnpCeeeKLTy0/ldVHrnnnmmU6LKXH44Yen0aNHp7vvvrtT6nIkbP/617/mNrs7Jec7i0mpbuaPf/xjzv5Pnjw5bbvttmn8+PHp7bffbvE77a2QXT2p8I9//CM9+eST7f77GDSRXT/ooINyAxmJgI40PnHeaBRffPHF5m12BbFvxx57bLr11ls7ZZsE0yRpGDgi/t+VjrvYyZAQ6cjdsi996Uu5I+3du3cuJ88991z6/ve/36FBBYmBqVOnplVXXTVNmjQpff7zn0/3339/fo33aG9dnDZtWi7fHUVHf8ABB+T9K87MbG/b8o1vfCONGzcuXXLJJen222/v0OD72WefTdtss026+uqr8/cdCUTib3784x+nn/zkJ80/4z3Q3uvAPpE4uvDCC/P3cQOhvYFsHOPpp5+exo4dm5Zddtm8nw899FCbtkPfwXW4+OKLm4+NelvZh7S1TX3ppZfyAIDgMLYZr7fVEUcckctxrevaWYMLkl4dDTppV/7+97+nV155pcP7c++99+ZySHvVGShzf/nLX/L56mgy/dVXX83XhPLTWRgo3nDDDWnLLbdsUWY6Iw7paoMJ4gLaUdqEpZdeOh/7Zz/72U5LTHX0+sZgjITUzTffnFZaaaVO2a+4ptdee2266qqrWvysrfWY46L8nXjiiTkW7KjO6kfi9+nTYuZzZ9y8Ij7AUkstlf9P3HH22We3u37wd7RX9MEkp0iQdiQuipsGcT2JaTp6M+ypp57KsTn7yHXpSALjuuuuSz/72c/SlClT8nkjzuqMckOyonhdOrqthx9+uNMSXMVyR590/fXXd2h7nTkmpKwRe/z3f/93c0xJu9iR63vFFVfkNpVx8Jlnntl8/O2pc/xtr169crlmfLP11lvnMqO26XqjQC00//znP3Nw+IUvfCE9+uijeYYFsxm4s4BZs2alN998s00VktkaNIrPP/987hToWB555JF01113tWsf2zu4qSYaGO7scJy77LJLHtC2V5yXU089Na2xxhppo402ysfL9wwsYiDaXoccckhae+2188CxvQhSn3766fzvxx57LO9TRzqsuKaXXXZZGj58eIc7Gq5Jnz598mD497//ff4Zg+QDDzywQ9vlbxdGUpRy/e1vfzt3Wvy7reeSwOj111/PwXr46le/mjtCzkN7zh/X+M9//nOeEUJHyntstdVWOdFFOX/55ZfbFLjHMZ133nlpyJAh6dJLL21XMBePl/zhD3/I+7TzzjvnnzOIin2hw27rAJxBDtgmgTsB54QJE5oT7O25O0ZASHDz9a9/PbeF7UnmRSDMAJGEFPtEMLL33nunfffdN9fD9iRaYx+OOuqoPMuOdvunP/3pAoFso4OfBx98MG8HEydOzAlmfPOb38z72xYcK2Vj5MiReaBIu0D7v+eee7brWkSbSrkl+OccUleoa/F6WwZ5DBr4m8997nN5P4uJI8oOP2tPwEkdif2gPl900UVpiy22yP1pe3BDg/6Ic8hd/UjMdwQDpxVXXDF98pOfTJ3hK1/5Sk6o0/aBevuDH/ygub60BQlq4o1iEoV/c0e5vU444YT0wx/+MJe/L37xi81J+fYkpyKpwiPH1OXijMz2JjGLfQVxEl9tFe9NgpB//+d//mfzsQ8bNiz3Ie3t3zk/cdzUCfoNZmO1JznAYIybGeeee24uh4j9Ks6EbBQ3TbkWUVeIUZndv/3227fr5kvsC8dH4nbHHXfMA0YSXffdd19qj87qR6I9or7RB8fPmI1ELEj731b0PbRPe+yxR05G4eCDD84xfntv/vF3tM30v1xLknvcNOH6tGdWKu0BX2yLhPrJJ5+8wO80WmbiHHJNiKNpD4ill1tuudRe3CShbaac9O/fP9+IYdbUnXfemWOv9qDv4Jp+97vfbdGntBf9LmWZcsONnWJb0NZtk+iJ80gyips7HRmLRJlhnzojNicJGuMFyguxCOMGrtPs2bPbfLz8HbHllVdemdtX2i+uD3FRex5RZXvEWTfddFNaZZVVcv9Om+9yOW3UpG5j0qRJTXfccUfTP//5z/z9H//4x6ZBgwY1v37VVVc1feELX2i6//77m+bPn9/0wQcf1N3ev/71r6avfe1rTbvvvnvTxhtv3LT55ps3rb322k3rrbde09FHH938ex999FGL/9fywAMPNB1++OFNf/rTn5ree++9Bf6+Umvbi3381re+1fTjH/+46Z577sk/mzVrVtOcOXPatB1w7g455JCmT33qU01bbLFF05FHHtm07bbbNn33u99tuuuuu5ouvfTSprZ6//338/+feOKJpn/7t39revDBB5t+9KMfNbUH12vChAlN++23X/6+X79+Tf/5n//ZdPnllze98sorDR9n0f/8z/80DRs2rOknP/lJ/v7DDz9s/v/NN9/c5u39/e9/b3rppZea9tlnn6aHHnoo/+yAAw5o+sEPfpD/3dZ95Jred999zd+3Vmbbg2OnbH/xi1/MdSjqTyM++clPNk2cOLH5+xtvvLFp+eWXb3r33XcX2Odima9m9uzZ+VzddtttTaeffnrel6Lf//73uSzuvPPOuRw0ci7jvakPgwcPbho/fnzedkdss802uX5885vfbHr++eebZsyYkcs0+/XpT3867yM/bxTlb/r06fk6U084bso4X5dccknTU0891aby/O///u9N8+bNa/rzn/+c2z+2f8QRR7S4rlHOG0Gbee211+Z/77TTTk0bbrhh0/HHH9+06aab5mtVrOeNevbZZ5uGDx+e/27kyJFNvXr1ym1qtFttqSfHHntsU48ePXL78tWvfjX/jPO/1lpr5WvTlv37+c9/3vS9732vae7cuU1rrLFGrs+0iePGjWvxe43sX5xjrsmQIUPyvvziF7/IfcrBBx/cdN111zW99dZbzeU0fr/etqlrY8eObbr33nvz+Xr88cfzNu6+++7cT1GXY5vtwXa5rquuumrT97///VyO2urVV19tevLJJ/PxzZw5s+n//b//l7dLv0d/1Z52eurUqU3/9V//lcsb26GehIcffrjpkUceydtu1C233NL05S9/uem3v/1t08CBA3M8sPfeezftueeeTV/60pdy200b1kh7S/3cY489ch3h2OL4KDfHHHPMAr/fyPGzf1tvvXX+N+X661//em5zvvOd7+Sf0VZcf/31DR1rlCvK9AorrNC044475jLO3xMrtGW/qv3O+eefn+vKBRdc0NRe7NNmm22W/02sQRsTaFsp542qto+/+tWvcgzHF2WxLZ5++un8f+Ksr3zlKzXbz7/85S8Nn0PKyrLLLtu0ww475HJNf/bXv/616ac//WmOaY466qjmtqvWMRW9/PLL+bgoI6BtpY2hL1p//fXbdP46sx+J14ktaaO32mqrXNdAzPqNb3wjx+ltRXvO340aNSqXPfqQ1Vdfvfn1eI9G25r4Pdoujhe0AfTBe+21V9OVV16Zz3Gj/eY777yTY33+9oQTTmhaZZVVcuwRpk2b1vSPf/yjTftGPLrlllvmf9Mf0Yeed955udy0tU+nTkVZCY899ljTQQcdlOMYtl85TqnnjTfeyG0JZZAYgbLyzDPP5Nc4d6eeemqb44SIzWnzKc+Mu+JYifcrtXatORau6be//e2m/v37Nx122GG5n0IxXm1ke8QJa665ZvPfVx5btTFhve1x7djmSSed1HTRRRc17b///jlmYF+pO23t0ylztC2gHaEuv/766zk+og7jzTffbOj6xn5feOGFTZ/73Oeadt1113ydX3zxxRb9cPSVjY6HuyuTUt1ANMa/+93vmp577rnmn9OR/vd//3dzJ0GjSydBY0SFopOo15C//fbbuSJTqWkMf/Ob3+QAlMY2Or22JAkIRP7jP/6jad99981BSDTalcdBZ1MMSOqh4hNsMjCjEafjJLBm8FM8F/G79TDgIXD49a9/nb8/55xzmj72sY/lIJ0GiQ6a92i0M41zBDqqK664Im/7M5/5TPPxMuiL/WrkXDKIoDMgGULSjKC/d+/eeYBx5513NjfeDNKKA4RqOD80/CuuuGKLAP/RRx/N+0tDTpDSKJIqDB4IKGm8GTgQ0BLI0nlFcNsWdCBcEzqnah1InLNGAxLqAOe8Eh0U+73JJpvkYJZOp7VBHskdkgm33357c5DAAO/cc8/N/67cXwZCJJZqIQAmAcIgloE3x8b5f+2115quvvrqHFBQh0hC0rmeccYZDXd8nL8f/vCH+RowGAgMZtsyGKNeERgy2N5ll12atttuu6abbrqp6bTTTssDH8pRMVBpDcfAYIfzx3WOc06AQxvBYJcyQKKqkXrH9Yv6y2CR/SMJT7lkQMDPimWltYQ4Scall146X2PceuutzdeQwDUCnChDtJGtIZAhgcJ5oz7TJtNuMYCKpNSBBx6Yg7HWxH7yvlzXZZZZJifPOW+nnHJKfo02m3ac9oHAnveshnaUwTHtAgkZvigvJCEjkOc8NBpoBpI8N9xwQ/435572ZcyYMbk9ZR8pSxdffHFOVtGG1OuPTj755Hysf/vb35o22mij3K+deOKJTT179sznL4Jr+pbWBnv8LmWDtpJgmHJN4pYBHn1QHHNb2kDOIcmYs88+O5cH+o3tt98+D2QZbJNwKP5uI4kkzjHln/pL20VbH30dfQB9Aa9xLuL3W0Mbw0Cd+sC14HgZAFHvuF4cMzcWSKy15pprrsltZgyY4vxTn4ttbVsGjewDbSnlkXgBxDKRuOHac24bEe/LgJN2k+NmwEz5I7lMP9XWMk0/y7nbbbfdmtZdd918vdsq9os6yc0+bkiQWOC449iI0TiPJB4o560Nzth3yjMDao6LWI9yt9JKK+V+mOvSluvAtSVZScKNOIN6yrapp7TzlBvaacoK+0jdbhT9HXWNNouYLVAnueFBG0lf18j1oC3u27dvPl7aOQbwxFu0FZT1aBeKMVkZ/UigbaFd5nipY7TtbId2kT6GpEZ78L4kLYi3BgwY0NzOgv2L+IjyRHvTGhJl9B3sK31G1DsSNfTTtF+N7CsxC+9PvEXMTxv1s5/9LJcdkKQiAdkatkFbd9lll+WbLCTMou/gRtYnPvGJBeKsKVOm1E020BaTnGbfcOaZZ+bYj0QZuB7Ua9pZbopVjlNqxYIkyIjHKbP8HTEXN1Wpf5SbokaSU7SBUQaJKWPcwLFx47O4X7XKH+/D+eLaffazn83lkOQPfTo/j3pIeaasNBpPcn25BsRGtFvF4yn+mxiHm0f1+nWQ0OOcc9MeJJIZg8RrJF+JExpBnMCYJtpQjp34lPNFuxBo1yIBy4304g2KahgT0LfTNtBvEydxXYhLK9vURupad2VSqhshcIjBMbOS1llnndyYk4AKdNDMWqEyLbfccnW3R7DRp0+f5tkA0RiBoJXGm8Ez2yIAoJNopMEg2UODQwWnE628I0bgQ6cxefLkmtvhPWnooyGh4+NOHT8bMWJEDiJoLBkkV7urUBQNMftBQ0UgyOCbWVK8D+eVzD2NEdsmAKeziXNRCwMaBnZ0WHRMvM/KK6/cfKeSu2SjR49u3gcGUTEYqiYaPgb/NLogMUBnSFDGtjleBgcEejS0rWFGAMdEh8yAmCCUc8fdgGK5aQSdJYEH26PTJOhgQMZ1ZPBEAoe7ohHsoJEAh7LMgInyStll1sALL7ywwO/RqfK7tXB92R8CEK5BtU6I80CAPHTo0Hz92F61DoZj4Hj4XQJN7oQRhBA4Fa8VQTDXhN9ZbbXVWgSN1TDQjAQmQTB1hPNKgnCDDTbIZZDrTzKHulwtwRYoE9RPAiQG25wfZoQRlBTrUOC8RmBWDeeBWQvsF0hYcEwkSakj0cGH1gIcBlgMLhm8M1gkcAAD8JjxQ93lHBAgMoBkAFcrwCG4/PjHP96cDGUAG7OsqL/cbeR6EWDTdrWG80UgSH1nHxgUEojwc64T78FAIBJ+lFHqTj0EzGyT80hQTBvDduNY4+YCMyUaSVLHOSagO+uss3IbzACAeh31jMEP5Z7BLeU62pxKzDIiCcD1oO+g7NK+sD8EeCToCUQpe60lHmPwzDX9/Oc/n/9N0oQ+heOkTJPgolzTTtDWMFDhGGqVG84dZY26wT5yTUBwHXe9abvpC9h/Au162A7nhN9nAMwd2mJfRNvN61HeG8FxURZIpDCrh5kpMYOGAR/tQ9xdJcnMoKqYqKqGATXHyLkB+0tSjnZgqaWWyv0lAwvafdoh6nW9wTczpimr1HfqH/WJQSj7SuKGtpq+gzYDJMRrtQsE5/Q/tCPFhCx1jRtFoKzQB3C9GGC2NvuMesw+0FZS5iiztNe0Ab/85S/ztmIWVag1MzPqEIko6gXxB/g/Mxg4XtoG3rO1+IXBLDcWaMupTxwjd+RJhrC/Mchl8NNabFBEQoF9Acky+nYGpBwrbT7lhf2lrFCvK2fQFtHeMVAkyUVbT7tAYrGY0G9LUoprxzEze5y6wHEzYKSdo12hzPB/+ppinFhLsW5TdigjfDGrgRiJpD9oD+in6Aco060lk6jLtJlcS9oRYhAGscSaxEVtmeXeWf0Ig3PiZPaBWA+UW76nD+BYiQ1pE6gbjcRriJuNfHFTKdD2cQ5IPNBmB9pB4rHWEIvRJpOU4vgYzJNU4DiIDbnGxCbEYMUYrhJ9NO0fyVHaac4/sRQ3HbhxwPajbWE7nONq8Rx4X/obyh6JSq4pSWC2C9p9XmdmJe0eiVdiX967FmJQ2hJQZ2kXOFa+KHPsU8SyJHL4eb2ECG1MlBmSPfQh3GxhNhI3urnpxAQB2gm22QjOdfSbxKmcr7jWJF6Jz0EZoIxRB6vdhOF16iztXPF1nrKIm2scH21sxMO0D8RatZI0xZiddpU4lBtinIPKhCj7TLKam0n12h3qJ8cQ7Sf90x/+8Ifm60W5KR4TfWKtGzr0VRwTCUKSWbSlvDf1lbJBGaevp42M7bGP1dr/6D9og6irxMCUbfp0bkTQFvI9iTf6SX6HssV7Fm/S6P+YlOoG6ASYYUGnDoI+gkQqCA0YgQ53O8i6U/loYGmIWxsgx0CF5A4dJoEwgykaNyonHe2hhx6aE1cMXhrZXjSMJJDYLxpeghKSPMVGkM6P12sho86+MEhhUEPHxGCAxhUEc3T0fE8nSsDcWraefSEAoyOhUaMjZ9/YNtvjvNKwsU0aYoLxWuhMeZ19o3OiweNuPNcJnEvuXkbihwaW4JTOoBo6yrgDQcDAo2YEIXHNQTBGQ0jHTAeOegPbeGyNxEvMYuN7kig0tARPMZjkZ/Xu0vJaBAoMYAkWGHCzHR4vIajlvBJUM/OMBAbntdYAuRLHTnBFEoDzxnUgQcP1oYPmnFAWeM96x0yQRaDL+WOQR6dZLeBl8EwgRNBdLVgk6I3ECcErHRH1gDvdzJ4hCKCM8PcEI5yHYgBZq6PnOlMGGCRzvhiIsC+8zjYpn/ybc0ritl4SicHqcccdl+s8dZPBDJ1o4FzGbBw6eZIrBI7VRFDO+3KdI4jkHHKeqDsMVCgzBJmNPPZEGY1ECucu7rITuHN9aKd4nWCHO6wkhwi6qyVLKX8cJ2Wf8kU5jsCNIJEkBQNXBnhsM6bF10L7RiBDMMKAkOPmutBucg6548Y1p8wxcOa8EQi3djeP68vfMLOAwIV2muMi6IykHomheFyQYJtkTKVigMfghjoROH+ci9gGx08ASgKx1mMsnD+OjTaA32OQTJvJflI22FeSuQRfJDII9rjW1Y6X80Xd4O8ZQMRgnXMUMzzps6inDPDpQ8D1og2vhcCUskabSlKQfQIDAQJNyhHtANeJetHa4Jvjon7GbGKuDQkPkg+UFQb3vB8zLyrPeWvYF5JSzESlrWDmG2WY8xn9Bm0+5ZzERj3UC64Jvwv6RdoArjvlHLQNXHOuH+1qPdz4oE+njaY9pC9lQEd8wOCROsJ7UZ9JavN7tQZ59DdcQ8oDv8e1ZFv0eeB6su/01QyWW5t5xTnhbzk2zhmDGWID6hxtPgMI6g/7Hf0s5ZB6Xy8JThkhKYhi20TbTnmiLeKcMNio9fg2ZYJj4UYfiT1wfhhwsn3KI+0F+0kik36zVntQnBXO9SRRS3zC488kgkhU0q5E4pV95n2IIWK2SS0MxqhjYFvcuGjPY6jRL3LtqQf0FyRl6I8YgEViguRqPJ7UKGIXriHXJNokBuyUO2aagPNB209cVCthw+/EI9UcKwl+4iCSCbRBtNO8F3WDWIl+jPik1tIEndmP0JZQbrmm/JsYivaP/aVd573iEXD6T5I3bXmUj7JDUpqkA+0hSIYSUxGH0LfwPYPvWjN4aduIM0jAU75IoNAO0DZybajbxFiRwCBGp68pzhCuRHmjDBJD0/7FjBfqAuWEskQdoi2kf6BOFR/tC8X6TNzIe3NNGdOAekpfyXvwXvyb/in6vVqK/SllK5L9tF0kCOmPqd/EALQvxK4RC9dDPM2MXfaB9oD4D7TJvCdxNrEgx1xvxjzlgmsWSRK2F2WQekAZivpAG0MdoG2ttSwDyW3Kftxcpa+gfoCyzPWkHYuxB+UnrlklYhSSivS3xfEaZYYyx/mrrA/E5PQP9dp8Er+069z0Ir6IJU64CUgdIUlIciriUNqKypl2XCv6Um5gcH5IVHMtaS/p4zlHlHf6fJJTMbuVGDkeC61Ewo8EL/EZcS/vyY22eG9iG84lZZrEOslIxpvFJUfUkkmpJVg02lTWeEaYhpoAKxpu7kySdaeTpeJQUWl8aChrKTY2VDoGyDTe/JzOi8FFBFVUfpI0NFa17mYRVBJ0MTBmanp0oCAgpvOk86ORYDDBYIVMfgSe1Y45pmMTINFp0KGTRKIBogOhoeE9aSTpsAiKq92NieMgc05nETOVOKd0xtw5oYEmkCHoIWABgUYMVurh7zhfBHCcfwJQ9p2GLNbb4JgIWng91mEq4rwysKHBZiYXwQMdMHdmIhjkerPPXC/KQr3H2kgaMHChcyMIp8OnfFAuCBwY8LANzheNLLMZODeco1oYoMdjFnRQBIeRhCHQLK5Bxh1+GnA6GxIntdDJVAbTNPZcDwIAAi06L2YRsO8MeBp9RJA7M3T0BDnsT+Wddo6HQWVMJy6iQ+ZRhuLdQs4fnSp3uQlE6TTpvCgjxW1Xux4kWfhbjonzTKfKjDUQmDDA4JErgjjKJ+WFMs4AoZa4/gQIBEi8L8E6dZlzRJBAWYp9otxQ/mKwVUQ54HipRwQHDLriEbBIZrGfkRznXBBk1rubyiwN2g2OhYCJMkJwSSDHnWjKDoN3ZsfFTBWC5BiYV2JAGWWMMkX7wb4wS4wkDe1BcSBN2YvZCZVo53gvAiLKFEEaAR3tCe0NdZG7eNQTEjDsUwRLtQbGcd0pF5QrynExQUg94+ckfmgLCTRJwhDoMWuzGgJSAnDuLMZAlbaetoG6Slsb+0UyM5L81cogbQttO/tHGeb6ch0YiNEOsk2uQySy+X9x1k8l2iPKU8wC43oyqGUwxrllEMk+EtxxDBwLdSDa6MrzyHtTPqi37CPXln2mzFGuCbxrnfNqYvsEsdQJ+iS2w/mmLhCkElxSpmIGX2vbjEdGGLBzw4bjou0nwUI7xfHRD3Fe2HeOh3JYa4Ac+0hdpc2nn4xZsJRR9jse0SRhXzlTsZri2iEE2RwPA222TT2mXeEGDPtInaL+VJtpHOeBdpdjpR4w6GYQEDepQJ/JAKxeW1BEEoY2gLLFAK44E5p2kv60OHuN60f/VG0gGm0gyfiYmRHntbJ8Ud4Z4JOIq9eHRD/MwI42hWtKO0U9YH/pT2n7OH76FupOtZm2XAfqFgPi6B8YFNPGg/rPNY/ZAAwEo32rVQbjmDg33MzgvNMW0/7R1rVl3cni73GMXGMSPMSBHBt9Pf0mSQ8GkTEDpF79iNfoz2LNLMpsDPiJXSnH9C+RnKGdpV7Wm8XArCNiGfaR/pM2h/6TuCbqB3WTBCfXL26QLex+hBuvvBf7T/tJP0w55BjpxylDsX9gVhvtdK1Hu2gbiYmjTFA3GDRTbogFizEW/QHHQl2h7NRCO87v0NYRe9P2kXCiPSX24ryTHKYdi5krtP8klSrRhhdvTHP+iBvi6QPqATEEZQjEv4wNSNZUax+ivFDfiHNpS9ge54kywz5T9kiC17vhF2iXORex78xyi0fiQIKRMsIYiURK3Dwg1q8Wl8c1CNQ7yh99HOeC60wfznWI9paECOW0XtKC8x3JaMoQ5ZhYNWYBR0Iwzg83LbiGtW6Ssy/8Heef60k7xzUgtqWfo9/n/LNvJL3p/+PRwMr2gmQ57RI3IHg/2uTi79C30z+xfeInri37FuOqau0PfUbUg0gaxexhEsOUI9oytkNfyb7RvlX2m9QLYkquX9QXyiPlmPFhXEPOL/0w/R5tDe12LPVSuX+0E9z8pdywTzFujgRe1FViRNoItc6k1BIsKhB3h7nTQiNBsEBHGkEaA1caYRouOnqCJ4LGWs9J08gwmIlAnEaKhoXOnMpO0oJAJwY4dKxxd7taQBKJHII17ojQwEYyhtlCNOR0rAQ4vAeNEB1N5fT8IhoC3jMCFzLZ3KHgWLnLQKNGQoPBIg0Rx8+2q6Ez464E+0aDQ3BNx0+ATfKBIISAh//TadEw0blVPiNeFOeBTogGnHPGAIJEBx0qx8c1ikdF2GcC63pJBhpY7ljSYRAI0vjToXKXjHNG8MMUffYxAsRqSUICOe7a0MDTsHPu2SeSYiTg6ADYBo0sgTF3MGhs6djqzUCig6ehp9wxcCcZWlwvg30h+GZbHD9BTq1HzzgvlGUCOc515Qw37npwd6da0rI1xU6H60IQQpKLQXTsM+WJTqwWkmCxcHskWOjoKTsk5BhEFgd/rc2w4PpTrmJ9LwIRkgkEWwRflB2uV7F8UL5rzfwrHiPXhFk+7DPBJPtNB0uyJ84fv08ZiOnT1RCAE2Cxf/HhCQz2aAeoWwTT4DqTGCHYqfdcPe0V70/Sh+QHGAwSIJCsoe4RwMZjDyS/mRlHm1GJgQ3tSszUo30i2cE5pSyzTdqAmE7N+aY94rxUw3nleLh7SP0gUREDfo6PYJPgh7Yl7vLVm6od14P35e9IZjGAoA2jTeVc0CaSKKZOxuxUgvlasyKoSwxySOjFXXAGXhxrzEwiyUUSh3NGe1prlhSDjRi0s3+UMxL8lBXeh0CbgJhgmzYy1iKKWZ/VEPDSxtGug7+LQI62IhbljcepuM4xu6NafeEcsy36J9pjriuDFZLbnEfKL/WEfWxEXBPOL9tjf7me7A/biMejKcsxqKhXj9kvBnIMrKlz7Cvlmbaw8lzTHzKwoG+sNZMm9o++l2On/eUccc657rTTJAnYDvWoOIu1VuKBOsSNHwYm9G/0fZRD6h9tTswy5th5T/o86hD1uNo2aae5xsUkDjdGqIv0dSR4igN2tlGvD2F/YuYv7097RLmkfyNJRV2h7SHBR9xBu11rgFfcX8pvrJlYOeCP72OtzdbWsmRwFOsf8f6UOfpl2orYd8ol7TXXJvat8vxxLPT5UX8Z3JLsiTJGHEeZRtypb8vi1Qyqo+xFrBCzOVtbOwVxnUjwsC1wLYkLKLO0VfEYI+e32kLJRbHPtH3Ut5jhS1tDMrSIPo86xECWclkrKQ/afMoKZYe4gvNOfEAcQn9K28d7xawl+qpaj/h3dj8S7Qf7QczG4JgyxvGynyT7AglDykN8oES1a0wZI+6hXsY6eHFTj32nzyUBFjcP6cfY53qPBRJXxiPRoD3gHMVNxkj2UI5jNjfHXS32oA0t3ohgW+wjs7Hpy6ljxEkR99Urx8X2maQQZZ/4hdiK/pIyR2KEGJHrTntA+1trm5R52j76VPYJjA+izhJvRjmn7BNPx3WtVV84Dq5HbI/rwxdtCOMtyjBjkeifmSxQ60mIwPFybIH+jYQc/QbHTb/M+aBPinVfSYxW1qEi2hTqKsfDOaP8kKyhPjAbM+oDdYQETCRe6/V31H8Sy7TNxL9FtA+UHcoC249H76ptj1glbrxRhmk7adOpd8SsXF9w7GyTmIT2jH6mEgkkyhd9BWWQfjISqTH7krpNYjMer6b/iutX3L/4N7EK/Sznl3NDfaD/IVbhuCnXxXintVmsMim1xKPDiTsPoMEimOGOGw1ucVFjKiIzdGIWVTV0NnRQBF40jgQQIPCn4aCxZaBGh03lJHAsftJdLQzyCKgJ6piFw+CdgQoNUsz2oTFjCioD3VrBYXQ6ZLvj8Smy6zSA3O3mDgPBGINazgP7RqNda30kghaScLGAJ3c2GcARaDNAofFmoEGHynswyONObrXBcWWDRsfBHQASb7xHILijg6VBJ3iiE+BOV61BfBwz+0hwSUNOlj/QCJP4YR9jTaN626EzpdPlDhszaeioCSC4tnTMNL68F9e5OFOpXidFY88xxGM+caeDu+50CBwz+8fvkZiqF5AwoKPz4S54rUEb5SfuKrdFtUQdQSudDndNKH+UyZhO3sgjOwQGkZShXBCI1JuVUg13qhgMEoSQmCDQJnkcyWWSFnE3p7VFMmNAQecdC0QSPMUMAeo3nT9JCx6PIACgM6+1TlDxGChfBL60DVxbtsV+FdcwQr1HZZk5wu+TICcYjLpE0EFAwvcE8iQGox1g8FLr0S6SqZw76iYzKSLJBcoj9YxAm2tMEoxEEsdU79oQcBDUcMeddpQ6Qr2L4IbrEtPlaWcioVkP5Z9rQmBD/adtoa2hrnFNaV9JFnJNKhdfrkS7xr7E7By2RfBbnNVD0p4yTfBNMBwJoEq0pQTsBJoxe466TBkh8CXgZxBEv8G/2Q7tT2trJrA97oKyX8X1Cyk3xZkKDLopT7WuB+crPkmHhBjtMANX+g/aCOo0SVwGdFyrRmawBv6WAWcxmUXZjIEw7Rj9FYlCzkWtR5hp00jq8DuxGDfXiHPI3xbv5NO2xaedtoaBZjxewjnnmNkm/S7vSXnimpAsaG2R6VjXiXpPwoi/oc4S+HNThn4qZiLFIvSc91prd5CYpN/hfHPt6L+4S852iDUI9klaMCCKT8Zt7VhpG2j/43EVyjXvQTxCmWTgSdvCgI/6GYu7V4qyRPmIhfprtZPsW72bEMXf57iItxj40w6SpKD8kjCnLSN+YF9be0yRc1Wc2UY/S3tCeaSMMSCjfyPpwaAoZkG0ts5ctBn8fvERHGIY2j/KJn0JZaW1xXhJlPAIayS5iRuoXzFojUfIaW/p8+vNMIv94oZIPCJOXY44hutMeaJNI4HI9a+XjArEodQB3pvkBNtnwEi7ShzIeaDt55zSHtXrO3n/zu5HSHTTl8XxRhxIvSJWj6Qt55r62Fq8wA1F2nTaUOLpePQ40NayXa4TCYiYcVMP5Y6ECWWC884Am8E87T19G/WN7XJua/UhgWvHuWfGdeVaeYwliIuYCd7oh6HQzlEmaEvZR+ofCVr6D64n5Y66Qj2hDtbbblz7mI3LTVyOibaP/ikSyJSfmPVfr7zEEw6UC240sB2Ss8QCbIMb1+wzcTYJ6uIi27VwzonFiEmZvRmPZ4Pjps0n3qKvIwbh+hNT12oXIt6lLSaupa8L8cQF5YRkGjez6fvqlcHi+zDeIl5gjFnt8UbqJOW73qfRER9QxpitxfmjfvH+7A/9EGUoHi+l7NMXUF6rrUVLPeMmdkxMoL/gbzlH8UnqlKWYKUdMQ32sd7z0txFXRSxNOaNcUA9pM4gRKffse1sfY+6OTEp1A3EnhMpH50mAQ6dCUEkjRCBHh8Pgk0a+kYEyFZtGi8CLIIuAkQaTTgA0NjSeDMZbm05OR0JwE0ELWW/uxBDAVVu/obW7bqChoFEADQ7bJGiiQ2R/OG7u8hOAxYKrtfaPQSADXgZZNLDxWBtBEQkHOsSYikrDWS1LX4lBErO36Ojo+Ohg+FnxUwsJpkgO8dXaQpwcEw0jgQiDn5ipRUBM8MT/2XcaZgKxegutcoejuAgw7x2BIh0nCQASanSoDBIanfJPB0egwB1fygnBWwwIKI8M7gg+G/no9yIaepJEcUyUPQbyBGbxXHgj2yLJRofEzIJqa2wwiGdwHDMGqol9j86Z7XE3slgHqG8EE1zrRj+dkkCG+kVwyj5w14Wyx3Uo1odGPz2Iu3sEIQQv1DUSyASXEVRyF5pkDvWFMt3aHbwYTDOY5e4RQRyPY9HRc94i0GvkY+QZtDKwIyAhaKDeEmRw/BFsUGeYxQbKNsnYeu0Wv8MgjCAjprfT3kXCniCP4JAggyCi1nkkuIiEGr/P8VE/mBFB2SUwoX4xi422luQ3yfFag+NA+YxFfKkT8fgjgxbOH+U4ZqcUA7xq5ZpkXpwLgnDqKYPquEvHtYzp8uwb7VmtQTL1gHaNARbnn6CLu38MHmgnKBskUDhOyjSBPEmNSMJWHmMRsxwY5JGY4NhIrtB2RpsQx0CbFXf3q11jrkfMXmCGL/0cSR7ueDJgjuvFoI/r3dZPQuMmQyQYCd5JeMTxkHTlfTiG1tYVoYyR3KcdjEcMaZu5ppwv2gr6Er7nHFY7Z0UkZGPWAckjBg+UXfq6WDOQ4JjZqBw3db21ukcfTJKSa0v7TGBOXxeJJO7Ek6CL2WH1PkiB/pLjYABM3SDpSr9EO0sZ5Vi56cE5oZ2p9yEe/D4JBjBIYfDBMTPg5NqwHcox5ZSBNgn7qE+12gXOBeef9pl4oVadLy5G31psxDY5d/TZXA/Ofaxnwww0zlvxBlhr22OfYs1KyhjlmutCTEd9ZpYB+0e71JZH76ivtHWVA1YGdiQeolzWWwOPxHzcCIjBOXEksRXvwXFTlkjqMThtbf+IfxhYR1tJXMlxs4+UOZJKDIpp62kn2HZrj8vGbDC2SzmMwWzMsKV9pS8llmsk7ojlATraj8R7cX45Rvp1+mDqKO0A70H/S1KuMlartn9srzjbjEQv/TsDYcoG78MAPm6akNAklmvkcUraaWZL0a5wTSPeYP/4MBf2j3JDsqvWguSx38QXtAHcWCPuJmFN2aF/ph6SyCgmWetdC84Zv0t9i8fQid2IQ0jIRPI3kvb1Zs/H+9B/0LYQ80e/QWwaH1xAv078FY8sN3pTketIOSN2JGlBW0A5Z7+J26lzlBnOX71jpo2kvMVj3jFzLtYvC5WfLtjaJ0ESk9LPcq3B2CNmhnFOqC+M5+IRtHr9SOWMV+oBSbn4lL3ipz7W2yeOgXIRv8vYNBKDXF9iB9oo2gTGE8zsik8MrLbtmNlGsp3YjNiH8SvtCjcn4uZntGXsczxVUtxffi8W36ce8Rpln3rHOIn4g3LGeIZ2i76dfpC2srUPV5FJqW6FwQiVmcpOh0qnQuWhs6ZiM+hodAE2Gi0aRjoAGkc6h+JHbvMedLLxkaXVRENJoxVBNGgICCqpyCSNCC7rfYRrUSS2GNTRkNIA0cGzPZIyDCJoWLnrRFKKwVC1jqU40CfJQSaewSEdB0FHfNINDQ4Dppgd1eiCoSTbCMTZj2gsCVwZbBPM8zPekw63OLuhNXTyBA40jhwnnSFBIZ1XfBoMHWytjo+BTgRsxaQPwRwNaiz4R5DD/hHs1QtEiihzcaeAu8B0BpShWHeG7TEro57Y7zjPJC3izj1lhetOkoXOidfiU7caQXngbwnkON7KacdgcB/Tmat19JWPUDCYianv0bFRTrhLEzOb6h1nNQxGCIIZ6FEW6WAb/dSW2EfKHx0qxxnXj0CWMkinzL4x2ON4GQC0NqCIT4eMekPCmwEzCQvqCQOpRmZMFveRY2KfGGRSbmPdEAZ6xSns7HO1NYNC8TrxSCHJk1g7pHL2CPWn1uLwJCQIWClnHCuBOMfJIJuyy6wA2jwGOpRjBk2NzJAqYgARn/zDdWbmWTzuw77znvUei6NdjgERCSIG6QxyCKxjsEdSOZLWBO204fUSAsW7wZz7WDeBQJqkFMlRBkAEdtysoE2oF6xTL6OeMCOAwQ5lmT6AQLM4U5dkM9tvRPE9aesYCHN8keDhGGm/or1tbX2v+D/7w75xHrgO8Vg4gx4GGLFAdi2Vd9NJ8HDcBMS0YyQOKb/MUibZx/7WWhut1rYJ9mlHacPYR8p1JGXA9mo9ol5EwoSyS3KV5B2zrKgn3PFnhkQsYMs1aTShTj/EPvFFIol6VGznOXYSe7VmXIHyEuWXJAXtOwky6iPngDrBTR4G+CTGaTMaXT+QASjtHOc/HueIa0/7TdKnNcWBFv0nsyOIO+jjSZbFGle0E40+QhpllPMVM29Amxg3iTh2Zu3EB1I0sj2QoCjOvq68ltzUYd8r6wh9H79LMrL4uG9xRhB1j7a7OKO90ZslsX4WM2lo4+nfYiBHGWQQynuTlKMu15qpEvsdyQNuTJIQoH0nDqSMREzJNmONs3qKMShtMdtraz9C3amc5U+bybUlPuK8UYZjRg/lu96nTCPOO20A54+/JSZln+jfaaNoU+jP2Vd+vzjbu544j5QF2n0G/vRPxG881RDJI8pgJOvr7SO4ZvTBxNDUU5L5JF6ZxQLqdnyyXGsoV8W4kqQb5Y+2j36NG1dcX2Jqrnm9Nqa4fizHSv2Nx3qjLSA5xzlFo+0fMQI38In1aQPp07kexcf+SL4QVzRyw51rQqKI7VE+aEtJuEVShXaa69LaOn20e1F/KHfE48UPNaFfi0+RI64pzspq9NpE+aE+k/QqtlONJNGjznHTgEf9iLVo/0kCsf/EXNzs4Hi5zvVm3FJeuca0fSSxqIfE0fQ9xDG023Fzi5tZMRuuiGtOm8SNM9pOvicuJaYh7ovYiLrA33MTIm4cqTEmpbqBaDzjU1ZomKnQdFYMIOLRl3oNNtnfCMZpyBgkRkDDHUwacLbDNlu7Y8zgNBqkymQTPycxRkDCYJ5ggsCYxEzxY2yLigFkdIwcczy7zX7RWNHo07AxK4ukCgOzajOQ6Ngj413cL7ZPJ0fjT0dIR0CjSDKueBy17mSBjqLYmRUH6gQxdKgE/PHpbex/Iwm54nuyfYJKrgkJBc4DAXy9AJHOsHK6fnFhXvaHxCMdHtebhpgEJ51srURcHCd3d0kWMUiIOzHR+RG8ExzT6ZDAiLWYKs9hnL8YmMR7EtSwf5SZ+NhetkmihrtuBBHF96wl7opxjqgbDOIoA9xBjeQR15wBQbX9qzwHdPIx26X4u5x7zjXlu9YCqEXFAQx/y/vwf46R68D+kvhlQEXAWGs9jDh/BGvc7YvrU1xPgrJJwpJEKEEoyRDev5FAqVoAQPDFuWegS4KAstLaYyuV55NrSLDBeef/HB8Jrjh3DHjjk2KqHS9tFsdcfKwqFrUlQK81S7IS55myxPvyN7RJ1IUI6uKDCBgEEYwQ0HE+i0FtI7gmsa8EcMxU4VzG2hbUk+IHIBSRhIpFN2lDSKQQgFU+UkZ9JFkdAWK1x3Si7lLHI+CP2Q9xt5nBMueeARizV0gOck4ZoNS6Huw7QWTgUSyOkddJGjEgoN0jycDghGA+BvTVEl21kl+UOwYiDNBoC2LGbZyLWu1LoMzHrEGuNQlc9o36AeogwXpxplC9RBwJo+Ji29TdWBMItI0Ex/R3lKVG2qxq/Qzlk0CYfoMbLvGYOW1ErbVtKF8kBRkkUSaKbQh9Oe0B8QHHz35xDvm+2uLStQYa3ACLdofzQBIoBk3UFQZWtc5frVmGDMyob/TTJPkoRyQXiGNoL6rFC1Gu+bvip2xxvSnbzNag3sS+kTjkcSJU7l+1mTX8jDv6Ubc4fwzG4uYa16Tep3oWxWNxId6H2RUkyuJmR3vQN5H8qFz8vhibVF5L2kBmYzDwp49k5gPXprL9YNYGCetow1tLSNGeMGhlwBnoeymznANiOgad9KkMjiMuqfV4YRwD1794s5Nrw3YoI7HINzdqiZXo66rtZ1xj2jjiyOJMG46PuLQt/QhljLizMqbjBhhJbs5pzPZg5gVxQq1PtiweK4hP+Xv6nOLaNcQvzDom7o1PS+V4q4njpZ8p3qjgHEUcwLFSdkgkMUuKdoNzUKuexDIBxace2FcSKsR7seYkfQHtKftO/EF/Vk+Uz+Ijg/wdZYQ2gcQW140YmH661vbiXJGQiHaXa8tNFsoe1z2S1fXqR1ExWVos18QulGOSIsTTxU9CbXSmY6C80lbTnzIzPWYFcp5JMFVb4iSuDec7ZkLxvtQV2iyuJTED14V2ivia/o9ZUvGJgLXaadpjyjV9brU2mzJIjNpasizOMccTN8g5n7QJxG30H7H8BePNmD1P3FBrUgXXmFiRNoUyEXWdfoDtcvycE2ZHxSSAqKPF46VvoT2Px+v5njaUY6b8kqgnLqL9431oo4nHmN3WaBKzuzMptYQrNnQ0XHQQ/CymCxN01ZuxEY0q05XjjnMERtEA0BkxyGFWAIEX2WcanxhMVO4PFTWm4vO70dhFkEEjwx1zBk80DARBdFwxGKuF4LkyEGUwQKAf54Egnc6KgCTW9qhER8tAjAY5ZnoxwIlOlUaI46PxifVzCGa5e9baXTuCIRrsWh0QDS7TPovJskYfp6x2HCBAj0WCayW4CJZoSKs16jTaMXuBxpyOgKCWTrxyXYBqOM/x+ANonIvHxOCGgIE7CvFYSC3xuAB3/RioFz8unetDh08ChACMQJdrWGv9oggcSLCyreKgkQQhd6IodwQQBLMM+KOjr7x+1A1my0UgR+BJuai2jgvXiiCuOJOhVuBAOeVxhcq7rlwnAocIFEneEMy2tm4MA/QIHovieHiNaeYMOhoV+8u+FLdNW1NMvHGe682qK14P7jgVrwdBIWWUoJq6QX2kveDuXXFwWYnBL8kkgj+2Udw/2gICDNqDuMtYq16SaIpZd7G+B/WKQRPXkTaLtoaAi7t48ThBa+t7NRKQkmCKu8iU+2JSp4gkRwxoaAd5pJDjo14RlBXrPm0pr7eGMh+fYkO7V0zU83OSUrRnJB4JvKrdXSy2fyQqYxBBO1AZqFGGuE4R2BUTYkW0OzGrguRQzBxB1FHKJYMLZjTUStYW0Z7RztEekaThRgZBJ8cW7WtbEmOcF+otNx4IWGM9Lo6ZJA2JNwJW6ixJ8HgUoxGV5yO+5z3jUR7a3daSwLSNlFGOm+Pn+hX3g/PG4IkgnMEog17KUq01HeNcUHeLbVblYsAM9qJcUY9r1RNik3icM2b/FnFdGEjErFYGtSQOqIP1zhlxBwlGEqjFwTd1mAQXM0log2LtqsrzHe0ufSzbiT6GOkbbXFwjjPJDP8Q2OZf1kgwxWKJ/JeaKxE5lPWH2EI8TNTpLOcR5pu2LNqqaWmWaekHfRXmNpCR9X/GDcUh+0uZWW9el2v7QbnDDIQbvlLXix7lzvrgOtNOR5G/t8aFoN4tLKXDN2C6DYxCfctOJfra47mo1lCfaS+pAZR8b/Qj1OR7NrNaeU1Z474jt4lpwvNTTykclKUv0MbU+fRPEO/QJ1BNm5DB7jBtWzObhpkmUS94rblK2dt4oU8S1xSQS9a4485DrTT9E8oN6EjM7K4+bm148pcD1I2amvsU+xbqBtCUkBzl/XAt+p/hJgfVQDoi7Il6hHWA/uVEc68/WexSaOIK/j0+fpZ0u3oTjddodksCUUdqK1tZaK6L9JE6LG5TEQ5RLkhj0YZQ74sRG4ujKda8iUUbbGmtbxjphMfOv2jFzTRivxPXkeGjzq91kAGU+PqGxuD3+Lmb0sQ/EyiQaiblom6JdjbaE75kdF+Ok1pJw7GOtWYKxTRJrtLXUqbhxV8RNSa4/N5SpR4w7uY78DeePOkl9oW2hL6HP50ZYrbEm55n9CsyOIu6KT+6jDnNNSehRnogHSYwXb0CpPpNSS7hoxAjyWeOlMulAJ0VnVS9AoUIRqNHhkUVm+iKzhAia4u4rCS6mphNE0ZnVGiDTEDGIpAGjoSguIMrfM7hmsMFgpBjYsa1qi4dHB8L7M2glsKYT4c5OZSdfnM1CAq3Wx7jSeNIxkshhP0mKxSCUAQ6PG8RH29KZEijR4NW6AxqNL3ccYs2teuggY5H2eg0yj1KAuztsm06+8i4EA3wSG5X7UkRDzOCTRpRzGI8c8dgPHTvXi4FIBMzcCS4GjdVEx80+FRdwLK6PUHzkgW0zi63aow3R8fGe8alFdHoEZAy8uJNVXMCSgQU/q7YeWTUMkOiYCJ4YYBTLLcEEiTKmM1PGYr8rUQ/o/NifWH+GuzoEbjEgi7pIUMBAK2YOFMX1IUnHY2/US8oMj6aQkCnegaKuIIK8ejPWqCcMZCLpV/m7xbuIxTUyGhHXkHNO4jsGKOx7W9deqHY9iu0ASRECpOKna9VCsoL6ScDPjFDaFMo5AW+0Gwyc2O96STjKJIk63iuSq7RvBOQkuxiQsE1+j8EBA/JImNRDMr74OCr7wheDea4V5ZjrEm0Bv8tMhfjEuso6THmJMkbwBNox2izabx7X4JqwHd672scmF68n7V8EoxwP9Z72MI6N9q4YCFIOqz2iGcfIgIw6HmUC7G+****************************************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"}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "✅ 建议设置 max_features ≈ 9673\n"]}], "execution_count": 4}, {"metadata": {"ExecuteTime": {"end_time": "2025-04-12T14:26:13.021679Z", "start_time": "2025-04-12T14:26:12.835566Z"}}, "cell_type": "code", "source": ["import matplotlib.pyplot as plt\n", "import numpy as np\n", "\n", "# 假设 counter 是你的词频Counter\n", "sorted_freqs = np.array(sorted(counter.values(), reverse=True))\n", "total_freq = sorted_freqs.sum()\n", "cumulative_freq = np.cumsum(sorted_freqs) / total_freq\n", "\n", "# 画累计覆盖率曲线\n", "plt.figure(figsize=(10,6))\n", "plt.plot(range(1, len(sorted_freqs)+1), cumulative_freq)\n", "plt.xlabel('特征数')\n", "plt.ylabel('累计覆盖率')\n", "plt.title('累计覆盖率 vs 特征数')\n", "plt.grid(True)\n", "\n", "# 标出90%、95%、99%点\n", "for threshold in [0.9, 0.95, 0.99]:\n", "    idx = np.argmax(cumulative_freq >= threshold)\n", "    plt.scatter(idx, cumulative_freq[idx], label=f'{threshold*100:.0f}% @ {idx}')\n", "    plt.text(idx, cumulative_freq[idx], f'{idx}特征', fontsize=8, ha='right')\n", "\n", "plt.legend()\n", "plt.show()\n"], "id": "8414c7c73d2c17c", "outputs": [{"data": {"text/plain": ["<Figure size 1000x600 with 1 Axes>"], "image/png": "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"}, "metadata": {}, "output_type": "display_data"}], "execution_count": 5}, {"metadata": {}, "cell_type": "markdown", "source": "# TF-IDF", "id": "17ba6f3dd4e479c3"}, {"metadata": {"ExecuteTime": {"end_time": "2025-04-11T15:13:32.595750Z", "start_time": "2025-04-11T15:12:48.942921Z"}}, "cell_type": "code", "source": ["from scipy.sparse import hstack\n", "\n", "# 建议限制特征数量 (max_features)，避免维度过高\n", "# ngram_range=(1, 2) 表示考虑单个词和相邻双词组合\n", "tfidf_vectorizer = TfidfVectorizer(\n", "    tokenizer=chinese_tokenizer, # 使用 jieba 分词\n", "    max_features=9500,         # 限制最大特征数，例如 5000\n", "    ngram_range=(1, 2),        # 只考虑单个词 (可以试试 (1, 2))\n", "    stop_words=stopwords, # 可以加载停用词表 (需要自己实现 load_stopwords)\n", "    token_pattern=None         # 因为我们提供了 tokenizer，所以设为 None\n", ")\n", "\n", "print(\"拟合 TF-IDF 向量化器...\")\n", "# 注意：这里我们是在整个 df 上 fit_transform，严格来说应该在训练集上 fit_transform\n", "# 在测试集上只用 transform。这里为了演示，先在整个 df 上操作。\n", "# 在实际模型训练流程中，需要将数据分割后再操作。\n", "content_tfidf_features = tfidf_vectorizer.fit_transform(text_to_process)\n", "\n", "print(f\"TF-IDF 特征提取完成，特征维度: {content_tfidf_features.shape}\")\n", "print(\"TF-IDF 特征是稀疏矩阵格式。\")\n", "# 可以考虑对 'title', 'cover_ocr_content', 'video_content' 执行类似操作，然后合并特征\n", "print(\"-\" * 30)\n", "\n", "# (可选) 提及 Embeddings:\n", "# print(\"另一种文本处理方法是使用词向量 (Embeddings)，例如 Word2Vec 或预训练 BERT。\")\n", "# print(\"这通常涉及更复杂的步骤：加载模型 -> 文本转向量 -> 聚合向量 (如平均池化)。\")\n", "# print(\"Embeddings 能捕捉语义信息，但计算量更大，实现更复杂。\")\n", "# print(\"-\" * 30)\n", "\n", "\n", "# --- 4. 类别特征编码 ---\n", "\n", "print(\"开始处理类别特征...\")\n", "\n", "# 选择要进行独热编码的类别列\n", "# 假设 'site_id', 'gender', 'post_type', 'publish_weekday' 是目标列\n", "# 注意：需要确保这些列在上一步已经被合理处理（如填充NaN）\n", "categorical_cols = ['site_id', 'gender', 'post_type', 'publish_weekday']\n", "\n", "# 检查列是否存在，并填充可能的 NaN (例如用 'Unknown')\n", "for col in categorical_cols:\n", "    if col not in df.columns:\n", "        print(f\"警告: 类别列 '{col}' 不存在于 DataFrame 中，将跳过。\")\n", "        categorical_cols.remove(col) # 从列表中移除不存在的列\n", "    else:\n", "        # 确保是字符串类型，并填充 NaN\n", "        df[col] = df[col].astype(str).fillna('Unknown')\n", "\n", "print(f\"将要进行独热编码的列: {categorical_cols}\")\n", "\n", "onehot_encoder = OneHotEncoder(\n", "    handle_unknown='ignore', # 忽略测试集中未出现过的值\n", "    sparse_output=True       # 输出稀疏矩阵，与 TF-IDF 兼容\n", ")\n", "\n", "print(\"拟合 OneHotEncoder...\")\n", "# 同样，这里在整个 df 上操作仅为演示，实际应在训练集拟合，在测试集转换\n", "categorical_features_encoded = onehot_encoder.fit_transform(df[categorical_cols])\n", "\n", "print(f\"独热编码完成，特征维度: {categorical_features_encoded.shape}\")\n", "print(\"独热编码特征也是稀疏矩阵格式。\")\n", "print(\"-\" * 30)\n", "\n", "# (可选) 提及 Target Encoding:\n", "# print(\"对于高基数类别特征 (如 'city' 或 'uid')，可以考虑 Target Encoding。\")\n", "# print(\"但 Target Encoding 容易导致数据泄露，需要结合交叉验证谨慎实现。\")\n", "# print(\"-\" * 30)\n", "\n", "\n", "# --- 5. 数值特征缩放 ---\n", "\n", "print(\"开始处理数值特征...\")\n", "\n", "# 选择需要进行缩放的数值列\n", "# 假设这些列已在上一步骤中被清洗和创建\n", "numerical_cols = [\n", "    'fans_cnt_log1p',\n", "    'coin_cnt_log1p',\n", "    'age_cleaned',\n", "    'publish_year',\n", "    'publish_month',\n", "    'publish_day'\n", "    # 可能还包括 video_cnt_cleaned, duration_seconds_cleaned 等 (需先确保存在且已清洗/填充NaN)\n", "]\n", "\n", "# 检查并处理数值列中的 NaN (例如用中位数填充) - 这一步很重要！\n", "for col in numerical_cols:\n", "     if col not in df.columns:\n", "        print(f\"警告: 数值列 '{col}' 不存在于 DataFrame 中，将跳过。\")\n", "        numerical_cols.remove(col)\n", "     else:\n", "        if df[col].isnull().any():\n", "            median_val = df[col].median()\n", "            print(f\"警告: 数值列 '{col}' 存在 NaN，将用中位数 {median_val:.2f} 填充。\")\n", "            df[col].fillna(median_val, inplace=True)\n", "\n", "print(f\"将要进行标准化的数值列: {numerical_cols}\")\n", "\n", "scaler = StandardScaler()\n", "\n", "print(\"拟合 StandardScaler...\")\n", "# 同样，这里在整个 df 上操作仅为演示\n", "numerical_features_scaled = scaler.fit_transform(df[numerical_cols])\n", "\n", "print(\"数值特征标准化完成。\")\n", "print(f\"标准化后的数值特征维度: {numerical_features_scaled.shape}\")\n", "print(\"标准化后的数值特征是密集 NumPy 数组格式。\")\n", "print(\"-\" * 30)\n", "\n", "\n", "# --- 6. 合并所有特征 ---\n", "\n", "print(\"开始合并所有处理好的特征...\")\n", "\n", "# 检查所有特征部分是否都已成功生成\n", "# 这里假设 content_tfidf_features, categorical_features_encoded, numerical_features_scaled 都已生成\n", "# 注意：如果某个部分（如文本特征）未处理或处理失败，需要调整这里的合并逻辑\n", "\n", "# 使用 scipy.sparse.hstack 水平合并特征\n", "# hstack 可以自动处理稀疏和密集矩阵的混合（它会将密集矩阵转为稀疏）\n", "all_features_processed = hstack([\n", "    content_tfidf_features,         # 文本 TF-IDF (稀疏)\n", "    categorical_features_encoded, # 类别独热编码 (稀疏)\n", "    numerical_features_scaled      # 数值标准化 (密集，hstack 会自动转稀疏)\n", "], format='csr') # 推荐使用 'csr' 或 'csc' 格式，对后续模型训练通常更高效\n", "\n", "print(\"所有特征合并完成！\")\n", "print(f\"最终合并后的特征矩阵维度: {all_features_processed.shape}\")\n", "print(f\"最终特征矩阵格式: {type(all_features_processed)}\")\n", "\n", "# 这个 all_features_processed 就是可以输入到模型的特征矩阵 X\n", "# 目标变量 y 就是 df['interaction_cnt'] (或 log1p 变换后的)\n"], "id": "7d9c617d01c42ecb", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["拟合 TF-IDF 向量化器...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["D:\\PythonProject\\pythonProject\\.venv\\lib\\site-packages\\sklearn\\feature_extraction\\text.py:402: UserWarning: Your stop_words may be inconsistent with your preprocessing. Tokenizing the stop words generated tokens ['##', \"'\", '三部', '下', '不', '二册', '使', '全', '十册', '只', '唷', '啪', '喔', '天', '末', '漫', '特', '见', '设', '说', '达', '阿加莎'] not in stop_words.\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["TF-IDF 特征提取完成，特征维度: (585276, 9500)\n", "TF-IDF 特征是稀疏矩阵格式。\n", "------------------------------\n", "开始处理类别特征...\n", "将要进行独热编码的列: ['site_id', 'gender', 'post_type', 'publish_weekday']\n", "拟合 OneHotEncoder...\n", "独热编码完成，特征维度: (585276, 20)\n", "独热编码特征也是稀疏矩阵格式。\n", "------------------------------\n", "开始处理数值特征...\n", "将要进行标准化的数值列: ['fans_cnt_log1p', 'coin_cnt_log1p', 'age_cleaned', 'publish_year', 'publish_month', 'publish_day']\n", "拟合 StandardScaler...\n", "数值特征标准化完成。\n", "标准化后的数值特征维度: (585276, 6)\n", "标准化后的数值特征是密集 NumPy 数组格式。\n", "------------------------------\n", "开始合并所有处理好的特征...\n", "所有特征合并完成！\n", "最终合并后的特征矩阵维度: (585276, 9526)\n", "最终特征矩阵格式: <class 'scipy.sparse._csr.csr_matrix'>\n"]}], "execution_count": 6}, {"metadata": {"ExecuteTime": {"end_time": "2025-04-12T15:01:17.316657Z", "start_time": "2025-04-12T14:46:24.541882Z"}}, "cell_type": "code", "source": ["from sentence_transformers import SentenceTransformer\n", "import numpy as np\n", "from scipy.sparse import hstack, csr_matrix\n", "from sklearn.preprocessing import StandardScaler\n", "from sklearn.preprocessing import OneHotEncoder\n", "\n", "# ---------------------------\n", "# 1. 文本向量化（使用预训练模型）\n", "# ---------------------------\n", "# 这里你可以选择使用 BERT 或 BGE-M3 模型\n", "# 若使用 BERT 中文版：\n", "# model_embed = SentenceTransformer('bert-base-chinese')\n", "# 若使用 BGE-M3 模型（假设该模型已经上传至 Hugging Face）：\n", "model_embed = SentenceTransformer('sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2', device='cuda')\n", "\n", "print(\"开始对文本进行向量化...\")\n", "\n", "# 对 text_to_process（列表）中的每个文本生成嵌入，支持批量计算加速\n", "# 这里的 text_to_process 应该是已经经过清洗的文本\n", "text_embeddings = model_embed.encode(text_to_process, show_progress_bar=True, batch_size=32)\n", "\n", "# 查看嵌入维度，例如 BERT 一般为768，BGE-M3 可能不同\n", "print(f\"文本嵌入生成完成，形状: {text_embeddings.shape}\")\n", "# 将得到的 dense 嵌入转换成稀疏矩阵，以便与其他稀疏/密集特征合并\n", "text_embeddings_sparse = csr_matrix(text_embeddings)\n", "\n", "\n"], "id": "cc57aa8f987ebdd5", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["开始对文本进行向量化...\n"]}, {"data": {"text/plain": ["Batches:   0%|          | 0/18290 [00:00<?, ?it/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "43ea70f4ed0247ae889f68706d5a2b2d"}}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["文本嵌入生成完成，形状: (585276, 384)\n", "开始处理类别特征...\n", "将要进行独热编码的列: ['site_id', 'gender', 'post_type', 'publish_weekday']\n", "独热编码完成，特征维度: (585276, 20)\n", "开始处理数值特征...\n", "将要进行标准化的数值列: ['fans_cnt_log1p', 'coin_cnt_log1p', 'age_cleaned', 'publish_year', 'publish_month', 'publish_day']\n", "数值特征标准化完成，维度: (585276, 6)\n", "开始合并所有处理好的特征...\n", "所有特征合并完成！\n", "最终合并后的特征矩阵维度: (585276, 410)\n", "最终特征矩阵格式: <class 'scipy.sparse._csr.csr_matrix'>\n"]}], "execution_count": 9}, {"metadata": {"ExecuteTime": {"end_time": "2025-04-12T15:03:21.852926Z", "start_time": "2025-04-12T15:03:16.732544Z"}}, "cell_type": "code", "source": ["# ---------------------------\n", "# 2. 类别特征编码（独热编码），代码与原来基本一致\n", "# ---------------------------\n", "print(\"开始处理类别特征...\")\n", "\n", "# 假设 df 为原始 DataFrame，categorical_cols 为目标列\n", "categorical_cols = ['site_id', 'gender', 'post_type', 'publish_weekday']\n", "\n", "# 检查列是否存在并填充缺失值\n", "for col in categorical_cols:\n", "    if col not in df.columns:\n", "        print(f\"警告: 类别列 '{col}' 不存在，将跳过。\")\n", "        categorical_cols.remove(col)\n", "    else:\n", "        df[col] = df[col].astype(str).fillna('Unknown')\n", "\n", "print(f\"将要进行独热编码的列: {categorical_cols}\")\n", "\n", "onehot_encoder = OneHotEncoder(handle_unknown='ignore', sparse_output=True)\n", "categorical_features_encoded = onehot_encoder.fit_transform(df[categorical_cols])\n", "print(f\"独热编码完成，特征维度: {categorical_features_encoded.shape}\")\n", "\n", "# ---------------------------\n", "# 3. 数值特征缩放，代码基本不变\n", "# ---------------------------\n", "print(\"开始处理数值特征...\")\n", "\n", "numerical_cols = [\n", "    'fans_cnt_log1p',\n", "    'coin_cnt_log1p',\n", "    'age_cleaned',\n", "    'publish_year',\n", "    'publish_month',\n", "    'publish_day'\n", "]\n", "\n", "# 填充数值列中的 NaN\n", "for col in numerical_cols:\n", "    if col not in df.columns:\n", "        print(f\"警告: 数值列 '{col}' 不存在，将跳过。\")\n", "        numerical_cols.remove(col)\n", "    else:\n", "        if df[col].isnull().any():\n", "            median_val = df[col].median()\n", "            print(f\"警告: 数值列 '{col}' 存在 NaN，将用中位数 {median_val:.2f} 填充。\")\n", "            df[col].fillna(median_val, inplace=True)\n", "\n", "print(f\"将要进行标准化的数值列: {numerical_cols}\")\n", "\n", "scaler = StandardScaler()\n", "numerical_features_scaled = scaler.fit_transform(df[numerical_cols])\n", "print(f\"数值特征标准化完成，维度: {numerical_features_scaled.shape}\")\n", "\n", "# 将数值特征转换为稀疏矩阵\n", "numerical_features_sparse = csr_matrix(numerical_features_scaled)\n", "\n", "# ---------------------------\n", "# 4. 合并所有特征\n", "# ---------------------------\n", "print(\"开始合并所有处理好的特征...\")\n", "\n", "# 合并文本嵌入（稀疏）、类别独热编码（稀疏）和数值特征（稀疏）\n", "all_features_processed = hstack([\n", "    text_embeddings_sparse,           # 文本向量（稀疏）\n", "    categorical_features_encoded,     # 独热编码的类别特征（稀疏）\n", "    numerical_features_sparse         # 数值特征（稀疏）\n", "], format='csr')\n", "\n", "print(\"所有特征合并完成！\")\n", "print(f\"最终合并后的特征矩阵维度: {all_features_processed.shape}\")\n", "print(f\"最终特征矩阵格式: {type(all_features_processed)}\")"], "id": "a7cc5e0496c46379", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["开始处理类别特征...\n", "将要进行独热编码的列: ['site_id', 'gender', 'post_type', 'publish_weekday']\n", "独热编码完成，特征维度: (585276, 20)\n", "开始处理数值特征...\n", "将要进行标准化的数值列: ['fans_cnt_log1p', 'coin_cnt_log1p', 'age_cleaned', 'publish_year', 'publish_month', 'publish_day']\n", "数值特征标准化完成，维度: (585276, 6)\n", "开始合并所有处理好的特征...\n", "所有特征合并完成！\n", "最终合并后的特征矩阵维度: (585276, 410)\n", "最终特征矩阵格式: <class 'scipy.sparse._csr.csr_matrix'>\n"]}], "execution_count": 11}, {"metadata": {"ExecuteTime": {"end_time": "2025-04-12T15:09:09.406012Z", "start_time": "2025-04-12T15:09:09.384976Z"}}, "cell_type": "code", "source": ["import numpy as np\n", "\n", "# --- 4. 目标变量转换 ---\n", "\n", "# 在训练集上对目标变量进行对数转换\n", "y_train_log = np.log1p(df[target])\n", "print(\"目标变量转换完成，训练集前5条转换结果：\")\n", "print(y_train_log.head())\n"], "id": "6b77c2051f115ad", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["目标变量转换完成，训练集前5条转换结果：\n", "0    0.000000\n", "1    0.000000\n", "2    0.000000\n", "3    0.000000\n", "4    1.791759\n", "Name: interaction_cnt, dtype: float64\n"]}], "execution_count": 12}, {"metadata": {"ExecuteTime": {"end_time": "2025-04-12T15:11:13.768318Z", "start_time": "2025-04-12T15:09:40.165875Z"}}, "cell_type": "code", "source": ["import lightgbm as lgb\n", "from lightgbm import early_stopping, log_evaluation\n", "\n", "target = 'interaction_cnt'\n", "y = y_train_log\n", "\n", "# 划分训练集和验证集，这里采用 80% 训练，20% 验证\n", "X_train, X_val, y_train, y_val = train_test_split(all_features_processed, y, test_size=0.2, random_state=42)\n", "\n", "print(\"训练集特征矩阵维度：\", X_train.shape)\n", "print(\"验证集特征矩阵维度：\", X_val.shape)\n", "\n", "# 定义 callbacks\n", "callbacks = [log_evaluation(period=100), early_stopping(stopping_rounds=30)]\n", "\n", "# 初始化 LightGBM 模型\n", "# objective='regression_l1' 使用L1损失（对应MAE），random_state 保证结果复现\n", "model = lgb.LGBMRegressor(\n", "    objective='regression_l1',\n", "    random_state=42,\n", "    learning_rate=0.01,\n", "    n_estimators=4000,\n", "    device='gpu',\n", ")\n", "\n", "print(\"开始训练 LightGBM 模型...\")\n", "# 使用验证集进行早停训练\n", "model.fit(\n", "    X_train, y_train,\n", "    eval_set=[(X_val, y_val)],\n", "    eval_metric='mae',           # MAE 指标\n", "    callbacks=callbacks,\n", ")\n", "\n", "print(\"模型训练完成！\")\n", "\n", "# 预测验证集数据（预测结果是对数转换后的值，需要进行 np.expm1 还原）\n", "y_val_pred_log = model.predict(X_val)\n", "y_val_pred = np.expm1(y_val_pred_log)\n", "\n", "print(\"还原为原始尺度后的部分预测结果：\")\n", "print(y_val_pred[:10])"], "id": "ce3d226c9bc91248", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["训练集特征矩阵维度： (468220, 410)\n", "验证集特征矩阵维度： (117056, 410)\n", "开始训练 LightGBM 模型...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["D:\\PythonProject\\pythonProject\\.venv\\lib\\site-packages\\joblib\\externals\\loky\\backend\\context.py:136: UserWarning: Could not find the number of physical cores for the following reason:\n", "[WinError 2] 系统找不到指定的文件。\n", "Returning the number of logical cores instead. You can silence this warning by setting LOKY_MAX_CPU_COUNT to the number of cores you want to use.\n", "  warnings.warn(\n", "  File \"D:\\PythonProject\\pythonProject\\.venv\\lib\\site-packages\\joblib\\externals\\loky\\backend\\context.py\", line 257, in _count_physical_cores\n", "    cpu_info = subprocess.run(\n", "  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\subprocess.py\", line 503, in run\n", "    with Popen(*popenargs, **kwargs) as process:\n", "  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\subprocess.py\", line 971, in __init__\n", "    self._execute_child(args, executable, preexec_fn, close_fds,\n", "  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\subprocess.py\", line 1440, in _execute_child\n", "    hp, ht, pid, tid = _winapi.CreateProcess(executable, args,\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[LightGBM] [Info] This is the GPU trainer!!\n", "[LightGBM] [Info] Total Bins 98488\n", "[LightGBM] [Info] Number of data points in the train set: 468220, number of used features: 409\n", "[LightGBM] [Info] Using GPU Device: NVIDIA GeForce RTX 4060 Laptop GPU, Vendor: NVIDIA Corporation\n", "[LightGBM] [Info] Compiling OpenCL Kernel with 256 bins...\n", "[LightGBM] [Info] GPU programs have been built\n", "[LightGBM] [Info] Size of histogram bin entry: 8\n", "[LightGBM] [Info] 392 dense feature groups (175.04 MB) transferred to GPU in 0.114142 secs. 1 sparse feature groups\n", "Training until validation scores don't improve for 30 rounds\n", "[100]\tvalid_0's l1: 0.741415\n", "[200]\tvalid_0's l1: 0.648084\n", "[300]\tvalid_0's l1: 0.620328\n", "[400]\tvalid_0's l1: 0.612264\n", "[500]\tvalid_0's l1: 0.608737\n", "[600]\tvalid_0's l1: 0.606168\n", "[700]\tvalid_0's l1: 0.604482\n", "[800]\tvalid_0's l1: 0.599452\n", "[900]\tvalid_0's l1: 0.594543\n", "[1000]\tvalid_0's l1: 0.594178\n", "[1100]\tvalid_0's l1: 0.593635\n", "[1200]\tvalid_0's l1: 0.593143\n", "[1300]\tvalid_0's l1: 0.592844\n", "[1400]\tvalid_0's l1: 0.592765\n", "[1500]\tvalid_0's l1: 0.592447\n", "[1600]\tvalid_0's l1: 0.592443\n", "[1700]\tvalid_0's l1: 0.592441\n", "[1800]\tvalid_0's l1: 0.592268\n", "[1900]\tvalid_0's l1: 0.592264\n", "[2000]\tvalid_0's l1: 0.590978\n", "[2100]\tvalid_0's l1: 0.589586\n", "[2200]\tvalid_0's l1: 0.587323\n", "[2300]\tvalid_0's l1: 0.586161\n", "[2400]\tvalid_0's l1: 0.585028\n", "[2500]\tvalid_0's l1: 0.584351\n", "[2600]\tvalid_0's l1: 0.58416\n", "[2700]\tvalid_0's l1: 0.584066\n", "[2800]\tvalid_0's l1: 0.583668\n", "[2900]\tvalid_0's l1: 0.583553\n", "[3000]\tvalid_0's l1: 0.583547\n", "[3100]\tvalid_0's l1: 0.583544\n", "[3200]\tvalid_0's l1: 0.583542\n", "[3300]\tvalid_0's l1: 0.583541\n", "[3400]\tvalid_0's l1: 0.583541\n", "[3500]\tvalid_0's l1: 0.583541\n", "[3600]\tvalid_0's l1: 0.58354\n", "[3700]\tvalid_0's l1: 0.583446\n", "[3800]\tvalid_0's l1: 0.583414\n", "Early stopping, best iteration is:\n", "[3825]\tvalid_0's l1: 0.58339\n", "模型训练完成！\n"]}, {"name": "stderr", "output_type": "stream", "text": ["D:\\PythonProject\\pythonProject\\.venv\\lib\\site-packages\\sklearn\\utils\\validation.py:2739: UserWarning: X does not have valid feature names, but LGBMRegressor was fitted with feature names\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["还原为原始尺度后的部分预测结果：\n", "[ 9.99994795e+00 -1.13287216e-07  7.06441497e+00  5.43434903e-02\n", "  8.62362957e-06  1.00000007e+01  1.75389661e-02  6.88129385e-06\n", "  5.91311077e-01  4.64100439e-02]\n"]}], "execution_count": 14}, {"metadata": {"ExecuteTime": {"end_time": "2025-04-12T15:16:39.805426Z", "start_time": "2025-04-12T15:16:35.746300Z"}}, "cell_type": "code", "source": ["import xgboost as xgb\n", "from sklearn.model_selection import train_test_split\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "\n", "# 假设 y_train_log 已经定义，all_features_processed 为合并后的特征矩阵\n", "# 注意：y_train_log 是经过 np.log1p 转换后的目标变量\n", "target = 'interaction_cnt'\n", "y = y_train_log  # 对数转换后的目标变量\n", "\n", "# 划分训练集和验证集 (80% 训练，20% 验证)\n", "X_train, X_val, y_train, y_val = train_test_split(\n", "    all_features_processed, y, test_size=0.2, random_state=42\n", ")\n", "\n", "print(\"训练集特征矩阵维度：\", X_train.shape)\n", "print(\"验证集特征矩阵维度：\", X_val.shape)\n", "\n", "# 将数据转换为 XGBoost 的 DMatrix 格式\n", "dtrain = xgb.DMatrix(X_train, label=y_train)\n", "dval   = xgb.DMatrix(X_val, label=y_val)\n", "\n", "# 定义模型参数，同时启用 GPU 加速\n", "params = {\n", "    'objective': 'reg:absoluteerror',  # 采用 L1 损失 (MAE)\n", "    'eval_metric': 'mae',              # 评估指标为 MAE\n", "    'learning_rate': 0.01,\n", "    'tree_method': 'gpu_hist',         # 使用 GPU 加速的直方图算法\n", "    'predictor': 'gpu_predictor',\n", "    'seed': 42,\n", "}\n", "\n", "# 设置监控的验证集，键名 'eval' 可随意命名\n", "evals = [(dval, 'eval')]\n", "\n", "num_boost_round = 4000\n", "early_stopping_rounds = 30\n", "\n", "print(\"开始训练 XGBoost 模型...\")\n", "bst = xgb.train(\n", "    params,\n", "    dtrain,\n", "    num_boost_round=num_boost_round,\n", "    evals=evals,\n", "    early_stopping_rounds=early_stopping_rounds,  # 如果验证集指标连续 30 轮无改善，则提前停止训练\n", "    verbose_eval=100                             # 每 100 轮输出一次日志\n", ")\n", "print(\"模型训练完成！\")\n", "\n", "# 使用模型对验证集进行预测\n", "y_val_pred_log = bst.predict(dval)\n", "# 因为目标变量在训练前使用了 np.log1p 转换，预测结果需要使用 np.expm1 还原原始尺度\n", "y_val_pred = np.expm1(y_val_pred_log)\n", "\n", "print(\"还原为原始尺度后的部分预测结果：\")\n", "print(y_val_pred[:10])\n"], "id": "d25dad1c126f25f", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["训练集特征矩阵维度： (468220, 410)\n", "验证集特征矩阵维度： (117056, 410)\n", "开始训练 XGBoost 模型...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["D:\\PythonProject\\pythonProject\\.venv\\lib\\site-packages\\xgboost\\callback.py:386: UserWarning: [23:16:39] WARNING: C:\\actions-runner\\_work\\xgboost\\xgboost\\src\\common\\error_msg.cc:27: The tree method `gpu_hist` is deprecated since 2.0.0. To use GPU training, set the `device` parameter to CUDA instead.\n", "\n", "    E.g. tree_method = \"hist\", device = \"cuda\"\n", "\n", "  self.starting_round = model.num_boosted_rounds()\n", "D:\\PythonProject\\pythonProject\\.venv\\lib\\site-packages\\xgboost\\callback.py:386: UserWarning: [23:16:39] WARNING: C:\\actions-runner\\_work\\xgboost\\xgboost\\src\\learner.cc:738: \n", "Parameters: { \"predictor\" } are not used.\n", "\n", "  self.starting_round = model.num_boosted_rounds()\n"]}, {"ename": "XGBoostError", "evalue": "[23:16:39] C:\\actions-runner\\_work\\xgboost\\xgboost\\src\\common\\hist_util.cu:106: Check failed: avail > 0 (0 vs. 0) : No GPU memory is left, are you using RMM? If so, please install XGBoost with RMM support. If you are using other types of memory pool, please consider reserving a portion of the GPU memory for XGBoost.", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mXGBoostError\u001b[0m                              <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[17], line 40\u001b[0m\n\u001b[0;32m     37\u001b[0m early_stopping_rounds \u001b[38;5;241m=\u001b[39m \u001b[38;5;241m30\u001b[39m\n\u001b[0;32m     39\u001b[0m \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m开始训练 XGBoost 模型...\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m---> 40\u001b[0m bst \u001b[38;5;241m=\u001b[39m \u001b[43mxgb\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mtrain\u001b[49m\u001b[43m(\u001b[49m\n\u001b[0;32m     41\u001b[0m \u001b[43m    \u001b[49m\u001b[43mparams\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m     42\u001b[0m \u001b[43m    \u001b[49m\u001b[43mdtrain\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m     43\u001b[0m \u001b[43m    \u001b[49m\u001b[43mnum_boost_round\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mnum_boost_round\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m     44\u001b[0m \u001b[43m    \u001b[49m\u001b[43mevals\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mevals\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m     45\u001b[0m \u001b[43m    \u001b[49m\u001b[43mearly_stopping_rounds\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mearly_stopping_rounds\u001b[49m\u001b[43m,\u001b[49m\u001b[43m  \u001b[49m\u001b[38;5;66;43;03m# 如果验证集指标连续 30 轮无改善，则提前停止训练\u001b[39;49;00m\n\u001b[0;32m     46\u001b[0m \u001b[43m    \u001b[49m\u001b[43mverbose_eval\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;241;43m100\u001b[39;49m\u001b[43m                             \u001b[49m\u001b[38;5;66;43;03m# 每 100 轮输出一次日志\u001b[39;49;00m\n\u001b[0;32m     47\u001b[0m \u001b[43m)\u001b[49m\n\u001b[0;32m     48\u001b[0m \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m模型训练完成！\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[0;32m     50\u001b[0m \u001b[38;5;66;03m# 使用模型对验证集进行预测\u001b[39;00m\n", "File \u001b[1;32mD:\\PythonProject\\pythonProject\\.venv\\lib\\site-packages\\xgboost\\core.py:729\u001b[0m, in \u001b[0;36mrequire_keyword_args.<locals>.throw_if.<locals>.inner_f\u001b[1;34m(*args, **kwargs)\u001b[0m\n\u001b[0;32m    727\u001b[0m \u001b[38;5;28;01mfor\u001b[39;00m k, arg \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mzip\u001b[39m(sig\u001b[38;5;241m.\u001b[39mparameters, args):\n\u001b[0;32m    728\u001b[0m     kwargs[k] \u001b[38;5;241m=\u001b[39m arg\n\u001b[1;32m--> 729\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m func(\u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs)\n", "File \u001b[1;32mD:\\PythonProject\\pythonProject\\.venv\\lib\\site-packages\\xgboost\\training.py:183\u001b[0m, in \u001b[0;36mtrain\u001b[1;34m(params, dtrain, num_boost_round, evals, obj, maximize, early_stopping_rounds, evals_result, verbose_eval, xgb_model, callbacks, custom_metric)\u001b[0m\n\u001b[0;32m    181\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m cb_container\u001b[38;5;241m.\u001b[39mbefore_iteration(bst, i, dtrain, evals):\n\u001b[0;32m    182\u001b[0m     \u001b[38;5;28;01mbreak\u001b[39;00m\n\u001b[1;32m--> 183\u001b[0m \u001b[43mbst\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mupdate\u001b[49m\u001b[43m(\u001b[49m\u001b[43mdtrain\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43miteration\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mi\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mfobj\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mobj\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    184\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m cb_container\u001b[38;5;241m.\u001b[39mafter_iteration(bst, i, dtrain, evals):\n\u001b[0;32m    185\u001b[0m     \u001b[38;5;28;01mbreak\u001b[39;00m\n", "File \u001b[1;32mD:\\PythonProject\\pythonProject\\.venv\\lib\\site-packages\\xgboost\\core.py:2246\u001b[0m, in \u001b[0;36mBooster.update\u001b[1;34m(self, dtrain, iteration, fobj)\u001b[0m\n\u001b[0;32m   2243\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_assign_dmatrix_features(dtrain)\n\u001b[0;32m   2245\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m fobj \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[1;32m-> 2246\u001b[0m     \u001b[43m_check_call\u001b[49m\u001b[43m(\u001b[49m\n\u001b[0;32m   2247\u001b[0m \u001b[43m        \u001b[49m\u001b[43m_LIB\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mXGBoosterUpdateOneIter\u001b[49m\u001b[43m(\u001b[49m\n\u001b[0;32m   2248\u001b[0m \u001b[43m            \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mhandle\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mctypes\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mc_int\u001b[49m\u001b[43m(\u001b[49m\u001b[43miteration\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mdtrain\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mhandle\u001b[49m\n\u001b[0;32m   2249\u001b[0m \u001b[43m        \u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m   2250\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m   2251\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[0;32m   2252\u001b[0m     pred \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mpredict(dtrain, output_margin\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mTrue\u001b[39;00m, training\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mTrue\u001b[39;00m)\n", "File \u001b[1;32mD:\\PythonProject\\pythonProject\\.venv\\lib\\site-packages\\xgboost\\core.py:310\u001b[0m, in \u001b[0;36m_check_call\u001b[1;34m(ret)\u001b[0m\n\u001b[0;32m    299\u001b[0m \u001b[38;5;250m\u001b[39m\u001b[38;5;124;03m\"\"\"Check the return value of C API call\u001b[39;00m\n\u001b[0;32m    300\u001b[0m \n\u001b[0;32m    301\u001b[0m \u001b[38;5;124;03mThis function will raise exception when error occurs.\u001b[39;00m\n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m    307\u001b[0m \u001b[38;5;124;03m    return value from API calls\u001b[39;00m\n\u001b[0;32m    308\u001b[0m \u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[0;32m    309\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m ret \u001b[38;5;241m!=\u001b[39m \u001b[38;5;241m0\u001b[39m:\n\u001b[1;32m--> 310\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m XGBoostError(py_str(_LIB\u001b[38;5;241m.\u001b[39mXGBGetLastError()))\n", "\u001b[1;31mXGBoostError\u001b[0m: [23:16:39] C:\\actions-runner\\_work\\xgboost\\xgboost\\src\\common\\hist_util.cu:106: Check failed: avail > 0 (0 vs. 0) : No GPU memory is left, are you using RMM? If so, please install XGBoost with RMM support. If you are using other types of memory pool, please consider reserving a portion of the GPU memory for XGBoost."]}], "execution_count": 17}, {"metadata": {"ExecuteTime": {"end_time": "2025-04-11T09:18:14.231995Z", "start_time": "2025-04-11T08:24:40.251039Z"}}, "cell_type": "code", "source": ["import lightgbm as lgb\n", "from lightgbm import early_stopping, log_evaluation\n", "from sklearn.model_selection import train_test_split, RandomizedSearchCV, KFold\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "\n", "# 假设 all_features_processed 为合并后的特征矩阵，y_train_log 是目标变量对数转换后的数据\n", "target = 'interaction_cnt'\n", "y = y_train_log  # 对数转换后的目标变量\n", "\n", "# 划分训练集和验证集（80% 训练，20% 验证）\n", "X_train, X_val, y_train, y_val = train_test_split(\n", "    all_features_processed, y, test_size=0.2, random_state=42\n", ")\n", "\n", "print(\"训练集特征矩阵维度：\", X_train.shape)\n", "print(\"验证集特征矩阵维度：\", X_val.shape)\n", "\n", "# ---------------------------\n", "# 1. 随机搜索 + kfold 交叉验证调参\n", "# ---------------------------\n", "\n", "# 定义调参的参数空间\n", "param_dist = {\n", "    'learning_rate': [0.01, 0.05, 0.1],\n", "    'num_leaves': [31, 63, 127],\n", "    'max_depth': [-1, 7, 10, 15],\n", "    'n_estimators': [2000, 3000, 4000],\n", "    'feature_fraction': [0.7, 0.8, 0.9, 1.0],\n", "    'bagging_fraction': [0.7, 0.8, 0.9, 1.0],\n", "    'lambda_l1': [0, 0.01, 0.1, 1.0],\n", "    'lambda_l2': [0, 0.01, 0.1, 1.0]\n", "}\n", "\n", "# 初始化基础 LightGBM 模型，开启 GPU 加速\n", "base_model = lgb.LGBMRegressor(\n", "    objective='regression_l1',  # L1 损失，对应 MAE\n", "    random_state=42,\n", "    device='gpu',\n", ")\n", "\n", "# 定义 5 折交叉验证\n", "kf = KFold(n_splits=5, shuffle=True, random_state=42)\n", "\n", "# 构建 RandomizedSearchCV 对象，使用负 MAE 做评估（越高越好）\n", "random_search = RandomizedSearchCV(\n", "    estimator=base_model,\n", "    param_distributions=param_dist,\n", "    n_iter=20,  # 随机搜索20组参数组合，可根据资源和需要调整\n", "    scoring='neg_mean_absolute_error',\n", "    cv=kf,\n", "    verbose=2,\n", "    random_state=42,\n", "    n_jobs=-1\n", ")\n", "\n", "print(\"开始随机搜索调参...\")\n", "# 在随机搜索过程中，不传入 callbacks / eval_set，使用内部交叉验证\n", "random_search.fit(X_train, y_train)\n", "\n", "print(\"随机搜索完成！\")\n", "print(\"最佳参数：\", random_search.best_params_)\n", "print(\"最佳交叉验证得分 (负 MAE)：\", random_search.best_score_)\n", "\n", "# ---------------------------\n", "# 2. 用最佳参数重新训练模型（加入 early stopping）\n", "# ---------------------------\n", "# 定义 callbacks：每 100 轮打印日志，连续 30 轮验证集无提升提前停止\n", "callbacks = [log_evaluation(period=100), early_stopping(stopping_rounds=30)]\n", "\n", "# 将最佳参数更新到模型中（注意：最佳参数中已包含 n_estimators、learning_rate 等）\n", "final_params = random_search.best_params_\n", "# 加入 GPU 相关参数\n", "final_params.update({\n", "    'objective': 'regression_l1',\n", "    'random_state': 42,\n", "    'device': 'gpu',\n", "\n", "})\n", "\n", "# 初始化最终模型\n", "final_model = lgb.LGBMRegressor(**final_params)\n", "\n", "print(\"开始使用最佳参数并加入 early stopping 进行最终模型训练...\")\n", "final_model.fit(\n", "    X_train, y_train,\n", "    eval_set=[(X_val, y_val)],\n", "    eval_metric='mae',\n", "    callbacks=callbacks\n", ")\n", "print(\"最终模型训练完成！\")\n", "\n", "# ---------------------------\n", "# 3. 对验证集进行预测并还原目标变量尺度\n", "# ---------------------------\n", "y_val_pred_log = final_model.predict(X_val)\n", "y_val_pred = np.expm1(y_val_pred_log)  # 还原到原始尺度\n", "\n", "print(\"还原为原始尺度后的部分预测结果：\")\n", "print(y_val_pred[:10])\n", "\n", "# ---------------------------\n", "# 4. 绘制验证集 MAE 随迭代次数变化的曲线\n", "# ---------------------------\n", "# 对于 scikit-learn API 的 LightGBM 模型，评估结果存储在 evals_result_ 中\n", "evals_result = final_model.evals_result_\n", "# 注意：键名可能为 'validation_0'\n", "val_mae = evals_result['validation_0']['l1']\n", "epochs = range(1, len(val_mae) + 1)\n", "\n", "plt.figure(figsize=(8, 6))\n", "plt.plot(epochs, val_mae, marker='o', label='Validation MAE')\n", "plt.xlabel(\"Iteration\")\n", "plt.ylabel(\"MAE\")\n", "plt.title(\"Validation MAE over Iterations\")\n", "plt.grid(True)\n", "plt.legend()\n", "plt.show()\n"], "id": "6bc36067622e2859", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["训练集特征矩阵维度： (468220, 9526)\n", "验证集特征矩阵维度： (117056, 9526)\n", "开始随机搜索调参...\n", "Fitting 5 folds for each of 20 candidates, totalling 100 fits\n"]}, {"ename": "KeyboardInterrupt", "evalue": "", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mKeyboardInterrupt\u001b[0m                         <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[34], line 59\u001b[0m\n\u001b[0;32m     57\u001b[0m \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m开始随机搜索调参...\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[0;32m     58\u001b[0m \u001b[38;5;66;03m# 在随机搜索过程中，不传入 callbacks / eval_set，使用内部交叉验证\u001b[39;00m\n\u001b[1;32m---> 59\u001b[0m \u001b[43mrandom_search\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mfit\u001b[49m\u001b[43m(\u001b[49m\u001b[43mX_train\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43my_train\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m     61\u001b[0m \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m随机搜索完成！\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[0;32m     62\u001b[0m \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m最佳参数：\u001b[39m\u001b[38;5;124m\"\u001b[39m, random_search\u001b[38;5;241m.\u001b[39mbest_params_)\n", "File \u001b[1;32mD:\\PythonProject\\pythonProject\\.venv\\lib\\site-packages\\sklearn\\base.py:1389\u001b[0m, in \u001b[0;36m_fit_context.<locals>.decorator.<locals>.wrapper\u001b[1;34m(estimator, *args, **kwargs)\u001b[0m\n\u001b[0;32m   1382\u001b[0m     estimator\u001b[38;5;241m.\u001b[39m_validate_params()\n\u001b[0;32m   1384\u001b[0m \u001b[38;5;28;01mwith\u001b[39;00m config_context(\n\u001b[0;32m   1385\u001b[0m     skip_parameter_validation\u001b[38;5;241m=\u001b[39m(\n\u001b[0;32m   1386\u001b[0m         prefer_skip_nested_validation \u001b[38;5;129;01mor\u001b[39;00m global_skip_validation\n\u001b[0;32m   1387\u001b[0m     )\n\u001b[0;32m   1388\u001b[0m ):\n\u001b[1;32m-> 1389\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m fit_method(estimator, \u001b[38;5;241m*\u001b[39margs, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs)\n", "File \u001b[1;32mD:\\PythonProject\\pythonProject\\.venv\\lib\\site-packages\\sklearn\\model_selection\\_search.py:1024\u001b[0m, in \u001b[0;36mBaseSearchCV.fit\u001b[1;34m(self, X, y, **params)\u001b[0m\n\u001b[0;32m   1018\u001b[0m     results \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_format_results(\n\u001b[0;32m   1019\u001b[0m         all_candidate_params, n_splits, all_out, all_more_results\n\u001b[0;32m   1020\u001b[0m     )\n\u001b[0;32m   1022\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m results\n\u001b[1;32m-> 1024\u001b[0m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_run_search\u001b[49m\u001b[43m(\u001b[49m\u001b[43mevaluate_candidates\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m   1026\u001b[0m \u001b[38;5;66;03m# multimetric is determined here because in the case of a callable\u001b[39;00m\n\u001b[0;32m   1027\u001b[0m \u001b[38;5;66;03m# self.scoring the return type is only known after calling\u001b[39;00m\n\u001b[0;32m   1028\u001b[0m first_test_score \u001b[38;5;241m=\u001b[39m all_out[\u001b[38;5;241m0\u001b[39m][\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mtest_scores\u001b[39m\u001b[38;5;124m\"\u001b[39m]\n", "File \u001b[1;32mD:\\PythonProject\\pythonProject\\.venv\\lib\\site-packages\\sklearn\\model_selection\\_search.py:1951\u001b[0m, in \u001b[0;36mRandomizedSearchCV._run_search\u001b[1;34m(self, evaluate_candidates)\u001b[0m\n\u001b[0;32m   1949\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21m_run_search\u001b[39m(\u001b[38;5;28mself\u001b[39m, evaluate_candidates):\n\u001b[0;32m   1950\u001b[0m \u001b[38;5;250m    \u001b[39m\u001b[38;5;124;03m\"\"\"Search n_iter candidates from param_distributions\"\"\"\u001b[39;00m\n\u001b[1;32m-> 1951\u001b[0m     \u001b[43mevaluate_candidates\u001b[49m\u001b[43m(\u001b[49m\n\u001b[0;32m   1952\u001b[0m \u001b[43m        \u001b[49m\u001b[43mParameterSampler\u001b[49m\u001b[43m(\u001b[49m\n\u001b[0;32m   1953\u001b[0m \u001b[43m            \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mparam_distributions\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mn_iter\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mrandom_state\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mrandom_state\u001b[49m\n\u001b[0;32m   1954\u001b[0m \u001b[43m        \u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m   1955\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[1;32mD:\\PythonProject\\pythonProject\\.venv\\lib\\site-packages\\sklearn\\model_selection\\_search.py:970\u001b[0m, in \u001b[0;36mBaseSearchCV.fit.<locals>.evaluate_candidates\u001b[1;34m(candidate_params, cv, more_results)\u001b[0m\n\u001b[0;32m    962\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mverbose \u001b[38;5;241m>\u001b[39m \u001b[38;5;241m0\u001b[39m:\n\u001b[0;32m    963\u001b[0m     \u001b[38;5;28mprint\u001b[39m(\n\u001b[0;32m    964\u001b[0m         \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mFitting \u001b[39m\u001b[38;5;132;01m{0}\u001b[39;00m\u001b[38;5;124m folds for each of \u001b[39m\u001b[38;5;132;01m{1}\u001b[39;00m\u001b[38;5;124m candidates,\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m    965\u001b[0m         \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m totalling \u001b[39m\u001b[38;5;132;01m{2}\u001b[39;00m\u001b[38;5;124m fits\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;241m.\u001b[39mformat(\n\u001b[0;32m    966\u001b[0m             n_splits, n_candidates, n_candidates \u001b[38;5;241m*\u001b[39m n_splits\n\u001b[0;32m    967\u001b[0m         )\n\u001b[0;32m    968\u001b[0m     )\n\u001b[1;32m--> 970\u001b[0m out \u001b[38;5;241m=\u001b[39m \u001b[43mparallel\u001b[49m\u001b[43m(\u001b[49m\n\u001b[0;32m    971\u001b[0m \u001b[43m    \u001b[49m\u001b[43mdelayed\u001b[49m\u001b[43m(\u001b[49m\u001b[43m_fit_and_score\u001b[49m\u001b[43m)\u001b[49m\u001b[43m(\u001b[49m\n\u001b[0;32m    972\u001b[0m \u001b[43m        \u001b[49m\u001b[43mclone\u001b[49m\u001b[43m(\u001b[49m\u001b[43mbase_estimator\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    973\u001b[0m \u001b[43m        \u001b[49m\u001b[43mX\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    974\u001b[0m \u001b[43m        \u001b[49m\u001b[43my\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    975\u001b[0m \u001b[43m        \u001b[49m\u001b[43mtrain\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mtrain\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    976\u001b[0m \u001b[43m        \u001b[49m\u001b[43mtest\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mtest\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    977\u001b[0m \u001b[43m        \u001b[49m\u001b[43mparameters\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mparameters\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    978\u001b[0m \u001b[43m        \u001b[49m\u001b[43msplit_progress\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43msplit_idx\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mn_splits\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    979\u001b[0m \u001b[43m        \u001b[49m\u001b[43mcandidate_progress\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43mcand_idx\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mn_candidates\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    980\u001b[0m \u001b[43m        \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mfit_and_score_kwargs\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    981\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    982\u001b[0m \u001b[43m    \u001b[49m\u001b[38;5;28;43;01mfor\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43m(\u001b[49m\u001b[43mcand_idx\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mparameters\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m(\u001b[49m\u001b[43msplit_idx\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m(\u001b[49m\u001b[43mtrain\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mtest\u001b[49m\u001b[43m)\u001b[49m\u001b[43m)\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;129;43;01min\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mproduct\u001b[49m\u001b[43m(\u001b[49m\n\u001b[0;32m    983\u001b[0m \u001b[43m        \u001b[49m\u001b[38;5;28;43menumerate\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43mcandidate_params\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    984\u001b[0m \u001b[43m        \u001b[49m\u001b[38;5;28;43menumerate\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43mcv\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43msplit\u001b[49m\u001b[43m(\u001b[49m\u001b[43mX\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43my\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mrouted_params\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43msplitter\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43msplit\u001b[49m\u001b[43m)\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    985\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    986\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    988\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mlen\u001b[39m(out) \u001b[38;5;241m<\u001b[39m \u001b[38;5;241m1\u001b[39m:\n\u001b[0;32m    989\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mValueError\u001b[39;00m(\n\u001b[0;32m    990\u001b[0m         \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mNo fits were performed. \u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m    991\u001b[0m         \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mWas the CV iterator empty? \u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m    992\u001b[0m         \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mWere there no candidates?\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m    993\u001b[0m     )\n", "File \u001b[1;32mD:\\PythonProject\\pythonProject\\.venv\\lib\\site-packages\\sklearn\\utils\\parallel.py:77\u001b[0m, in \u001b[0;36mParallel.__call__\u001b[1;34m(self, iterable)\u001b[0m\n\u001b[0;32m     72\u001b[0m config \u001b[38;5;241m=\u001b[39m get_config()\n\u001b[0;32m     73\u001b[0m iterable_with_config \u001b[38;5;241m=\u001b[39m (\n\u001b[0;32m     74\u001b[0m     (_with_config(delayed_func, config), args, kwargs)\n\u001b[0;32m     75\u001b[0m     \u001b[38;5;28;01mfor\u001b[39;00m delayed_func, args, kwargs \u001b[38;5;129;01min\u001b[39;00m iterable\n\u001b[0;32m     76\u001b[0m )\n\u001b[1;32m---> 77\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43msuper\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[38;5;21;43m__call__\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43miterable_with_config\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[1;32mD:\\PythonProject\\pythonProject\\.venv\\lib\\site-packages\\joblib\\parallel.py:2007\u001b[0m, in \u001b[0;36mParallel.__call__\u001b[1;34m(self, iterable)\u001b[0m\n\u001b[0;32m   2001\u001b[0m \u001b[38;5;66;03m# The first item from the output is blank, but it makes the interpreter\u001b[39;00m\n\u001b[0;32m   2002\u001b[0m \u001b[38;5;66;03m# progress until it enters the Try/Except block of the generator and\u001b[39;00m\n\u001b[0;32m   2003\u001b[0m \u001b[38;5;66;03m# reaches the first `yield` statement. This starts the asynchronous\u001b[39;00m\n\u001b[0;32m   2004\u001b[0m \u001b[38;5;66;03m# dispatch of the tasks to the workers.\u001b[39;00m\n\u001b[0;32m   2005\u001b[0m \u001b[38;5;28mnext\u001b[39m(output)\n\u001b[1;32m-> 2007\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m output \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mreturn_generator \u001b[38;5;28;01<PERSON><PERSON>\u001b[39;00m \u001b[38;5;28;43mlist\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43moutput\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[1;32mD:\\PythonProject\\pythonProject\\.venv\\lib\\site-packages\\joblib\\parallel.py:1650\u001b[0m, in \u001b[0;36mParallel._get_outputs\u001b[1;34m(self, iterator, pre_dispatch)\u001b[0m\n\u001b[0;32m   1647\u001b[0m     \u001b[38;5;28;01<PERSON><PERSON>\u001b[39;00m\n\u001b[0;32m   1649\u001b[0m     \u001b[38;5;28;01mwith\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_backend\u001b[38;5;241m.\u001b[39mretrieval_context():\n\u001b[1;32m-> 1650\u001b[0m         \u001b[38;5;28;01my<PERSON> from\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_retrieve()\n\u001b[0;32m   1652\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mGeneratorExit\u001b[39;00m:\n\u001b[0;32m   1653\u001b[0m     \u001b[38;5;66;03m# The generator has been garbage collected before being fully\u001b[39;00m\n\u001b[0;32m   1654\u001b[0m     \u001b[38;5;66;03m# consumed. This aborts the remaining tasks if possible and warn\u001b[39;00m\n\u001b[0;32m   1655\u001b[0m     \u001b[38;5;66;03m# the user if necessary.\u001b[39;00m\n\u001b[0;32m   1656\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_exception \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mTrue\u001b[39;00m\n", "File \u001b[1;32mD:\\PythonProject\\pythonProject\\.venv\\lib\\site-packages\\joblib\\parallel.py:1762\u001b[0m, in \u001b[0;36mParallel._retrieve\u001b[1;34m(self)\u001b[0m\n\u001b[0;32m   1757\u001b[0m \u001b[38;5;66;03m# If the next job is not ready for retrieval yet, we just wait for\u001b[39;00m\n\u001b[0;32m   1758\u001b[0m \u001b[38;5;66;03m# async callbacks to progress.\u001b[39;00m\n\u001b[0;32m   1759\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m ((\u001b[38;5;28mlen\u001b[39m(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_jobs) \u001b[38;5;241m==\u001b[39m \u001b[38;5;241m0\u001b[39m) \u001b[38;5;129;01mor\u001b[39;00m\n\u001b[0;32m   1760\u001b[0m     (\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_jobs[\u001b[38;5;241m0\u001b[39m]\u001b[38;5;241m.\u001b[39mget_status(\n\u001b[0;32m   1761\u001b[0m         timeout\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mtimeout) \u001b[38;5;241m==\u001b[39m TASK_PENDING)):\n\u001b[1;32m-> 1762\u001b[0m     \u001b[43mtime\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43msleep\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m0.01\u001b[39;49m\u001b[43m)\u001b[49m\n\u001b[0;32m   1763\u001b[0m     \u001b[38;5;28;01mcontinue\u001b[39;00m\n\u001b[0;32m   1765\u001b[0m \u001b[38;5;66;03m# We need to be careful: the job list can be filling up as\u001b[39;00m\n\u001b[0;32m   1766\u001b[0m \u001b[38;5;66;03m# we empty it and Python list are not thread-safe by\u001b[39;00m\n\u001b[0;32m   1767\u001b[0m \u001b[38;5;66;03m# default hence the use of the lock\u001b[39;00m\n", "\u001b[1;31mKeyboardInterrupt\u001b[0m: "]}], "execution_count": 34}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": "", "id": "80344ea30a5fbe00"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": "", "id": "7cfa95575608a4bb"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": "", "id": "924bd80b2855c14e"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": "", "id": "e8b7c1a16b8a6ad2"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": "", "id": "e700027029e390c6"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": "", "id": "7eba5e0b5623b40e"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": "", "id": "7e8c4d6e6562edc7"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": "", "id": "3624040f3b5f712a"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": "", "id": "88d434c9391093cf"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": "", "id": "f36bdbde07804617"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": "", "id": "7ad5835c044b60e4"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": "", "id": "97ea017a70cbf8e6"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": "", "id": "f7baa67c215dea2d"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": "", "id": "1557bad17127fb29"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": "", "id": "bc4989bd5fc223d1"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": "", "id": "c03a1e899bef8046"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": "", "id": "6ab15c50fc5ef87"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": "", "id": "3212affbc493bdee"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": "", "id": "b8d087dc87a6908b"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": "", "id": "a810fdf161562649"}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.6"}}, "nbformat": 4, "nbformat_minor": 5}