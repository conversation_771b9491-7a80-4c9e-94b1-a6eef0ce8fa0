import os
from openai import OpenAI

client = OpenAI(
    # 若没有配置环境变量，请用百炼API Key将下行替换为：api_key="sk-xxx",
    api_key="sk-470a9e513830456aaf67cff29209e4cb", # 如何获取API Key：https://help.aliyun.com/zh/model-studio/developer-reference/get-api-key
    base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
)

completion = client.chat.completions.create(
    model="deepseek-r1",  # 此处以 deepseek-r1-distill-qwen-7b 为例，可按需更换模型名称。
    messages=[
        {'role': 'system', 'content': 'You are a helpful assistant.'},
        {'role': 'user', 'content': 'strawberry这个单词中有多少个字母r'}
        ]
)
print(completion.choices[0].message.content)
