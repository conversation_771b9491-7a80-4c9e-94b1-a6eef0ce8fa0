import os
import pandas as pd
import nltk
from nltk.tokenize import word_tokenize
from nltk.util import ngrams
from nltk.corpus import stopwords
from collections import Counter
from sentence_transformers import SentenceTransformer
import umap
import hdbscan
import numpy as np
from nltk.stem import WordNetLemmatizer  # 新增导入
import re
from multiprocessing import Pool

# 彻底禁用Gradio
os.environ["GRADIO_ANALYTICS_ENABLED"] = "False"
os.environ["GRADIO_OFFLINE"] = "True"
os.environ["GRADIO_NO_ANALYTICS"] = "True"

# 确保下载NLTK的必要资源
#nltk.download('punkt_tab')
#nltk.download('stopwords')
# nltk.download('wordnet')



# 初始化词形还原器
lemmatizer = WordNetLemmatizer()

# 读取Excel文件
df = pd.read_excel("新加坡主题识别分析.xlsx")  # 请替换为你的文件路径
# 获取Abstract列
df['abstracts'] = df['abstract'].dropna()  # 删除空值


# 初始化词形还原器
lemmatizer = WordNetLemmatizer()

# 读取Excel文件
df = pd.read_excel("新加坡主题识别分析.xlsx")  # 请替换为你的文件路径
# 获取Abstract列
abstracts = df['abstract'].dropna()  # 删除空值

# 替换表处理
replacement_df = pd.read_excel("EI推荐用词.xlsx") 
replacement_df = replacement_df.dropna().astype(str)
replacement_dict = dict(zip(replacement_df['预处理词'], replacement_df['推荐用词']))

# 关键排序逻辑 --------------------------------------------------
sorted_terms = sorted(
    replacement_dict.keys(),
    key=lambda x: (-x.count(' '), -len(x)),  # 双降序排序
    reverse=False
)

# 预编译正则表达式（按排序顺序）
patterns = [
    (re.compile(r'(?<!\S){}(?!\S)'.format(re.escape(term)), flags=re.IGNORECASE), repl)
    for term, repl in [(t, replacement_dict[t]) for t in sorted_terms]  # 按排序遍历
]

# 并行替换函数
def replace_parallel(text):
    text = str(text)
    for pattern, repl in patterns:
        text = pattern.sub(repl, text)
    return text

# 应用并行替换
with Pool(4) as p:
    df['abstract'] = p.map(replace_parallel, df['abstract'].astype(str))

# 获取英文停用词
stop_words = set(stopwords.words('english'))

# # 分词处理函数
# def tokenize(text):
#     # 分词
#     words = word_tokenize(text)
#    # 过滤非字母字符并转小写
#     words = [word.lower() for word in words if word.isalpha()]
#     # 过滤停用词
#     words = [word for word in words if word not in stop_words]
#     # 词形还原（按名词处理）
#     words = [lemmatizer.lemmatize(word, pos='n') for word in words]
#     return words

# df['Tokenized_Abstract'] = df['abstract'].dropna().apply(tokenize)

# 清理文本但不分词
def clean_text(text):
    # 保留字母、数字、空格和基本标点
    text = re.sub(r'[^a-zA-Z0-9\s.,!?]', '', str(text))
    # 转小写
    text = text.lower()
    return text

# 词形还原（保持句子完整）
def lemmatize_text(text):
    words = text.split()
    lemmatized_words = [lemmatizer.lemmatize(word, pos='n') for word in words]
    return ' '.join(lemmatized_words)

# 应用清理
df['abstract'] = df['abstract'].dropna().apply(clean_text)
# 词形还原
df['abstract'] = df['abstract'].apply(lemmatize_text)
df.head()

# 使用BERT模型进行文本嵌入表示
model = SentenceTransformer('all-MiniLM-L6-v2')  # BERT模型

import joblib
import umap.umap_ as umap
# 加载已经保存的 uMAP 模型
#umap_model = joblib.load('umap_model.joblib')

# 将预处理后的 Tokenized_Abstract 转换为完整文本（空格分隔）
# processed_texts = df['Tokenized_Abstract']

# 将所有Abstract列进行嵌入表示
embeddings = model.encode(df['abstract'], show_progress_bar=True)

# 使用uMAP进行降维
umap_model = umap.UMAP(n_components=2, random_state = 44)  # 降到2维
umap_embeddings = umap_model.fit_transform(embeddings)
umap_embeddings = umap_embeddings.astype(np.float64)

# 保存 uMAP 降维后的结果到 .npy 文件
#np.save('umap_embeddings.npy', umap_embeddings)

from hdbscan.validity import validity_index

# 使用HDBSCAN进行密度聚类
clusterer = hdbscan.HDBSCAN(
    min_samples=15,
    min_cluster_size=15,  # 最小簇大小，依据数据集调整
    # cluster_selection_method='leaf'
    prediction_data=True
    
)

clusters = clusterer.fit_predict(umap_embeddings)

# 打印DBCV分数
# DBCV分数越接近1，表示聚类的质量（簇内密度和簇间分离度）越好
dbcv_score_manual = validity_index(
    umap_embeddings, 
    clusterer.labels_, 
    metric=clusterer.metric # 确保使用与聚类器相同的距离度量
)

print(f"聚类的DBCV分数: {dbcv_score_manual:.4f}")
# 将聚类结果添加到DataFrame
df['Cluster'] = clusters

# 输出部分结果
print(df[['abstract', 'Cluster']].describe())

#import joblib

# 保存 SentenceTransformer 模型
#joblib.dump(model, 'sentence_transformer_model.pkl')

# 保存 UMAP 模型
#joblib.dump(umap_model, 'umap_model.pkl')

# 保存 N-Gram 词汇表
#joblib.dump(filtered_ngrams, 'filtered_ngrams.pkl')

#如果有使用 HDBSCAN 模型（假设你使用了聚类），也可以保存它：
#joblib.dump(hdbscan_model, 'hdbscan_model.pkl')


from bertopic import BERTopic
from sklearn.feature_extraction.text import CountVectorizer
stop_words = set(stopwords.words('english'))

vectorizer_model = CountVectorizer(
    stop_words=list(stop_words),
    ngram_range=(1,3),
    min_df=5
)
# 假设 `embeddings` 是通过 BERT 模型生成的文本嵌入
# 使用 BERTopic 进行建模
topic_model = BERTopic(hdbscan_model=clusterer, vectorizer_model=vectorizer_model, top_n_words=12)  # 将 HDBSCAN 聚类模型传递给 BERTopic 
topics, _ = topic_model.fit_transform(df['abstract'], embeddings)

def remove_redundant_ngrams(keywords):
    # Step 1: 按长度降序（优先长短语），权重降序排序
    sorted_keywords = sorted(
        keywords,
        key=lambda x: (-len(x[0].split()), x[1]),  # 长度优先，权重其次
        reverse=False
    )
    
    filtered = []
    redundant_phrases = set()
    seen_phrases = set()

    # Step 2: 优先处理长短语，标记其子短语为冗余
    for phrase, score in sorted_keywords:
        if phrase in redundant_phrases:
            continue
        # 保留当前短语
        filtered.append((phrase, score))
        seen_phrases.add(phrase)
        # 标记所有子短语为冗余（仅处理长度>1的短语）
        words = phrase.split()
        if len(words) > 1:
            for i in range(len(words)):
                for j in range(i + 1, len(words) + 1):
                    subphrase = ' '.join(words[i:j])
                    if subphrase != phrase and len(subphrase.split()) > 1:
                        redundant_phrases.add(subphrase)

    # Step 3: 按权重降序排序最终结果
    filtered_sorted = sorted(filtered, key=lambda x: x[1], reverse=True)
    return filtered_sorted


topic_info_expanded = []
for _, row in topic_info.iterrows():
    if row["Topic"] != -1:
        raw_keywords = topic_model.get_topic(row['Topic'])
        filtered_keywords = remove_redundant_ngrams(raw_keywords)[:10]
        print(f"\nTopic {row['Topic']} (共 {row['Count']} 篇文档):")
        print(" | ".join([f"{word} ({weight:.3f})" for word, weight in filtered_keywords[:10]]))
        # 将原始词和去重词合并到同一行
        topic_info_expanded.append({
            "Topic": row["Topic"],
            "Count": row["Count"],
            # "Name": "_".join([f"{word}" for word, weight in filtered_keywords[:4]]),
            "Name": f"{row['Topic']}_" + "_".join([word for word, _ in filtered_keywords[:4]]),
            "Representation": [f"{word} ({weight:.3f})" for word, weight in filtered_keywords],
            "Representative_Docs": row["Representative_Docs"]
        })

from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
import pandas as pd
import re

# 假设 topic_info_expanded 已经定义
topic_info_expanded_df = pd.DataFrame(topic_info_expanded)
# 读取叙词表（示例结构）
thesaurus_df = pd.read_excel("/root/data/EI叙词表3.7（翻译）.xlsx")

# 预处理层级编号
def preprocess_code(code):
    """将编号转换为整数元组，处理异常值"""
    try:
        # 提取所有数字部分并转换为整数
        return tuple(map(int, re.findall(r'\d+', str(code))))
    except Exception as e:
        print(f"Error processing code {code}: {str(e)}")
        return tuple()  # 返回空元组作为标记

# 创建code_tuple列（必须先执行此步骤）
thesaurus_df['code_tuple'] = thesaurus_df['编号'].apply(preprocess_code)
thesaurus_df['hierarchy_level'] = thesaurus_df['code_tuple'].apply(len)

# 读取叙词表后添加
code_to_index = {code_tuple: idx for idx, code_tuple in enumerate(thesaurus_df['code_tuple'])}

# 生成父节点代码
def get_parent_code(code_tuple):
    if len(code_tuple) > 1:
        return code_tuple[:-1]
    return None  # 根节点无父节点

# 构建代码到标题的映射字典
code_to_title = dict(zip(thesaurus_df['code_tuple'], thesaurus_df['标题']))

# 递归获取完整层级路径
def get_full_path(code_tuple):
    path = []
    current_code = code_tuple
    while current_code:
        path.append((
            ".".join(map(str, current_code)),  # 格式化代码如 "1.10.1"
            code_to_title.get(current_code, "Unknown")  # 获取标题
        ))
        current_code = get_parent_code(current_code)
    return reversed(path)  # 从根节点开始显示

# 构建语义索引
vectorizer = TfidfVectorizer()
title_vectors = vectorizer.fit_transform(thesaurus_df['标题'])

# 主题分类函数
def classify_topic(topic_keywords, top_n=3, threshold=0.3):
    # 生成主题特征
    topic_text = " ".join([word for word, _ in topic_keywords])
    topic_vec = vectorizer.transform([topic_text])
    
    # 计算相似度
    similarities = cosine_similarity(topic_vec, title_vectors).flatten()
    sorted_indices = similarities.argsort()[::-1]
    
    # 结果筛选
    results = []
    seen_codes = set()
    for idx in sorted_indices:
        if similarities[idx] < threshold:
            continue
        code = thesaurus_df.iloc[idx]['code_tuple']
        
        # 过滤父类（保留最细粒度）
        is_child = True
        for seen_code in seen_codes:
            if code[:len(seen_code)] == seen_code:
                is_child = False
                break
        if not is_child:
            continue
            
        seen_codes.add(code)
        results.append({
            "code": ".".join(map(str, code)),
            "title": thesaurus_df.iloc[idx]['标题'],
            "similarity": similarities[idx],
            "level": thesaurus_df.iloc[idx]['hierarchy_level']
        })
        if len(results) >= top_n:
            break
            
    return results

# 获取下级节点
def get_child_nodes(code_tuple, thesaurus_df):
    """获取当前节点的所有下级节点"""
    children = []
    for _, row in thesaurus_df.iterrows():
        if row['code_tuple'][:len(code_tuple)] == code_tuple and len(row['code_tuple']) > len(code_tuple):
            children.append((row['code_tuple'], row['标题']))
    return children

# 获取同级节点，返回索引列表
def get_sibling_nodes(code_tuple, thesaurus_df, code_to_index):
    """获取当前节点的所有同级节点的索引列表"""
    if len(code_tuple) == 0:
        return []  # 根节点没有同级节点
    parent_code = code_tuple[:-1]  # 父节点代码
    sibling_indices = []
    for _, row in thesaurus_df.iterrows():
        if row['code_tuple'][:-1] == parent_code and row['code_tuple'] != code_tuple:
            sibling_index = code_to_index.get(row['code_tuple'])
            if sibling_index is not None:
                sibling_indices.append(sibling_index)
    return sibling_indices

# 对每个主题进行分类
topic_classifications = []
# 修改后的主题分类循环
for _, row in topic_info_expanded_df.iterrows():
    keywords = [tuple(item.split(" (")) for item in row['Representation']]
    topic_text = " ".join([word for word, _ in keywords])
    topic_vec = vectorizer.transform([topic_text])  # 生成主题向量
    classification = classify_topic(keywords)
    
    topic_classifications.append({
        "Topic": row["Topic"],
        "Name": row["Name"],
        "Classification": classification,
        "topic_vec": topic_vec  # 保存主题向量
    })

# 修改结果展示部分
for res in topic_classifications:
    print(f"\nTopic {res['Topic']} ({res['Name']})")
    topic_vec = res['topic_vec']  # 获取当前主题的向量
    for cls in res['Classification']:
        # 解析匹配的叙词表代码
        matched_code = tuple(map(int, cls['code'].split('.')))
        
        print(f"-> 匹配节点 {cls['code']} ({cls['title']})")
        print("   完整层级路径:")
        for level_code, level_title in get_full_path(matched_code):
            print(f"   - {level_code}: {level_title}")

        # 输出下级节点
        child_nodes = get_child_nodes(matched_code, thesaurus_df)
        if child_nodes:
            print("   下级节点:")
            for child_code, child_title in child_nodes:
                print(f"   - {'.'.join(map(str, child_code))}: {child_title}")
        
        # 输出同级节点（批量计算相似度）
        sibling_indices = get_sibling_nodes(matched_code, thesaurus_df, code_to_index)
        if sibling_indices:
            print("   同级节点:")
            sibling_vectors = title_vectors[sibling_indices]
            similarities = cosine_similarity(topic_vec, sibling_vectors).flatten()
            for i, idx in enumerate(sibling_indices):
                sibling_code = thesaurus_df.iloc[idx]['code_tuple']
                sibling_title = thesaurus_df.iloc[idx]['标题']
                sibling_sim = similarities[i]
                print(f"   - {'.'.join(map(str, sibling_code))}: {sibling_title} (相似度: {sibling_sim:.2f})")
        
        print(f"   相似度: {cls['similarity']:.2f}\n")

def remove_redundant_ngrams(keywords):
    # Step 1: 按长度降序（优先长短语），权重降序排序
    sorted_keywords = sorted(
        keywords,
        key=lambda x: (-len(x[0].split()), x[1]),  # 长度优先，权重其次
        reverse=False
    )
    
    filtered = []
    redundant_phrases = set()
    seen_phrases = set()

    # Step 2: 优先处理长短语，标记其子短语为冗余
    for phrase, score in sorted_keywords:
        if phrase in redundant_phrases:
            continue
        # 保留当前短语
        filtered.append((phrase, score))
        seen_phrases.add(phrase)
        # 标记所有子短语为冗余（仅处理长度>1的短语）
        words = phrase.split()
        if len(words) > 1:
            for i in range(len(words)):
                for j in range(i + 1, len(words) + 1):
                    subphrase = ' '.join(words[i:j])
                    if subphrase != phrase and len(subphrase.split()) > 1:
                        redundant_phrases.add(subphrase)

    # Step 3: 按权重降序排序最终结果
    filtered_sorted = sorted(filtered, key=lambda x: x[1], reverse=True)
    return filtered_sorted


topic_info_expanded = []
for _, row in topic_info.iterrows():
    if row["Topic"] != -1:
        raw_keywords = topic_model.get_topic(row['Topic'])
        filtered_keywords = remove_redundant_ngrams(raw_keywords)[:10]
        print(f"\nTopic {row['Topic']} (共 {row['Count']} 篇文档):")
        print(" | ".join([f"{word} ({weight:.3f})" for word, weight in filtered_keywords[:10]]))
        # 将原始词和去重词合并到同一行
        topic_info_expanded.append({
            "Topic": row["Topic"],
            "Count": row["Count"],
            # "Name": "_".join([f"{word}" for word, weight in filtered_keywords[:4]]),
            "Name": f"{row['Topic']}_" + "_".join([word for word, _ in filtered_keywords[:4]]),
            "Representation": [f"{word} ({weight:.3f})" for word, weight in filtered_keywords],
            "Representative_Docs": row["Representative_Docs"]
        })

def remove_redundant_ngrams(keywords):
    # Step 1: 按长度降序（优先长短语），权重降序排序
    sorted_keywords = sorted(
        keywords,
        key=lambda x: (-len(x[0].split()), x[1]),  # 长度优先，权重其次
        reverse=False
    )
    
    filtered = []
    redundant_phrases = set()
    seen_phrases = set()

    # Step 2: 优先处理长短语，标记其子短语为冗余
    for phrase, score in sorted_keywords:
        if phrase in redundant_phrases:
            continue
        # 保留当前短语
        filtered.append((phrase, score))
        seen_phrases.add(phrase)
        # 标记所有子短语为冗余（仅处理长度>1的短语）
        words = phrase.split()
        if len(words) > 1:
            for i in range(len(words)):
                for j in range(i + 1, len(words) + 1):
                    subphrase = ' '.join(words[i:j])
                    if subphrase != phrase and len(subphrase.split()) > 1:
                        redundant_phrases.add(subphrase)

    # Step 3: 按权重降序排序最终结果
    filtered_sorted = sorted(filtered, key=lambda x: x[1], reverse=True)
    return filtered_sorted


topic_info_expanded = []
for _, row in topic_info.iterrows():
    if row["Topic"] != -1:
        raw_keywords = topic_model.get_topic(row['Topic'])
        filtered_keywords = remove_redundant_ngrams(raw_keywords)[:10]
        print(f"\nTopic {row['Topic']} (共 {row['Count']} 篇文档):")
        print(" | ".join([f"{word} ({weight:.3f})" for word, weight in filtered_keywords[:10]]))
        # 将原始词和去重词合并到同一行
        topic_info_expanded.append({
            "Topic": row["Topic"],
            "Count": row["Count"],
            # "Name": "_".join([f"{word}" for word, weight in filtered_keywords[:4]]),
            "Name": f"{row['Topic']}_" + "_".join([word for word, _ in filtered_keywords[:4]]),
            "Representation": [f"{word} ({weight:.3f})" for word, weight in filtered_keywords],
            "Representative_Docs": row["Representative_Docs"]
        })

def remove_redundant_ngrams(keywords):
    # Step 1: 按长度降序（优先长短语），权重降序排序
    sorted_keywords = sorted(
        keywords,
        key=lambda x: (-len(x[0].split()), x[1]),  # 长度优先，权重其次
        reverse=False
    )
    
    filtered = []
    redundant_phrases = set()
    seen_phrases = set()

    # Step 2: 优先处理长短语，标记其子短语为冗余
    for phrase, score in sorted_keywords:
        if phrase in redundant_phrases:
            continue
        # 保留当前短语
        filtered.append((phrase, score))
        seen_phrases.add(phrase)
        # 标记所有子短语为冗余（仅处理长度>1的短语）
        words = phrase.split()
        if len(words) > 1:
            for i in range(len(words)):
                for j in range(i + 1, len(words) + 1):
                    subphrase = ' '.join(words[i:j])
                    if subphrase != phrase and len(subphrase.split()) > 1:
                        redundant_phrases.add(subphrase)

    # Step 3: 按权重降序排序最终结果
    filtered_sorted = sorted(filtered, key=lambda x: x[1], reverse=True)
    return filtered_sorted


topic_info_expanded = []
for _, row in topic_info.iterrows():
    if row["Topic"] != -1:
        raw_keywords = topic_model.get_topic(row['Topic'])
        filtered_keywords = remove_redundant_ngrams(raw_keywords)[:10]
        print(f"\nTopic {row['Topic']} (共 {row['Count']} 篇文档):")
        print(" | ".join([f"{word} ({weight:.3f})" for word, weight in filtered_keywords[:10]]))
        # 将原始词和去重词合并到同一行
        topic_info_expanded.append({
            "Topic": row["Topic"],
            "Count": row["Count"],
            # "Name": "_".join([f"{word}" for word, weight in filtered_keywords[:4]]),
            "Name": f"{row['Topic']}_" + "_".join([word for word, _ in filtered_keywords[:4]]),
            "Representation": [f"{word} ({weight:.3f})" for word, weight in filtered_keywords],
            "Representative_Docs": row["Representative_Docs"]
        })

def remove_redundant_ngrams(keywords):
    # Step 1: 按长度降序（优先长短语），权重降序排序
    sorted_keywords = sorted(
        keywords,
        key=lambda x: (-len(x[0].split()), x[1]),  # 长度优先，权重其次
        reverse=False
    )
    
    filtered = []
    redundant_phrases = set()
    seen_phrases = set()

    # Step 2: 优先处理长短语，标记其子短语为冗余
    for phrase, score in sorted_keywords:
        if phrase in redundant_phrases:
            continue
        # 保留当前短语
        filtered.append((phrase, score))
        seen_phrases.add(phrase)
        # 标记所有子短语为冗余（仅处理长度>1的短语）
        words = phrase.split()
        if len(words) > 1:
            for i in range(len(words)):
                for j in range(i + 1, len(words) + 1):
                    subphrase = ' '.join(words[i:j])
                    if subphrase != phrase and len(subphrase.split()) > 1:
                        redundant_phrases.add(subphrase)

    # Step 3: 按权重降序排序最终结果
    filtered_sorted = sorted(filtered, key=lambda x: x[1], reverse=True)
    return filtered_sorted


topic_info_expanded = []
for _, row in topic_info.iterrows():
    if row["Topic"] != -1:
        raw_keywords = topic_model.get_topic(row['Topic'])
        filtered_keywords = remove_redundant_ngrams(raw_keywords)[:10]
        print(f"\nTopic {row['Topic']} (共 {row['Count']} 篇文档):")
        print(" | ".join([f"{word} ({weight:.3f})" for word, weight in filtered_keywords[:10]]))
        # 将原始词和去重词合并到同一行
        topic_info_expanded.append({
            "Topic": row["Topic"],
            "Count": row["Count"],
            # "Name": "_".join([f"{word}" for word, weight in filtered_keywords[:4]]),
            "Name": f"{row['Topic']}_" + "_".join([word for word, _ in filtered_keywords[:4]]),
            "Representation": [f"{word} ({weight:.3f})" for word, weight in filtered_keywords],
            "Representative_Docs": row["Representative_Docs"]
        })

topic_classifications

import os

# 创建目录（如果不存在）
os.makedirs("./result", exist_ok=True)
topics_fig.write_html("./result/topics_visualization.html")
pd.DataFrame(topic_info_expanded).to_excel("./result/topic_info新加坡.xlsx", index=False)

topic_info.to_excel("./result/topic_info.xlsx") 

# 将聚类结果添加到 DataFrame
df['Cluster'] = clusters

# 输出部分结果
print(df[['abstract', 'Cluster']].head())

# 过滤掉 Cluster == 0 的数据点
filtered_df = df[df['Cluster'] != -1]  # 只保留 Cluster 不为 0 的行
filtered_embeddings = umap_embeddings[filtered_df.index]  # 取对应的降维数据

# 可视化聚类结果
import matplotlib.pyplot as plt

plt.figure(figsize=(10, 8))
plt.scatter(
    filtered_embeddings[:, 0],
    filtered_embeddings[:, 1],
    c=filtered_df['Cluster'],
    cmap='Spectral',
    s=10,  # 调整点的大小
    alpha=0.7  # 设置透明度，减少重叠
)
plt.colorbar(label="Cluster")
plt.title("Cluster Visualization (Excluding Cluster -1)", fontsize=14)
plt.xlabel("UMAP Dimension 1", fontsize=12)
plt.ylabel("UMAP Dimension 2", fontsize=12)
plt.grid(alpha=0.3)
plt.show()


from sklearn.feature_extraction.text import TfidfVectorizer
# 统计生成的主题数量
n_clusters = len(set(clusters)) - (1 if -1 in clusters else 0)
print(f"生成的主题数量: {n_clusters}")

# 为每个簇提取关键词及其权重
def extract_cluster_keywords_with_weights(texts, cluster_labels, cluster_id):
    # 筛选属于当前簇的文本
    cluster_texts = [texts[i] for i in range(len(cluster_labels)) if cluster_labels[i] == cluster_id]
    if not cluster_texts:
        return []

    # 使用c-TF-IDF提取关键词
    vectorizer = TfidfVectorizer(stop_words='english', max_features=20)  # 提取每个簇的前20个关键词
    tfidf_matrix = vectorizer.fit_transform(cluster_texts)
    keywords = vectorizer.get_feature_names_out()
    weights = np.asarray(tfidf_matrix.mean(axis=0)).flatten()  # 计算每个关键词的平均权重
    keyword_weights = sorted(zip(keywords, weights), key=lambda x: x[1], reverse=True)  # 按权重降序排序
    return keyword_weights

# 提取每个主题的关键词及权重
cluster_keywords_with_weights = {}
for cluster_id in set(clusters):
    if cluster_id == -1:
        continue  # 跳过孤立点
    cluster_keywords_with_weights[cluster_id] = extract_cluster_keywords_with_weights(df['abstract'].tolist(), clusters, cluster_id)

# 打印每个主题的关键词及其权重
for cluster_id, keyword_weights in cluster_keywords_with_weights.items():
    print(f"主题 {cluster_id}:")
    for keyword, weight in keyword_weights:
        print(f"  {keyword}: {weight:.4f}")
    print()

# 将每个簇的关键词与权重保存到DataFrame
keyword_data = []
for cluster_id, keyword_weights in cluster_keywords_with_weights.items():
    for keyword, weight in keyword_weights:
        keyword_data.append({'Cluster': cluster_id, 'Keyword': keyword, 'Weight': weight})

keyword_df = pd.DataFrame(keyword_data)
#keyword_df.to_excel("cluster_keywords_with_weights-25-75-75.xlsx", index=False)





