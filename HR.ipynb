{"cells": [{"cell_type": "code", "id": "initial_id", "metadata": {"collapsed": true, "ExecuteTime": {"end_time": "2025-04-14T08:12:24.003638Z", "start_time": "2025-04-14T08:12:12.415954Z"}}, "source": ["import requests\n", "\n", "url = \"https://www.home-for-researchers.com/protect/pub/model_title/recommend_title\"\n", "\n", "headers = {\n", "    \"Content-Type\": \"application/x-www-form-urlencoded\",\n", "    \"Cookie\": \"Hm_lvt_2309b3a60fa801f73807c5ef901439c8=1744599534; tk=T01hQFY8Pz1OJ2FFVkplP0tNZT9CSiplSF9gak9NYUBXPEs%2BRkxhRVZgSm9JYEdLWSUmK0dJR0NLPFlvSEwmaFY8RzlISU9DSzxCbElgR0tDSi5KTyhdRUYoWWVOX0M4Vzw%2FZUhJT2FaSmUlTEtdZEonPm5aPG0%2FVmM2KEhMJmhWJWFmVyhdQ1ZNWWVOJyUqWCZlS0NMXV5LJktlSSdhQldMbW5HPCpoSCdlaVZKbV9WSm05SGJpVkw6P2xMSUNHVkouaUNJO15HKFlvSEwmVlgmYU1OJ2FFVkplJkxhXWBPYkdOSWJtX1ZKYW9ITCZIWDtlaUxhXVZLJ21qTkpdR1ZKLmZGTUtDRihZZU85P0xYO2RuVjk3P1ZiLW1JOkNAVjxHPkpiZV1GJWAnT0lCbFglYWpGYm1fV0k7bE5vQ0RYPD5uQ01lP1ZjNyZPO1kpWCdPSVknYV1HJk9kTGBLaFZiPm5DS09fV01ZZUknYT9WSm05SGJdYEg7P2ZOSkM%2FQ2JLKkhfP11MOj9JTGFYbVY8O01DSktPTDxpZUknZThESm05SF87X0JhTypLPCY8WTtlSUhJTz9WYEspSUpHZ1o6JkpJSUdEVmAlbElLR0daYCpmSE1dQ0s6YWVJOkNAQkthSUhJT15XYU9vTmBDKFZMQm5HY0NWVkpgJ0hMZTtaOiY6VihdQ0s6JSVJYmVnQjomZktJN0RaYUptSSVHR1ZMT0lZJ2FdV2E%2FbEhMZWtWSiZKR01ZRUs6SylJTWVjQmAuSkpNYURLOzpvSWFHS0M6KjlIX2BqRE1cKkpLR0JWY04mV01DKUNKKmtYYDs9Q0xZSVo7P2NCSzopSiY7aFpLR2NKO11BTEthPVc8QyZLRjIy; Hm_lvt_6bf284161ce1018df419c455085f7b47=1744599829; __tins__20784145=%7B%22sid%22%3A%201744611905467%2C%20%22vd%22%3A%201%2C%20%22expires%22%3A%201744613705467%7D; __51cke__=; __51laig__=1; Hm_lpvt_2309b3a60fa801f73807c5ef901439c8=1744611906; Hm_lvt_dafd87710e0b19c7c03126ce299519e9=1744613635; Hm_lvt_5189fb7063175cba5439f40023106b4a=1744616286; Hm_lpvt_5189fb7063175cba5439f40023106b4a=1744616286; SESSION=Y2IyYTMxODEtNWY4Zi00ZmU5LWFiNWYtZDc5MTVjYjZmZWVl; Hm_lvt_ca6287fe9b33ae381d09a4a313b06810=1744616670; Hm_lpvt_ca6287fe9b33ae381d09a4a313b06810=1744616670; Hm_lpvt_6bf284161ce1018df419c455085f7b47=1744616734; Hm_lpvt_dafd87710e0b19c7c03126ce299519e9=1744618064\",\n", "}\n", "\n", "data = {\n", "    \"text\": \"The AIDS Committee of the Actuarial Society of South Africa has developed a demographic model to allow researchers to project the impact of HIV and AIDS in South Africa...\",\n", "    \"mode\": \"light\"\n", "}\n", "\n", "response = requests.post(url, headers=headers, data=data)\n", "\n", "print(\"服务器返回内容：\")\n", "print(response.text)\n"], "outputs": [{"name": "stdout", "output_type": "stream", "text": ["服务器返回内容：\n", "\"QlJkUk9pSnVORU4uX09pSUUtU2tKVGh1PkJsSlNkR1JALmRRT09HRkZUcFNOdD50S2p0V1F1V0JBLWhMUz5DMkhFbXdPZ1dwP2lOQVN0cUNBU2BIYk9TRUNFSnZTalZ0Q2hOQU9QT0VIa2RfYk5pUT9pYG1XdzZuPkVkMV4tMjNIUmRPUnRLVVFAcFNPMD9DS0NTRWF3S29ELkJjT09xbz9TYD9XUU9TSWtkLl9PT0RAUmBJUU5xQlRnLTo=\"\n"]}], "execution_count": 1}, {"metadata": {"ExecuteTime": {"end_time": "2025-04-14T08:18:49.131952Z", "start_time": "2025-04-14T08:18:49.114542Z"}}, "cell_type": "code", "source": ["import base64\n", "\n", "def decode_base64url(data):\n", "    # 修正 Base64url 和补齐\n", "    data = data.replace('-', '+').replace('_', '/')\n", "    padding = 4 - (len(data) % 4)\n", "    if padding:\n", "        data += '=' * padding\n", "    decoded_bytes = base64.b64decode(data)\n", "    return decoded_bytes.decode('utf-8')\n", "\n", "# 示例\n", "encoded_text = response.text  # 你实际返回的加密串\n", "decoded_text = decode_base64url(encoded_text)\n", "\n", "print(decoded_text)\n"], "id": "b7b414f0af604754", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["BRdROiJuNEN._OiIE-SkJThu>B<PERSON><PERSON><PERSON>d<PERSON>@.dQOOGFFTpSNt>tKjtWQuWBA-hLS>C2HEmwOgWp?iNAStqCAS`HbOSECEJvSjVtChNAOPOEHkd_bNiQ?i`mWw6n>Ed1^-23HRdORtKUQ@pSO0?CKCSEawKoD.BcOOqo?S`?WQOSIkd._OOD@R`IQNqBTg-:\n"]}], "execution_count": 2}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.6"}}, "nbformat": 4, "nbformat_minor": 5}