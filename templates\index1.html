{% extends "base.html" %}

{% block content %}
<style>
/* 通用变量 */
:root {
    --primary: #2F54EB;
    --primary-light: #E6EEFF;
    --success: #52C41A;
    --error: #F5222D;
    --bg: #FAFBFC;
    --text: #2C3E50;
    --border: #EBEEF5;
    --shadow: 0 4px 24px rgba(0,0,0,0.06);
}

/* 重置和基础样式 */
body {
    font-family: 'Inter', system-ui, sans-serif;
    background: var(--bg);
    color: var(--text);
    margin: 0;
    padding: 20px;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    display: grid;
    grid-template-columns: 300px 1fr;
    gap: 25px;
}

/* 侧边栏 */
.sidebar {
    background: white;
    border-radius: 16px;
    padding: 25px;
    box-shadow: var(--shadow);
}

.section-title {
    font-size: 15px;
    font-weight: 600;
    color: var(--primary);
    margin: 25px 0 15px;
    padding-bottom: 12px;
    border-bottom: 2px solid var(--border);
}

/* 文件上传 */
.upload-card {
    border: 2px dashed var(--border);
    border-radius: 12px;
    padding: 25px;
    text-align: center;
    transition: all 0.3s ease;
    margin-bottom: 25px;
}

.upload-card:hover {
    border-color: var(--primary);
    background: #f8f9ff;
}

.file-input {
    display: none;
}

.upload-label {
    cursor: pointer;
    display: block;
}

.upload-icon {
    font-size: 40px;
    margin-bottom: 15px;
    color: var(--primary);
}

.upload-text {
    font-size: 15px;
    color: var(--text);
    margin-bottom: 8px;
}

.file-types {
    font-size: 13px;
    color: #666;
}

/* 通用按钮样式 */
.btn {
    width: 100%;
    padding: 12px;
    background: var(--primary);
    color: white;
    border: none;
    border-radius: 10px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn:hover {
    background: #1A3ABF;
    transform: scale(1.02);
}

.btn:active {
    transform: scale(0.98);
}

/* 聊天容器 */
.chat-container {
    background: white;
    border-radius: 16px;
    box-shadow: var(--shadow);
    display: flex;
    flex-direction: column;
    height: 90vh;
    max-height: 100vh;
}

/* 消息区域 */
.messages {
    flex: 1;
    padding: 30px;
    overflow-y: auto;
    background: linear-gradient(180deg, #fbfcff 0%, #ffffff 100%);
    scroll-behavior: smooth;
}

.message {
    margin-bottom: 15px;
    display: flex;
    animation: fadeIn 0.3s ease-in;
}

.user-message {
    justify-content: flex-end;
}

.bot-message {
    justify-content: flex-start;
}

.bubble {
    max-width: 75%;
    padding: 20px;
    border-radius: 20px;
    line-height: 1.6;
    font-size: 15px;
    position: relative;
    box-shadow: 0 4px 12px rgba(0,0,0,0.05);
}

.user-bubble {
    background: var(--primary-light);
    color: var(--text);
    border-radius: 20px 20px 5px 20px;
}

.bot-bubble {
    background: white;
    border: 1px solid var(--border);
    border-radius: 20px 20px 20px 5px;
}

.timestamp {
    font-size: 12px;
    color: #888;
    margin-top: 5px;
}

/* 输入区域 */
.input-wrapper {
    padding: 20px;
    display: flex;
    gap: 15px;
    align-items: center;
}

select[name="pipeline_id"] {
    flex: 1;
    padding: 12px 20px;
    border: 1px solid var(--border);
    border-radius: 8px;
    font-size: 15px;
    transition: all 0.3s ease;
}

textarea[name="question"] {
    flex: 2;
    padding: 12px 20px;
    border: 1px solid var(--border);
    border-radius: 8px;
    font-size: 15px;
    min-height: 56px;
    resize: vertical;
    transition: all 0.3s ease;
}

select[name="pipeline_id"]:focus,
textarea[name="question"]:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 5px var(--primary);
}

button[type="submit"] {
    padding: 12px 24px;
    width: auto;
}

/* Toast 通知 */
.toast {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 10px 20px;
    background: var(--error);
    color: white;
    border-radius: 8px;
    animation: fadeIn 0.3s ease-in;
}

/* 动画 */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        grid-template-columns: 1fr;
    }
    .chat-container {
        height: auto;
        max-height: none;
    }
    .input-wrapper {
        flex-direction: column;
    }
    select[name="pipeline_id"],
    textarea[name="question"] {
        width: 100%;
    }
}
</style>

<div class="container">
    <!-- 侧边栏 -->
    <div class="sidebar">
        <div class="section-title">文件管理</div>

        <!-- 文件上传 -->
        <div class="upload-card">
            <form action="/upload_file" method="POST" enctype="multipart/form-data">
                <input type="file" name="file" id="file-input" class="file-input" required>
                <label for="file-input" class="upload-label">
                    <div class="upload-icon">📁</div>
                    <div class="upload-text">点击选择或拖拽文件</div>
                    <div class="file-types">支持格式：PDF/DOCX/TXT</div>
                </label>
                <button type="submit" class="btn">开始上传</button>
            </form>
        </div>

        <!-- 已上传文件 -->
        <div class="section-title">已上传文件</div>
        <div class="file-list">
            {% if uploaded_files %}
                {% for file in uploaded_files %}
                <div class="file-item">
                    <div class="file-name">{{ file.filename }}</div>
                    <div class="file-meta">{{ file.uploaded_at.strftime('%Y-%m-%d %H:%M') }}</div>
                </div>
                {% endfor %}
            {% else %}
                <div class="empty-state">暂无上传文件</div>
            {% endif %}
        </div>

        <!-- 创建知识库 -->
        <div class="section-title">创建知识库</div>
        <form action="/create_kb_with_files" method="POST">
            <input type="text" name="name" placeholder="知识库名称" required>
            <div class="file-selector">
                {% if uploaded_files %}
                    {% for file in uploaded_files %}
                    <label class="file-checkbox">
                        <input type="checkbox" name="file_ids" value="{{ file.file_id }}">
                        {{ file.filename }}
                    </label>
                    {% endfor %}
                {% else %}
                    <div class="empty-state">请先上传文件</div>
                {% endif %}
            </div>
            <button type="submit" class="btn">创建知识库</button>
        </form>
    </div>

    <!-- 主聊天区 -->
    <div class="chat-container">
        <div class="messages">
            <!-- 消息内容将显示在这里 -->
        </div>

        <div class="input-area">
            <form id="chat-form" method="POST" action="/ask" class="input-wrapper">
                <select name="pipeline_id" required>
                    <option value="" disabled selected>请选择知识库</option>
                    {% for kb in knowledge_bases %}
                    <option value="{{ kb.pipeline_id }}">{{ kb.name }}</option>
                    {% endfor %}
                </select>
                <textarea
                    id="question-input"
                    name="question"
                    placeholder="输入您的问题..."
                    required
                ></textarea>
                <button type="submit" class="btn" id="submit-btn">
                    <span class="btn-text">发送</span>
                </button>
            </form>
        </div>
    </div>
</div>

<script>
    // 消息容器自动滚动
    const messagesContainer = document.querySelector('.messages');
    function scrollToBottom() {
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
    }

    // 动态调整输入框高度
    const questionInput = document.getElementById('question-input');
    questionInput.addEventListener('input', () => {
        questionInput.style.height = 'auto';
        questionInput.style.height = `${questionInput.scrollHeight}px`;
    });

    // 处理表单提交
    document.getElementById('chat-form').addEventListener('submit', async (e) => {
        e.preventDefault();

        const formData = new FormData(e.target);
        const btn = document.getElementById('submit-btn');

        try {
            // 显示加载状态
            btn.disabled = true;
            messagesContainer.insertAdjacentHTML('beforeend', '<div class="typing">正在输入...</div>');
            scrollToBottom();

            // 发送请求
            const response = await fetch('/ask', {
                method: 'POST',
                body: formData
            });

            const data = await response.json();

            // 移除加载指示器
            document.querySelector('.typing')?.remove();

            if (data.error) {
                showToast(data.error);
            } else {
                // 用户消息
                const userMsg = document.createElement('div');
                userMsg.className = 'message user-message';
                userMsg.innerHTML = `
                    <div class="bubble user-bubble">
                        ${formData.get('question')}
                        <small class="timestamp">${new Date().toLocaleTimeString()}</small>
                    </div>
                `;

                // 机器人消息
                const botMsg = createMessageElement(data);

                // 添加到消息容器
                messagesContainer.appendChild(userMsg);
                messagesContainer.appendChild(botMsg);
                scrollToBottom();

                // 清空输入框
                questionInput.value = '';
                questionInput.style.height = 'auto'; // 重置高度
            }
        } catch (error) {
            showToast('请求失败，请检查网络连接');
        } finally {
            btn.disabled = false;
        }
    });

    // Toast 通知
    function showToast(message) {
        const toast = document.createElement('div');
        toast.className = 'toast';
        toast.textContent = message;
        document.body.appendChild(toast);
        setTimeout(() => toast.remove(), 3000);
    }

    // 创建机器人消息元素
    function createMessageElement(response) {
        const messageDiv = document.createElement('div');
        messageDiv.className = 'message bot-message';
        let answerHtml = response.answer.replace(/\n/g, '<br>');
        answerHtml = answerHtml.replace(/<ref>\$\$(\d+)\$\$<\/ref>/g, (match, p1) => {
            return `<sup class="reference-marker">[${p1}]</sup>`;
        });

        messageDiv.innerHTML = `
            <div class="bubble bot-bubble">
                <div class="answer-content">${answerHtml}</div>
                <small class="timestamp">${new Date().toLocaleTimeString()}</small>
            </div>
        `;
        return messageDiv;
    }
</script>
{% endblock %}