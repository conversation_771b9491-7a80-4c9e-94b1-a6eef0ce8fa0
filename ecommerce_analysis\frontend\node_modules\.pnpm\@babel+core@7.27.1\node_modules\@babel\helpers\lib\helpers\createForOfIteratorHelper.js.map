{"version": 3, "names": ["_unsupportedIterableToArray", "require", "_createForOfIteratorHelper", "o", "allowArrayLike", "it", "Symbol", "iterator", "Array", "isArray", "unsupportedIterableToArray", "length", "i", "F", "s", "n", "done", "value", "e", "f", "TypeError", "normalCompletion", "didErr", "err", "call", "step", "next", "return"], "sources": ["../../src/helpers/createForOfIteratorHelper.ts"], "sourcesContent": ["/* @minVersion 7.9.0 */\n\nimport unsupportedIterableToArray from \"./unsupportedIterableToArray.ts\";\n\nexport type IteratorFunction<T> = () => Iterator<T>;\n\nexport interface ForOfIteratorHelper<T> {\n  // s: start (create the iterator)\n  s: () => void;\n  // n: next\n  n: () => IteratorResult<T, undefined>;\n  // e: error (called whenever something throws)\n  e: (e: Error) => void;\n  // f: finish (always called at the end)\n  f: () => void;\n}\n\nexport default function _createForOfIteratorHelper<T>(\n  o: T[] | Iterable<T> | ArrayLike<T>,\n  allowArrayLike: boolean,\n): ForOfIteratorHelper<T> {\n  var it: IteratorFunction<T> | Iterator<T> | T[] | undefined =\n    (typeof Symbol !== \"undefined\" && (o as Iterable<T>)[Symbol.iterator]) ||\n    (o as any)[\"@@iterator\"];\n\n  if (!it) {\n    // Fallback for engines without symbol support\n    if (\n      Array.isArray(o) ||\n      // union type doesn't work with function overload, have to use \"as any\".\n      (it = unsupportedIterableToArray(o as any) as T[] | undefined) ||\n      (allowArrayLike && o && typeof (o as ArrayLike<T>).length === \"number\")\n    ) {\n      if (it) o = it;\n      var i = 0;\n      var F = function () {};\n      return {\n        s: F,\n        n: function () {\n          // After \"Array.isArray\" check, unsupportedIterableToArray to array, and allow arraylike\n          // o is sure to be an array or arraylike, but TypeScript doesn't know that\n          if (i >= (o as T[] | ArrayLike<T>).length) {\n            // explicit missing the \"value\" (undefined) to reduce the bundle size\n            return { done: true } as IteratorReturnResult<undefined>;\n          }\n          return { done: false, value: (o as T[] | ArrayLike<T>)[i++] };\n        },\n        e: function (e: Error) {\n          throw e;\n        },\n        f: F,\n      };\n    }\n\n    throw new TypeError(\n      \"Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\",\n    );\n  }\n\n  var normalCompletion = true,\n    didErr = false,\n    err: Error | undefined;\n\n  // \"it\" is being reassigned multiple times to reduce the variables (bundle size)\n  // thus TypeScript can't infer the correct type of the \"it\"\n  return {\n    s: function () {\n      it = (it as IteratorFunction<T>).call(o);\n    },\n    n: function () {\n      var step = (it as Iterator<T>).next();\n      normalCompletion = step.done!;\n      return step;\n    },\n    e: function (e: Error) {\n      didErr = true;\n      err = e;\n    },\n    f: function () {\n      try {\n        if (!normalCompletion && (it as Iterator<T>).return != null) {\n          (it as Iterator<T>).return!();\n        }\n      } finally {\n        // eslint-disable-next-line no-unsafe-finally\n        if (didErr) throw err!;\n      }\n    },\n  };\n}\n"], "mappings": ";;;;;;AAEA,IAAAA,2BAAA,GAAAC,OAAA;AAee,SAASC,0BAA0BA,CAChDC,CAAmC,EACnCC,cAAuB,EACC;EACxB,IAAIC,EAAuD,GACxD,OAAOC,MAAM,KAAK,WAAW,IAAKH,CAAC,CAAiBG,MAAM,CAACC,QAAQ,CAAC,IACpEJ,CAAC,CAAS,YAAY,CAAC;EAE1B,IAAI,CAACE,EAAE,EAAE;IAEP,IACEG,KAAK,CAACC,OAAO,CAACN,CAAC,CAAC,KAEfE,EAAE,GAAG,IAAAK,mCAA0B,EAACP,CAAQ,CAAoB,CAAC,IAC7DC,cAAc,IAAID,CAAC,IAAI,OAAQA,CAAC,CAAkBQ,MAAM,KAAK,QAAS,EACvE;MACA,IAAIN,EAAE,EAAEF,CAAC,GAAGE,EAAE;MACd,IAAIO,CAAC,GAAG,CAAC;MACT,IAAIC,CAAC,GAAG,SAAAA,CAAA,EAAY,CAAC,CAAC;MACtB,OAAO;QACLC,CAAC,EAAED,CAAC;QACJE,CAAC,EAAE,SAAAA,CAAA,EAAY;UAGb,IAAIH,CAAC,IAAKT,CAAC,CAAwBQ,MAAM,EAAE;YAEzC,OAAO;cAAEK,IAAI,EAAE;YAAK,CAAC;UACvB;UACA,OAAO;YAAEA,IAAI,EAAE,KAAK;YAAEC,KAAK,EAAGd,CAAC,CAAwBS,CAAC,EAAE;UAAE,CAAC;QAC/D,CAAC;QACDM,CAAC,EAAE,SAAAA,CAAUA,CAAQ,EAAE;UACrB,MAAMA,CAAC;QACT,CAAC;QACDC,CAAC,EAAEN;MACL,CAAC;IACH;IAEA,MAAM,IAAIO,SAAS,CACjB,uIACF,CAAC;EACH;EAEA,IAAIC,gBAAgB,GAAG,IAAI;IACzBC,MAAM,GAAG,KAAK;IACdC,GAAsB;EAIxB,OAAO;IACLT,CAAC,EAAE,SAAAA,CAAA,EAAY;MACbT,EAAE,GAAIA,EAAE,CAAyBmB,IAAI,CAACrB,CAAC,CAAC;IAC1C,CAAC;IACDY,CAAC,EAAE,SAAAA,CAAA,EAAY;MACb,IAAIU,IAAI,GAAIpB,EAAE,CAAiBqB,IAAI,CAAC,CAAC;MACrCL,gBAAgB,GAAGI,IAAI,CAACT,IAAK;MAC7B,OAAOS,IAAI;IACb,CAAC;IACDP,CAAC,EAAE,SAAAA,CAAUA,CAAQ,EAAE;MACrBI,MAAM,GAAG,IAAI;MACbC,GAAG,GAAGL,CAAC;IACT,CAAC;IACDC,CAAC,EAAE,SAAAA,CAAA,EAAY;MACb,IAAI;QACF,IAAI,CAACE,gBAAgB,IAAKhB,EAAE,CAAiBsB,MAAM,IAAI,IAAI,EAAE;UAC1DtB,EAAE,CAAiBsB,MAAM,CAAE,CAAC;QAC/B;MACF,CAAC,SAAS;QAER,IAAIL,MAAM,EAAE,MAAMC,GAAG;MACvB;IACF;EACF,CAAC;AACH", "ignoreList": []}