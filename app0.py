import time

from flask import Flask, render_template, request, redirect, url_for, jsonify
from flask_sqlalchemy import SQLAlchemy
from flask_login import LoginManager, UserMixin, login_user, login_required, logout_user, current_user
from dotenv import load_dotenv

import hashlib
from alibabacloud_bailian20231229.client import Client as BailianClient
from alibabacloud_bailian20231229 import models as bailian_20231229_models
from alibabacloud_tea_openapi import models as open_api_models
from alibabacloud_tea_util import models as util_models
from alibabacloud_tea_util.client import Client as UtilClient
from Tea.exceptions import TeaException, UnretryableException
import requests
import os
from http import HTTPStatus
from dashscope import Application
# 加载环境变量
load_dotenv()

# 初始化 Flask 应用
app = Flask(__name__)
app.config['SECRET_KEY'] = os.getenv('SECRET_KEY')
app.config['SQLALCHEMY_DATABASE_URI'] = 'mysql+pymysql://root:123456@localhost/rag_demo'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# 初始化数据库和登录管理器
db = SQLAlchemy(app)
login_manager = LoginManager(app)
login_manager.login_view = 'login'

# 数据库模型
class User(UserMixin, db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    password = db.Column(db.String(255), nullable=False)  # 修改字段名并存储明文密码

class KnowledgeBase(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    name = db.Column(db.String(120), nullable=False)
    pipeline_id = db.Column(db.String(255), nullable=False)
    created_at = db.Column(db.DateTime, server_default=db.func.now())

# 在数据库模型部分添加
class File(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    filename = db.Column(db.String(255), nullable=False)
    file_id = db.Column(db.String(255), nullable=False)  # lease_id
    uploaded_at = db.Column(db.DateTime, server_default=db.func.now())
# 用户加载函数
@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

# 初始化百炼客户端
def create_bailian_client():
    config = open_api_models.Config(
        access_key_id=os.getenv('ALIBABA_CLOUD_ACCESS_KEY_ID'),
        access_key_secret=os.getenv('ALIBABA_CLOUD_ACCESS_KEY_SECRET')
    )
    config.endpoint = 'bailian.cn-beijing.aliyuncs.com'
    return BailianClient(config)

bailian_client = create_bailian_client()

# 路由
@app.route('/')
@login_required
def index():
    knowledge_bases = KnowledgeBase.query.filter_by(user_id=current_user.id).all()
    uploaded_files = File.query.filter_by(user_id=current_user.id).all()  # 新增
    return render_template('index1.html',
                         knowledge_bases=knowledge_bases,
                         uploaded_files=uploaded_files)  # 新增
@app.route('/register', methods=['GET', 'POST'])
def register():
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        if User.query.filter_by(username=username).first():
            return '用户名已存在'
        # 直接存储明文密码
        new_user = User(username=username, password=password)
        db.session.add(new_user)
        db.session.commit()
        return redirect(url_for('login'))
    return render_template('register.html')

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        user = User.query.filter_by(username=username).first()
        if user and user.password == password:  # 明文密码比较
            login_user(user)
            return redirect(url_for('index'))
        return '无效的用户名或密码'
    return render_template('login.html')

@app.route('/logout')
@login_required
def logout():
    logout_user()
    return redirect(url_for('login'))


@app.route('/upload_file', methods=['POST'])
@login_required
def upload_file():
    uploaded_file = request.files.get('file')
    if not uploaded_file:
        return jsonify({"error": "未上传文件"}), 400

    # 一次性读取文件内容
    file_content = uploaded_file.read()

    # 计算文件 MD5 值
    file_md5 = hashlib.md5(file_content).hexdigest()

    # 获取文件大小（字节）
    file_size = len(file_content)

    # 重置文件指针，确保文件指针从头开始
    uploaded_file.seek(0)

    # 构造请求参数
    apply_file_upload_lease_request = bailian_20231229_models.ApplyFileUploadLeaseRequest(

        file_name=uploaded_file.filename,
        md_5=file_md5,
        size_in_bytes=str(file_size)
    )

    # 获取 workspace_id 从环境变量
    workspace_id = os.getenv('WORKSPACE_ID')
    if not workspace_id:
        return jsonify({"error": "缺少必要的环境变量"}), 500

    # 创建 runtime 参数
    runtime = util_models.RuntimeOptions()
    headers = {}

    try:
        # 调用 ApplyFileUploadLease 接口获取租约
        response = bailian_client.apply_file_upload_lease_with_options(
            'default', workspace_id, apply_file_upload_lease_request, headers, runtime
        )

        if response.status_code != 200:
            return jsonify({
                "error": "获取租约失败",
                "status_code": response.status_code,
                "response": response.body.message
            }), 500

        # 解析响应

        lease_data = response.body.data
        # 获取 FileUploadLeaseId
        print(lease_data)
        lease_id = lease_data.file_upload_lease_id
        upload_url = lease_data.param.url
        headers = lease_data.param.headers

        # 上传文件到阿里云临时存储
        upload_response = requests.put(upload_url, data=file_content, headers=headers)

        if upload_response.status_code != 200:
            return jsonify({"error": "文件上传失败", "response": upload_response.text}), 500

        # 调用 AddFile 接口将文件添加到数据管理
        add_file_request = bailian_20231229_models.AddFileRequest(
            category_id="default",
            parser="DASHSCOPE_DOCMIND",
            lease_id=lease_id,
            #workspace_id=workspace_id  # 确保 workspace_id 在这里传递
        )

        # 创建 runtime 参数
        runtime = util_models.RuntimeOptions()  # 创建 runtime 参数

        # 确保 headers 被传递，如果有需要，可以在此添加更多 headers
        headers = {}

        # 调用 AddFile 接口
        add_file_response = bailian_client.add_file_with_options(
            workspace_id,#workspace_id,  # 传递 workspace_id
            add_file_request,
            headers,
            runtime  # 确保传递了 runtime 参数
        )

        if add_file_response.status_code != 200:
            # API 调用失败，返回错误信息
            return jsonify({"error": "文件添加失败", "message": add_file_response.body.message}), 500
        if add_file_response.status_code == 200:
            # 打印返回值
            try:
                file_id = add_file_response.body.data.file_id
                # 在成功调用AddFile接口后保存文件记录
                new_file = File(
                    user_id=current_user.id,
                    filename=uploaded_file.filename,
                    file_id=file_id  # 使用lease_id作为file_id
                )
                db.session.add(new_file)
                db.session.commit()

                return redirect(url_for('index'))

            except TeaException as e:
                return jsonify({
                    "error": "API 调用失败",
                    "message": e.message
                }), 500
            except UnretryableException as e:
                return jsonify({
                    "error": "网络异常",
                    "message": str(e)
                }), 500
            except Exception as e:
                return jsonify({
                    "error": "其他异常",
                    "message": str(e)
                }), 500
            # 返回 JSON 格式的成功响应


    except TeaException as e:
        return jsonify({
            "error": "API 调用失败",
            "message": e.message
        }), 500
    except UnretryableException as e:
        return jsonify({
            "error": "网络异常",
            "message": str(e)
        }), 500
    except Exception as e:
        return jsonify({
            "error": "其他异常",
            "message": str(e)
        }), 500

@app.route('/create_kb_with_files', methods=['POST'])
@login_required
def create_kb_with_files():
    kb_name = request.form.get('name')
    file_ids = request.form.getlist('file_ids')

    # 验证至少选择一个文件
    if not file_ids:
        return jsonify({"error": "请至少选择一个文件"}), 400

    # 验证所有文件属于当前用户
    user_files = File.query.filter(
        File.file_id.in_(file_ids),
        File.user_id == current_user.id
    ).all()

    if len(user_files) != len(file_ids):
        return jsonify({"error": "包含无效文件ID"}), 400

    workspace_id = os.getenv('WORKSPACE_ID')
    if not workspace_id:
        return jsonify({"error": "缺少WORKSPACE_ID环境变量"}), 500

    # 构造创建知识库请求
    create_index_request = bailian_20231229_models.CreateIndexRequest(

        name=kb_name,
        structure_type="unstructured",
        source_type="DATA_CENTER_FILE",
        document_ids=file_ids,
        rerank_model_name="gte-rerank-hybrid",
        sink_type="BUILT_IN"
    )

    try:
        # 调用创建知识库API
        create_index_response = bailian_client.create_index_with_options(
            workspace_id,
            create_index_request,
            headers={},
            runtime=util_models.RuntimeOptions()
        )

        print("Create Index Response:", create_index_response.body.to_map())

        if create_index_response.status_code != 200:
            return jsonify({
                "error": "知识库创建失败",
                "message": create_index_response.body.message
            }), 500

        if not create_index_response.body or not create_index_response.body.data:
            return jsonify({"error": "API返回数据无效"}), 500

        index_id = create_index_response.body.data.id

        # 提交索引任务
        submit_job_request = bailian_20231229_models.SubmitIndexJobRequest(
            index_id=index_id
        )
        submit_job_response = bailian_client.submit_index_job_with_options(
            workspace_id,  # 添加workspace_id作为路径参数
            submit_job_request,
            headers={},
            runtime=util_models.RuntimeOptions()
        )

        if submit_job_response.status_code != 200:
            return jsonify({
                "error": "提交任务失败",
                "message": submit_job_response.body.message
            }), 500

        # 保存到数据库
        new_kb = KnowledgeBase(
            user_id=current_user.id,
            name=kb_name,
            pipeline_id=index_id
        )
        db.session.add(new_kb)
        db.session.commit()

        return jsonify({"message": "知识库创建成功", "kb_id": new_kb.id}), 200

    except TeaException as e:
        return jsonify({"error": "API调用失败", "message": e.message}), 500
    except Exception as e:
        return jsonify({"error": "其他异常", "message": str(e)}), 500


from dashscope import Application
from http import HTTPStatus

@app.route('/ask', methods=['POST'])
@login_required
def handle_ask():
    # 获取参数
    pipeline_id = request.form.get('pipeline_id')  # 前端需对应修改name属性
    question = request.form.get('question')

    # 参数校验
    if not pipeline_id or not question:
        return jsonify({"error": "缺少必要参数"}), 400

    try:
        # 调用DashScope应用
        response = Application.call(
            api_key=os.getenv("DASHSCOPE_API_KEY"),
            app_id=os.getenv("APP_ID"),
            prompt=question,
            rag_options={
                "pipeline_ids": [pipeline_id]  # 关键参数：传入知识库ID
            },
            session_id=str(current_user.id),  # 使用用户ID保持会话
            stream=False  # 关闭流式输出
        )
        print(response.output.text)
        # 处理响应
        if response.status_code == HTTPStatus.OK:
            return jsonify({
                "answer": response.output.text,
                "references": getattr(response.output, 'doc_references', [])
            })

        else:
            return jsonify({
                "error": f"[{response.code}] {response.message}",
                "request_id": response.request_id
            }), 500

    except Exception as e:
        return jsonify({"error": str(e)}), 500


if __name__ == '__main__':
    with app.app_context():
        db.create_all()
    app.run(debug=True)