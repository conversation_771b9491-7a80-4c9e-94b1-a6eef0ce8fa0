文档上传方式选择
在选择文档上传方式时，请考虑以下因素：

通过 file-id 上传

推荐：适合需要频繁引用和管理的文档。可以有效减少文本输入错误，操作简便。

文件格式仅限于纯文本文件类型，包括 TXT、DOCX、PDF、XLSX、EPUB、MOBI、MD等，目前不支持图片或扫描文档等本质是图片形式的内容。每个文件的大小限制为 150MB，单个阿里云账号最多可上传 1 万个文件，总文件大小不得超过 100GB。当任一条件超出限制时，需删除部分文件以满足要求后再重新尝试上传，详情请参见OpenAI文件接口兼容。
通过纯文本上传

适用场景：适合小规模文档或临时内容。如果文档较短且不需要长期存储，可以选择此方式。受限于API调用请求体大小，如果您的文本内容长度超过100万Token，请通过file-id传入。

通过 JSON 字符串上传

适用场景：适合需要传递复杂数据结构的情况。如果您的文档包含多层次信息，使用 JSON 字符串可以确保数据的完整性。

请根据您的具体需求和文档特性选择最合适的上传方式。我们建议优先考虑 file-id 上传，以获得最佳体验。

通过file-id传入文档信息
您可以通过OpenAI兼容接口上传文档，并将返回的file-id输入到System Message中，使得模型在回复时参考文档信息。

简单示例
Qwen-Long模型可以基于您上传的文档进行回复。此处以百炼系列手机产品介绍.docx作为示例文件。

将文件通过OpenAI兼容接口上传到百炼平台，保存至平台安全存储空间后获取file-id。有关文档上传接口的详细参数解释及调用方式，请参考API文档页面进行了解。

Pythoncurl
 
import os
from pathlib import Path
from openai import OpenAI

client = OpenAI(
    api_key=os.getenv("DASHSCOPE_API_KEY"),  # 如果您没有配置环境变量，请在此处替换您的API-KEY
    base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",  # 填写DashScope服务base_url
)

file_object = client.files.create(file=Path("百炼系列手机产品介绍.docx"), purpose="file-extract")
print(file_object.id)
运行以上代码，您可以得到本次上传文件对应的file-id。

file-id目前仅能用于Qwen-Long模型以及Batch接口。
将file-id传入System Message中且数量不超过 100 个，并在User Message中输入问题。

在通过 system message 提供文档信息时，建议同时设置一个正常role-play的system message，如默认的 “You are a helpful assistant.”，角色设定会对文档的处理效果产生影响，因此建议在消息中明确设定自己的角色。
Pythoncurl
 
import os
from openai import OpenAI

client = OpenAI(
    api_key=os.getenv("DASHSCOPE_API_KEY"),  # 如果您没有配置环境变量，请在此处替换您的API-KEY
    base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",  # 填写DashScope服务base_url
)
# 初始化messages列表
completion = client.chat.completions.create(
    model="qwen-long",
    messages=[
        {'role': 'system', 'content': 'You are a helpful assistant.'},
        # 请将 'file-fe-xxx'替换为您实际对话场景所使用的 file-id。
        {'role': 'system', 'content': 'fileid://file-fe-xxx'},
        {'role': 'user', 'content': '这篇文章讲了什么？'}
    ],
    stream=True,
    stream_options={"include_usage": True}
)

full_content = ""
for chunk in completion:
    if chunk.choices and chunk.choices[0].delta.content:
        # 拼接输出内容
        full_content += chunk.choices[0].delta.content
        print(chunk.model_dump())

print({full_content})
通过配置stream及stream_options参数，Qwen-Long模型会流式输出回复，并在最后返回的对象中通过usage字段展示Token使用情况。

本文中的所有代码示例均采用流式输出，以清晰和直观地展示模型输出过程。如果您希望查看非流式输出的案例，请参见非流式输出案例。
Pythoncurl
 
{'id':'chatcmpl-565151e8-7b41-9a78-ae88-472edbad8c47','choices':[{'delta':{'content':'','function_call':null,'role':'assistant','tool_calls':null},'finish_reason':null,'index':0,'logprobs':null}],'created':1726023099,'model':'qwen-long','object':'chat.completion.chunk','service_tier':null,'system_fingerprint':null,'usage':null}
{'id':'chatcmpl-565151e8-7b41-9a78-ae88-472edbad8c47','choices':[{'delta':{'content':'这篇文章','function_call':null,'role':null,'tool_calls':null},'finish_reason':null,'index':0,'logprobs':null}],'created':1726023099,'model':'qwen-long','object':'chat.completion.chunk','service_tier':null,'system_fingerprint':null,'usage':null}
{'id':'chatcmpl-565151e8-7b41-9a78-ae88-472edbad8c47','choices':[{'delta':{'content':'介绍了','function_call':null,'role':null,'tool_calls':null},'finish_reason':null,'index':0,'logprobs':null}],'created':1726023099,'model':'qwen-long','object':'chat.completion.chunk','service_tier':null,'system_fingerprint':null,'usage':null}
{'id':'chatcmpl-565151e8-7b41-9a78-ae88-472edbad8c47','choices':[{'delta':{'content':'百','function_call':null,'role':null,'tool_calls':null},'finish_reason':null,'index':0,'logprobs':null}],'created':1726023099,'model':'qwen-long','object':'chat.completion.chunk','service_tier':null,'system_fingerprint':null,'usage':null}
......
{'id':'chatcmpl-565151e8-7b41-9a78-ae88-472edbad8c47','choices':[{'delta':{'content':'满足不同的使用需求','function_call':null,'role':null,'tool_calls':null},'finish_reason':null,'index':0,'logprobs':null}],'created':1726023099,'model':'qwen-long','object':'chat.completion.chunk','service_tier':null,'system_fingerprint':null,'usage':null}
{'id':'chatcmpl-565151e8-7b41-9a78-ae88-472edbad8c47','choices':[{'delta':{'content':'。','function_call':null,'role':null,'tool_calls':null},'finish_reason':null,'index':0,'logprobs':null}],'created':1726023099,'model':'qwen-long','object':'chat.completion.chunk','service_tier':null,'system_fingerprint':null,'usage':null}
{'id':'chatcmpl-565151e8-7b41-9a78-ae88-472edbad8c47','choices':[{'delta':{'content':'','function_call':null,'role':null,'tool_calls':null},'finish_reason':'stop','index':0,'logprobs':null}],'created':1726023099,'model':'qwen-long','object':'chat.completion.chunk','service_tier':null,'system_fingerprint':null,'usage':null}
{'id':'chatcmpl-565151e8-7b41-9a78-ae88-472edbad8c47','choices':[],'created':1726023099,'model':'qwen-long','object':'chat.completion.chunk','service_tier':null,'system_fingerprint':null,'usage':{'completion_tokens':93,'prompt_tokens':5395,'total_tokens':5488}}
{'这篇文章是关于百炼系列手机的产品介绍，详细描述了六款不同型号的手机特点和卖点：.....每款手机都有其独特的特点和目标用户群体，旨在满足不同消费者的需求。'}
除了传入单个file-id外，您还可以通过传入多个file-id来向模型传入多个文档，或在对话过程中追加file-id使模型能够参考新的文档信息。

传入多文档
您可以在一条System Message中传入多个file-id，在一次请求中处理多个文档。使用方式请参考示例代码。

示例代码

Pythoncurl
 
import os
from openai import OpenAI

client = OpenAI(
    api_key=os.getenv("DASHSCOPE_API_KEY"),  # 如果您没有配置环境变量，请在此处替换您的API-KEY
    base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",  # 填写DashScope服务base_url
)
# 初始化messages列表
completion = client.chat.completions.create(
    model="qwen-long",
    messages=[
        {'role': 'system', 'content': 'You are a helpful assistant.'},
        # 请将 'file-fe-xxx1' 和 'file-fe-xxx2' 替换为您实际对话场景所使用的 file-id。
        {'role': 'system', 'content': f"fileid://file-fe-xxx1,fileid://file-fe-xxx2"},
        {'role': 'user', 'content': '这几篇文章讲了什么？'}
    ],
    stream=True,
    stream_options={"include_usage": True}
)

full_content = ""
for chunk in completion:
    if chunk.choices and chunk.choices[0].delta.content:
        # 拼接输出内容
        full_content += chunk.choices[0].delta.content
        print(chunk.model_dump())

print({full_content})
追加文档
在您与模型的交互过程中，可能需要补充新的文档信息。您可以在Messages 数组中添加新的file-id到System Message中来实现这一效果。

Pythoncurl
 
import os
from openai import OpenAI

client = OpenAI(
    api_key=os.getenv("DASHSCOPE_API_KEY"),  # 如果您没有配置环境变量，请在此处替换您的API-KEY
    base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",  # 填写DashScope服务base_url
)
# 初始化messages列表
messages = [
    {'role': 'system', 'content': 'You are a helpful assistant.'},
    # 请将 'file-fe-xxx1' 替换为您实际对话场景所使用的 file-id。
    {'role': 'system', 'content': f'fileid://file-fe-xxx1'},
    {'role': 'user', 'content': '这篇文章讲了什么？'}
]

# 第一轮响应
completion_1 = client.chat.completions.create(
    model="qwen-long",
    messages=messages,
    stream=False
)

# 打印出第一轮响应
# 如果需要流式输出第一轮的响应，需要将stream设置为True，并拼接每一段输出内容，在构造assistant_message的content时传入拼接后的字符
print(f"第一轮响应：{completion_1.choices[0].message.model_dump()}")

# 构造assistant_message
assistant_message = {
    "role": "assistant",
    "content": completion_1.choices[0].message.content}

# 将assistant_message添加到messages中
messages.append(assistant_message)

# 将追加文档的fileid添加到messages中
# 请将 'file-fe-xxx2' 替换为您实际对话场景所使用的 file-id。
system_message = {'role': 'system', 'content': f'fileid://file-fe-xxx2'}
messages.append(system_message)

# 添加用户问题
messages.append({'role': 'user', 'content': '这两篇文章讨论的方法有什么异同点？'})

# 追加文档后的响应
completion_2 = client.chat.completions.create(
    model="qwen-long",
    messages=messages,
    stream=True,
    stream_options={
        "include_usage": True
    }
)

# 流式打印出追加文档后的响应
print("追加文档后的响应：")
for chunk in completion_2:
    print(chunk.model_dump())
通过纯文本传入信息
除了通过 file-id 传入文档信息外，您还可以直接使用字符串传入文档内容。在此方法下，为避免模型混淆角色设定与文档内容，请确保在 messages 的第一条消息中添加用于角色设定的信息。

受限于API调用请求体大小，如果您的文本内容长度超过100万Token，请参考通过file-id传入文档信息，通过file-id传入。
简单示例传入多文档追加文档
您可以直接将文档内容输入System Message中。

Pythoncurl
 
import os
from openai import OpenAI

client = OpenAI(
    api_key=os.getenv("DASHSCOPE_API_KEY"),  # 如果您没有配置环境变量，请在此处替换您的API-KEY
    base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",  # 填写DashScope服务base_url
)
# 初始化messages列表
completion = client.chat.completions.create(
    model="qwen-long",
    messages=[
        {'role': 'system', 'content': 'You are a helpful assistant.'},
        {'role': 'system', 'content': '百炼手机产品介绍 百炼X1 ——————畅享极致视界：搭载6.7英寸1440 x 3200像素超清屏幕...'},
        {'role': 'user', 'content': '文章讲了什么？'}
    ],
    stream=True,
    stream_options={"include_usage": True}
)

full_content = ""
for chunk in completion:
    if chunk.choices and chunk.choices[0].delta.content:
        # 拼接输出内容
        full_content += chunk.choices[0].delta.content
        print(chunk.model_dump())

print({full_content})
通过JSON字符串传入文档信息
您可以通过JSON字符串传入文档的内容、类型、名称与标题，使模型在本轮对话中可以参考这些信息。

JSON格式的文档信息需要按照文档内容（content）、文档类型（file_type）、文档名称（filename）、文档标题（title）的格式进行组织。请先将结构化的文档信息转换为JSON 字符串，再输入System Message中。
简单示例传入多文档追加文档
Pythoncurl
 
import os
import json
from openai import OpenAI

client = OpenAI(
    api_key=os.getenv("DASHSCOPE_API_KEY"),  # 如果您没有配置环境变量，请在此处替换您的API-KEY
    base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",  # 填写DashScope服务base_url
)

file_info = {
    # 全文内容省略，只做格式示意
    'content': '百炼X1 —— 畅享极致视界：搭载6.7英寸1440 x 3200像素超清屏幕，搭配120Hz刷新率...',
    'file_type': 'docx',
    'filename': '百炼系列手机产品介绍',
    'title': '百炼手机产品介绍'
}
# 初始化messages列表
completion = client.chat.completions.create(
    model="qwen-long",
    messages=[
        {'role': 'system', 'content': 'You are a helpful assistant.'},
        # 通过json.dumps方法将JSON object转化为字符串
        {'role': 'system', 'content': json.dumps(file_info, ensure_ascii=False)},
        {'role': 'user', 'content': '文章讲了什么？'}
    ],
    stream=True
)
full_content = ""
for chunk in completion:
    if chunk.choices and chunk.choices[0].delta.content:
        # 拼接输出内容
        full_content += chunk.choices[0].delta.content
        print(chunk.model_dump())

print({full_content})
限制
文件上传限制：文件格式支持常见的文本文件（TXT、DOCX、PDF、XLSX、EPUB、MOBI、MD等），单文件大小限制为150M，总量限制为1万个文件，总文件大小限制为100G。更多文件上传相关内容请参见上传文件。

输入限制：

请避免直接将文档内容放在role为user的message中，用于role-play的system和user的message限制输入最长为9,000 Token（连续多条user message视为一条user message且长度限制在9,000 Token内）。

通过 file-id 传入文档信息，并在 system 消息中使用返回的 file-id 时，content 的最大输入限制可扩展至 10,000,000 Token，但 file-id 的数量限制为不超过 100 个。此外，文件的传入顺序将直接影响模型返回结果的顺序。

输出限制：最大输出为 6,000 Token。

免费额度：100万Token的免费额度仅在百炼开通后的180天内有效。使用超出免费额度的部分将按照相应的输入输出成本收费。

调用限制：关于模型的限流条件，请参见限流。

常见问题
Dashscope SDK的调用方式是否兼容？

是的，Dashscope SDK对模型调用仍然兼容，但文件上传与file-id获取目前只支持通过OpenAI SDK进行调用，且通过此方式获得的file-id与Dashscope对模型进行调用所需要的file-id通用。

Qwen-Long模型是否支持通System Message来指定模型行为？

是的，Qwen-Long支持通过System Message标准化指定模型行为的功能,详情请参照上方“快速开始”部分。

Qwen-Long模型是否支持解析处理jpg、jpeg、png、webp等图片类型文件？

目前Qwen-Long不支持对图片或扫描文档等本质是图片形式的内容进行解析处理，可以使用Qwen-VL视觉理解模型解析包含此类内容的文件。

如何在JSON格式中组织文档信息？

请参照上方“通过JSON字符串传入文档信息”部分，构造messages时，为避免格式问题，JSON格式的文档信息应按照文档内容（content）、文档类型（file_type）、文档名称（filename）、文档标题（title）的格式组织。

Qwen-Long模型支持以流式回复吗？

通过设置 stream 和 stream_options 中的 include_usage参数为True，Qwen-Long模型会以流式的形式进行回复，并在最后返回的对象中通过usage字段展示token使用情况。

Qwen-Long模型是否支持批量提交任务？

是的，Qwen-Long兼容 OpenAI Batch 接口，支持以文件方式批量提交任务。任务会以异步形式执行，并在 24 小时内返回结果。使用批量接口的费用为实时调用的 50%。

Qwen-Long模型的费用消耗是如何计算的？

Qwen-Long模型的总费用根据输入和输出的 Token 数量分别计费：

总费用 = 输入 Token 数 × 输入单价 + 输出 Token 数 × 输出单价

通过 OpenAI 文件兼容接口上传的文档，解析后的文本长度将计入输入 Token 数。因此，上传文档的大小会直接影响费用。无论采用何种输入方式（例如通过 API 或 SDK），Qwen-Long模型都严格按照实际使用的输入和输出 Token 数量进行计费。更多详细信息请参见计费项说明。

不同的API Key之间能否共享file-id进行调用？

file-id 只能在生成它的阿里云账号内的API Key之间共享使用，不支持跨账号调用。

通过OpenAI文件兼容接口上传文件后，文件将被保存在何处？

所有通过OpenAI文件兼容接口上传的文件均将被保存在当前阿里云账号下的百炼存储空间，关于所上传文件的信息查询与管理请参考OpenAI文件接口。

OpenAI文件兼容接口上传并保存的文件不会产生任何额外费用。