{"cells": [{"cell_type": "code", "id": "initial_id", "metadata": {"collapsed": true, "ExecuteTime": {"end_time": "2025-03-03T05:51:12.194883Z", "start_time": "2025-03-03T05:51:12.165095Z"}}, "source": ["import csv\n", "import json\n", "def messages_builder_example(content):\n", "    messages = [{\"role\": \"system\", \"content\": \"You are a conference keyword extractor. Based on the conference name, accurately extract 1-3 core keywords, strictly following the format 'keyword1/keyword2/keyword3,' ensuring conciseness and no redundant information. The results should be output in English.\"}, {\"role\": \"user\", \"content\": content}]\n", "    return messages\n", "\n", "with open(\"input_demo.csv\", \"r\") as fin:\n", "    with open(\"input_demo_deepseek-r1.jsonl\", 'w', encoding='utf-8') as fout:\n", "        csvreader = csv.reader(fin)\n", "        for row in csvreader:\n", "            body = {\"model\": \"deepseek-r1\", \"messages\": messages_builder_example(row[1])}\n", "            request = {\"custom_id\": row[0], \"method\": \"POST\", \"url\": \"/v1/chat/completions\", \"body\": body}\n", "            fout.write(json.dumps(request, separators=(',', ':'), ensure_ascii=False) + \"\\n\", )"], "outputs": [], "execution_count": 30}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": "", "id": "1ec85cc5f06d90a2"}, {"metadata": {"ExecuteTime": {"end_time": "2025-03-03T07:13:34.746840Z", "start_time": "2025-03-03T07:13:34.715139Z"}}, "cell_type": "code", "source": ["import json\n", "import csv\n", "columns = [\"custom_id\",\n", "           \"model\",\n", "           \"request_id\",\n", "           \"status_code\",\n", "           \"error_code\",\n", "           \"error_message\",\n", "           \"created\",\n", "           \"content\",\n", "           \"usage\"]\n", "\n", "def dict_get_string(dict_obj, path):\n", "    obj = dict_obj\n", "    try:\n", "        for element in path:\n", "            obj = obj[element]\n", "        return obj\n", "    except:\n", "        return None\n", "\n", "with open(\"qwen_batch.jsonl\", \"r\",encoding='utf-8') as fin:\n", "    with open(\"result.csv\", 'w', encoding='utf-8') as fout:\n", "        rows = [columns]\n", "        for line in fin:\n", "            request_result = json.loads(line)\n", "            row = [dict_get_string(request_result, [\"custom_id\"]),\n", "                   dict_get_string(request_result, [\"response\", \"body\", \"model\"]),\n", "                   dict_get_string(request_result, [\"response\", \"request_id\"]),\n", "                   dict_get_string(request_result, [\"response\", \"status_code\"]),\n", "                   dict_get_string(request_result, [\"error\", \"error_code\"]),\n", "                   dict_get_string(request_result, [\"error\", \"error_message\"]),\n", "                   dict_get_string(request_result, [\"response\", \"body\", \"created\"]),\n", "                   dict_get_string(request_result, [\"response\", \"body\", \"choices\", 0, \"message\", \"content\"]),\n", "                   dict_get_string(request_result, [\"response\", \"body\", \"usage\"])]\n", "            rows.append(row)\n", "        writer = csv.writer(fout)\n", "        writer.writerows(rows)\n", "        "], "id": "5c91a3f7c25f5098", "outputs": [], "execution_count": 31}, {"metadata": {"ExecuteTime": {"end_time": "2025-03-04T07:20:33.510805Z", "start_time": "2025-03-04T07:18:02.835124Z"}}, "cell_type": "code", "source": ["import os\n", "from openai import OpenAI\n", "\n", "client = OpenAI(\n", "    api_key = \"c21e4f5d-40a8-4826-bd7c-6ba739e3444d\",\n", "    base_url = \"https://ark.cn-beijing.volces.com/api/v3\",\n", ")\n", "\n", "# Non-streaming:\n", "print(\"----- standard request -----\")\n", "completion = client.chat.completions.create(\n", "    model = \"deepseek-r1-250120\",  # your model endpoint ID\n", "    messages = [\n", "        {\"role\": \"system\", \"content\": \"你是人工智能助手\"},\n", "        {\"role\": \"user\", \"content\": \"详细介绍一下transformer架构是什么\"},\n", "    ],\n", ")\n", "print(completion.choices[0].message.content)\n", "\n", "# Streaming:\n", "print(\"----- streaming request -----\")\n", "stream = client.chat.completions.create(\n", "    model = \"deepseek-r1-250120\",  # your model endpoint ID\n", "    messages = [\n", "        {\"role\": \"system\", \"content\": \"你是人工智能助手\"},\n", "        {\"role\": \"user\", \"content\": \"详细介绍一下transformer架构是什么\"},\n", "    ],\n", "    stream=True\n", ")\n", "\n", "for chunk in stream:\n", "    if not chunk.choices:\n", "        continue\n", "    print(chunk.choices[0].delta.content, end=\"\")\n", "print()"], "id": "cbe50621dd3d9031", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["----- standard request -----\n", "\n", "\n", "Transformer架构是一种基于自注意力机制（Self-Attention）的深度学习模型架构，最初由Vaswani等人在2017年的论文《Attention Is All You Need》中提出。它彻底改变了自然语言处理（NLP）领域，并成为BERT、GPT等现代预训练模型的基础。以下是Transformer的核心组成部分和工作原理的详细介绍：\n", "\n", "---\n", "\n", "### **1. 核心思想**\n", "Transformer摒弃了传统的循环神经网络（RNN）和卷积神经网络（CNN），完全依赖**自注意力机制**来捕捉序列中不同位置之间的依赖关系。其核心优势在于：\n", "- **并行计算**：可同时处理整个序列，而非逐步处理。\n", "- **长距离依赖建模**：自注意力机制能直接关联序列中任意两个位置的词。\n", "- **灵活性**：适用于多种任务（如翻译、文本生成、分类等）。\n", "\n", "---\n", "\n", "### **2. 整体架构**\n", "Transformer由**编码器（Encoder）**和**解码器（Decoder）**组成，每部分包含多个相同的层（通常为6层）。以下是整体结构：\n", "\n", "#### **编码器（Encoder）**\n", "1. **输入嵌入（Input Embedding）**：将输入词转换为向量。\n", "2. **位置编码（Positional Encoding）**：为词向量添加位置信息。\n", "3. **自注意力层（Self-Attention Layer）**：计算词之间的关系权重。\n", "4. **前馈网络（Feed-Forward Network）**：对每个位置进行非线性变换。\n", "5. **残差连接（Residual Connection）和层归一化（Layer Normalization）**：加速训练并稳定模型。\n", "\n", "#### **解码器（Decoder）**\n", "解码器结构与编码器类似，但增加以下部分：\n", "1. **掩码自注意力层**：防止解码时看到未来信息（通过掩码遮盖后续位置）。\n", "2. **交叉注意力层（Cross-Attention）**：连接编码器和解码器，关注编码器的输出。\n", "\n", "---\n", "\n", "### **3. 核心组件详解**\n", "\n", "#### **(1) 自注意力机制（Self-Attention）**\n", "- **目的**：计算序列中每个词对其他词的依赖权重。\n", "- **步骤**：\n", "  1. **生成Q、K、V向量**：通过线性变换将输入转换为查询（Query）、键（Key）、值（Value）向量。\n", "  2. **计算注意力分数**：通过点积计算Query和Key的相似度，缩放后通过Softmax归一化。\n", "  3. **加权求和**：用注意力权重对Value向量加权求和，得到输出。\n", "- **公式**：\n", "  \\[\n", "  \\text{Attention}(Q, K, V) = \\text{Softmax}\\left(\\frac{QK^T}{\\sqrt{d_k}}\\right)V\n", "  \\]\n", "  其中\\(d_k\\)是Key向量的维度。\n", "\n", "#### **(2) 多头注意力（Multi-Head Attention）**\n", "- **动机**：通过多个注意力头捕捉不同类型的关系（如语法、语义等）。\n", "- **实现**：将Q、K、V分割为多个子空间，分别计算注意力后拼接结果。\n", "- **公式**：\n", "  \\[\n", "  \\text{MultiHead}(Q, K, V) = \\text{Concat}(\\text{head}_1, ..., \\text{head}_h)W^O\n", "  \\]\n", "  每个\\(\\text{head}_i\\)独立计算自注意力。\n", "\n", "#### **(3) 位置编码（Positional Encoding）**\n", "- **问题**：自注意力机制本身无法感知词的位置顺序。\n", "- **解决方案**：通过正弦和余弦函数生成位置编码，添加到词嵌入中：\n", "  \\[\n", "  PE_{(pos, 2i)} = \\sin(pos / 10000^{2i/d_{\\text{model}}})\n", "  \\]\n", "  \\[\n", "  PE_{(pos, 2i+1)} = \\cos(pos / 10000^{2i/d_{\\text{model}}})\n", "  \\]\n", "  其中\\(pos\\)是位置，\\(i\\)是维度索引。\n", "\n", "#### **(4) 前馈网络（Feed-Forward Network）**\n", "- **结构**：两个线性变换夹一个激活函数（如ReLU）：\n", "  \\[\n", "  \\text{FFN}(x) = \\max(0, xW_1 + b_1)W_2 + b_2\n", "  \\]\n", "\n", "#### **(5) 残差连接与层归一化**\n", "- **残差连接**：将输入直接加到输出上（\\(x + \\text{Sublayer}(x)\\)），缓解梯度消失。\n", "- **层归一化**：对每层的输出进行归一化，加速训练。\n", "\n", "---\n", "\n", "### **4. Transformer的优势**\n", "1. **并行计算**：显著提升训练速度。\n", "2. **全局上下文建模**：自注意力直接关联所有位置。\n", "3. **灵活性**：可处理可变长度输入，适配多种任务。\n", "4. **可扩展性**：通过堆叠更多层提升模型容量。\n", "\n", "---\n", "\n", "### **5. 应用场景**\n", "- **NLP任务**：机器翻译（如Google翻译）、文本生成（GPT系列）、文本理解（BERT）。\n", "- **跨模态任务**：视觉Transformer（ViT）、多模态模型（如CLIP）。\n", "- **其他领域**：语音识别、时间序列预测等。\n", "\n", "---\n", "\n", "### **6. 变体与扩展**\n", "- **仅编码器模型**：如BERT，适用于文本分类、问答。\n", "- **仅解码器模型**：如GPT系列，专注于生成任务。\n", "- **稀疏注意力**：如Longformer，降低长序列的计算复杂度。\n", "\n", "---\n", "\n", "### **总结**\n", "Transformer通过自注意力机制和模块化设计，解决了传统序列模型的效率与长距离依赖问题，成为现代深度学习的基石。其设计思想不仅推动了NLP的进步，也深刻影响了计算机视觉和多模态领域。\n", "----- streaming request -----\n", "\n", "\n", "Transformer 是一种基于**注意力机制**的深度学习架构，由 Google 团队在 2017 年的论文《Attention Is All You Need》中提出。它彻底改变了自然语言处理（NLP）领域，取代了传统的循环神经网络（RNN）和卷积神经网络（CNN），成为现代模型（如 BERT、GPT 等）的核心架构。以下是其核心组件和工作原理的详细解析：\n", "\n", "---\n", "\n", "### **一、整体架构**\n", "Transformer 由 **编码器（Encoder）** 和 **解码器（Decoder）** 堆叠组成，两者均包含多个相同的层（通常为 6 层）。编码器处理输入序列，解码器生成输出序列。\n", "\n", "![Transformer架构图](https://miro.medium.com/v2/resize:fit:1400/1*BHzGVskWGS_3jEcYYi6miQ.png)\n", "\n", "---\n", "\n", "### **二、核心组件**\n", "\n", "#### 1. **自注意力机制（Self-Attention）**\n", "- **作用**：捕捉序列中不同位置间的依赖关系，无论距离远近。\n", "- **计算步骤**：\n", "  1. **生成 Q, K, V**：输入向量通过线性变换生成查询（Query）、键（Key）、值（Value）。\n", "  2. **计算注意力分数**：通过点积计算词与词之间的相关性，缩放后通过 Softmax 归一化。\n", "  3. **加权求和**：用注意力权重对 Value 加权，得到每个位置的上下文感知表示。\n", "  \n", "  公式：  \n", "  \\(\\text{Attention}(Q, K, V) = \\text{softmax}\\left(\\frac{QK^T}{\\sqrt{d_k}}\\right)V\\)\n", "\n", "#### 2. **多头注意力（Multi-Head Attention）**\n", "- **原理**：并行运行多个独立的注意力头，学习不同的关注模式。\n", "- **流程**：每个头的输出拼接后通过线性层融合，增强模型对不同语义关系的捕捉能力。\n", "\n", "#### 3. **位置编码（Positional Encoding）**\n", "- **作用**：为输入序列添加位置信息（因 Transformer 无循环结构）。\n", "- **方法**：使用正弦和余弦函数生成位置编码，与词向量相加。\n", "  \n", "  公式（第 \\(pos\\) 个位置，维度 \\(i\\)）：  \n", "  \\(PE_{(pos,2i)} = \\sin(pos/10000^{2i/d})\\)  \n", "  \\(PE_{(pos,2i+1)} = \\cos(pos/10000^{2i/d})\\)\n", "\n", "#### 4. **前馈网络（Feed-Forward Network, FFN）**\n", "- **结构**：两个线性层 + ReLU 激活，独立作用于每个位置。\n", "- 公式：  \n", "  \\(\\text{FFN}(x) = \\text{ReLU}(xW_1 + b_1)W_2 + b_2\\)\n", "\n", "#### 5. **残差连接与层归一化**\n", "- **残差连接**：每层的输入与输出相加，缓解梯度消失。\n", "- **层归一化**：加速训练，稳定模型。\n", "\n", "---\n", "\n", "### **三、编码器与解码器详解**\n", "\n", "#### **编码器层**\n", "1. **多头自注意力**：处理输入序列，关注全局上下文。\n", "2. **前馈网络**：进一步提取特征。\n", "\n", "#### **解码器层**\n", "1. **掩码多头自注意力**：防止当前位置关注后续位置（训练时使用）。\n", "2. **编码器-解码器注意力**：关注编码器的输出，关联输入与输出序列。\n", "3. **前馈网络**：同编码器。\n", "\n", "---\n", "\n", "### **四、训练与推理**\n", "\n", "- **训练**：解码器通过掩码自注意力避免信息泄露，使用交叉熵损失优化。\n", "- **推理**：自回归生成输出（如逐词生成翻译结果）。\n", "\n", "---\n", "\n", "### **五、优势与局限**\n", "\n", "#### **优势**\n", "1. **并行计算**：处理整个序列并行化，训练速度快于 RNN。\n", "2. **长距离依赖**：自注意力直接建模任意距离的词关系。\n", "3. **可扩展性**：堆叠更多层以提升性能。\n", "\n", "#### **局限**\n", "1. **计算资源消耗大**：尤其处理长序列时。\n", "2. **位置编码局限性**：绝对位置编码可能不足以处理复杂序列结构。\n", "\n", "---\n", "\n", "### **六、应用场景**\n", "- **机器翻译**（原始论文应用）\n", "- **文本生成**（如 GPT 系列）\n", "- **文本理解**（如 BERT、RoBERTa）\n", "- **图像处理**（Vision Transformer）\n", "\n", "---\n", "\n", "### **七、关键影响**\n", "Transformer 奠定了现代 NLP 的基础，推动了如 BERT、GPT-3 等模型的诞生，并扩展至计算机视觉等领域，成为 AI 领域最重要的架构之一。其核心思想——通过注意力机制动态捕捉上下文关系——已成为深度学习模型的标配。\n"]}], "execution_count": 1}, {"metadata": {"ExecuteTime": {"end_time": "2025-03-05T05:51:53.097703Z", "start_time": "2025-03-05T05:51:53.077854Z"}}, "cell_type": "code", "source": ["import json\n", "\n", "# 输入和输出文件名\n", "input_file = 'self_cognition.jsonl'  # 替换为你的输入文件名\n", "output_file = 'output_self_cognition.jsonl'  # 输出的文件名\n", "\n", "# 读取并转换\n", "with open(input_file, 'r', encoding='utf-8') as infile, \\\n", "     open(output_file, 'w', encoding='utf-8') as outfile:\n", "    \n", "    # 逐行处理\n", "    for line in infile:\n", "        # 解析每一行的json\n", "        data = json.loads(line.strip())\n", "        \n", "        # 转换为目标格式\n", "        converted_data = {\n", "            \"messages\": [\n", "                {\n", "                    \"role\": \"user\",\n", "                    \"content\": data[\"query\"]\n", "                },\n", "                {\n", "                    \"role\": \"assistant\",\n", "                    \"content\": data[\"response\"]\n", "                }\n", "            ]\n", "        }\n", "        \n", "        # 写入新的一行到输出文件\n", "        json.dump(converted_data, outfile, ensure_ascii=False)\n", "        outfile.write('\\n')\n", "\n", "print(f\"转换完成，结果已保存到 {output_file}\")"], "id": "798b010e3c62f37", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["转换完成，结果已保存到 output_self_cognition.jsonl\n"]}], "execution_count": 1}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.6"}}, "nbformat": 4, "nbformat_minor": 5}