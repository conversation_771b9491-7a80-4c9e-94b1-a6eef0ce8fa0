{"cells": [{"cell_type": "code", "id": "initial_id", "metadata": {"collapsed": true, "ExecuteTime": {"end_time": "2025-03-27T03:38:18.668152Z", "start_time": "2025-03-27T03:34:00.712737Z"}}, "source": ["import requests\n", "from bs4 import BeautifulSoup\n", "import pandas as pd\n", "import urllib3\n", "\n", "# 禁用SSL证书验证警告\n", "urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)\n", "\n", "def scrape_hcs_data(start_page, end_page):\n", "    base_url = \"https://hfffge51f32cb3b9346a8sfbw69xqb9u6v60wu.fxyh.librra.gdufs.edu.cn/List/AjaxHcsDataList\"\n", "    headers = {\n", "        'User-Agent': 'Apifox/1.0.0 (https://apifox.com)',\n", "        'Cookie': 'CWJSESSIONID=708915B4DD2F08657D566F5D098DB53C'\n", "    }\n", "    \n", "    all_data = []\n", "    \n", "    for page in range(start_page, end_page + 1):\n", "        url = f\"{base_url}?PageIndex={page}\"\n", "        try:\n", "            response = requests.post(url, headers=headers, verify=False)\n", "            if response.status_code == 200:\n", "                soup = BeautifulSoup(response.text, 'html.parser')\n", "                table = soup.find('table', {'id': 'datatable'})\n", "                \n", "                if table:\n", "                    rows = table.find_all('tr')[1:]  # 跳过表头\n", "                    for row in rows:\n", "                        cols = row.find_all('td')\n", "                        if len(cols) >= 5:  # 确保有5列数据\n", "                            data = {\n", "                                '序号': cols[0].get_text(strip=True),\n", "                                '作者': cols[1].get_text(strip=True),\n", "                                '单位': cols[2].get_text(strip=True),\n", "                                '学科': cols[3].get_text(strip=True),\n", "                                '类型': cols[4].get_text(strip=True)\n", "                            }\n", "                            all_data.append(data)\n", "                    print(f\"第 {page} 页数据提取完成\")\n", "                else:\n", "                    print(f\"第 {page} 页未找到表格\")\n", "            else:\n", "                print(f\"第 {page} 页请求失败，状态码：{response.status_code}\")\n", "        except Exception as e:\n", "            print(f\"第 {page} 页出错：{e}\")\n", "    \n", "    return all_data\n", "\n", "# 爬取1~5页数据\n", "data = scrape_hcs_data(5001, 5500)\n", "\n", "# 转换为DataFrame并保存为CSV\n", "df = pd.DataFrame(data)\n", "df.to_csv(\"高被引学者5001-5500.csv\", index=False, encoding='utf_8_sig')  # 避免中文乱码\n", "print(\"数据已保存为 高被引学者5001-6246.csv\")"], "outputs": [{"name": "stdout", "output_type": "stream", "text": ["第 5001 页数据提取完成\n", "第 5002 页数据提取完成\n", "第 5003 页数据提取完成\n", "第 5004 页数据提取完成\n", "第 5005 页数据提取完成\n", "第 5006 页数据提取完成\n", "第 5007 页数据提取完成\n", "第 5008 页数据提取完成\n", "第 5009 页数据提取完成\n", "第 5010 页数据提取完成\n", "第 5011 页数据提取完成\n", "第 5012 页数据提取完成\n", "第 5013 页数据提取完成\n", "第 5014 页数据提取完成\n", "第 5015 页数据提取完成\n", "第 5016 页数据提取完成\n", "第 5017 页数据提取完成\n", "第 5018 页数据提取完成\n", "第 5019 页数据提取完成\n", "第 5020 页数据提取完成\n", "第 5021 页数据提取完成\n", "第 5022 页数据提取完成\n", "第 5023 页数据提取完成\n", "第 5024 页数据提取完成\n", "第 5025 页数据提取完成\n", "第 5026 页数据提取完成\n", "第 5027 页数据提取完成\n", "第 5028 页数据提取完成\n", "第 5029 页数据提取完成\n", "第 5030 页数据提取完成\n", "第 5031 页数据提取完成\n", "第 5032 页数据提取完成\n", "第 5033 页数据提取完成\n", "第 5034 页数据提取完成\n", "第 5035 页数据提取完成\n", "第 5036 页数据提取完成\n", "第 5037 页数据提取完成\n", "第 5038 页数据提取完成\n", "第 5039 页数据提取完成\n", "第 5040 页数据提取完成\n", "第 5041 页数据提取完成\n", "第 5042 页数据提取完成\n", "第 5043 页数据提取完成\n", "第 5044 页数据提取完成\n", "第 5045 页数据提取完成\n", "第 5046 页数据提取完成\n", "第 5047 页数据提取完成\n", "第 5048 页数据提取完成\n", "第 5049 页数据提取完成\n", "第 5050 页数据提取完成\n", "第 5051 页数据提取完成\n", "第 5052 页数据提取完成\n", "第 5053 页数据提取完成\n", "第 5054 页数据提取完成\n", "第 5055 页数据提取完成\n", "第 5056 页数据提取完成\n", "第 5057 页数据提取完成\n", "第 5058 页数据提取完成\n", "第 5059 页数据提取完成\n", "第 5060 页数据提取完成\n", "第 5061 页数据提取完成\n", "第 5062 页数据提取完成\n", "第 5063 页数据提取完成\n", "第 5064 页数据提取完成\n", "第 5065 页数据提取完成\n", "第 5066 页数据提取完成\n", "第 5067 页数据提取完成\n", "第 5068 页数据提取完成\n", "第 5069 页数据提取完成\n", "第 5070 页数据提取完成\n", "第 5071 页数据提取完成\n", "第 5072 页数据提取完成\n", "第 5073 页数据提取完成\n", "第 5074 页数据提取完成\n", "第 5075 页数据提取完成\n", "第 5076 页数据提取完成\n", "第 5077 页数据提取完成\n", "第 5078 页数据提取完成\n", "第 5079 页数据提取完成\n", "第 5080 页数据提取完成\n", "第 5081 页数据提取完成\n", "第 5082 页数据提取完成\n", "第 5083 页数据提取完成\n", "第 5084 页数据提取完成\n", "第 5085 页数据提取完成\n", "第 5086 页数据提取完成\n", "第 5087 页数据提取完成\n", "第 5088 页数据提取完成\n", "第 5089 页数据提取完成\n", "第 5090 页数据提取完成\n", "第 5091 页数据提取完成\n", "第 5092 页数据提取完成\n", "第 5093 页数据提取完成\n", "第 5094 页数据提取完成\n", "第 5095 页数据提取完成\n", "第 5096 页数据提取完成\n", "第 5097 页数据提取完成\n", "第 5098 页数据提取完成\n", "第 5099 页数据提取完成\n", "第 5100 页数据提取完成\n", "第 5101 页数据提取完成\n", "第 5102 页数据提取完成\n", "第 5103 页数据提取完成\n", "第 5104 页数据提取完成\n", "第 5105 页数据提取完成\n", "第 5106 页数据提取完成\n", "第 5107 页数据提取完成\n", "第 5108 页数据提取完成\n", "第 5109 页数据提取完成\n", "第 5110 页数据提取完成\n", "第 5111 页数据提取完成\n", "第 5112 页数据提取完成\n", "第 5113 页数据提取完成\n", "第 5114 页数据提取完成\n", "第 5115 页数据提取完成\n", "第 5116 页数据提取完成\n", "第 5117 页数据提取完成\n", "第 5118 页数据提取完成\n", "第 5119 页数据提取完成\n", "第 5120 页数据提取完成\n", "第 5121 页数据提取完成\n", "第 5122 页数据提取完成\n", "第 5123 页数据提取完成\n", "第 5124 页数据提取完成\n", "第 5125 页数据提取完成\n", "第 5126 页数据提取完成\n", "第 5127 页数据提取完成\n", "第 5128 页数据提取完成\n", "第 5129 页数据提取完成\n", "第 5130 页数据提取完成\n", "第 5131 页数据提取完成\n", "第 5132 页数据提取完成\n", "第 5133 页数据提取完成\n", "第 5134 页数据提取完成\n", "第 5135 页数据提取完成\n", "第 5136 页数据提取完成\n", "第 5137 页数据提取完成\n", "第 5138 页数据提取完成\n", "第 5139 页数据提取完成\n", "第 5140 页数据提取完成\n", "第 5141 页数据提取完成\n", "第 5142 页数据提取完成\n", "第 5143 页数据提取完成\n", "第 5144 页数据提取完成\n", "第 5145 页数据提取完成\n", "第 5146 页数据提取完成\n", "第 5147 页数据提取完成\n", "第 5148 页数据提取完成\n", "第 5149 页数据提取完成\n", "第 5150 页数据提取完成\n", "第 5151 页数据提取完成\n", "第 5152 页数据提取完成\n", "第 5153 页数据提取完成\n", "第 5154 页数据提取完成\n", "第 5155 页数据提取完成\n", "第 5156 页数据提取完成\n", "第 5157 页数据提取完成\n", "第 5158 页数据提取完成\n", "第 5159 页数据提取完成\n", "第 5160 页数据提取完成\n", "第 5161 页数据提取完成\n", "第 5162 页数据提取完成\n", "第 5163 页数据提取完成\n", "第 5164 页数据提取完成\n", "第 5165 页数据提取完成\n", "第 5166 页数据提取完成\n", "第 5167 页数据提取完成\n", "第 5168 页数据提取完成\n", "第 5169 页数据提取完成\n", "第 5170 页数据提取完成\n", "第 5171 页数据提取完成\n", "第 5172 页数据提取完成\n", "第 5173 页数据提取完成\n", "第 5174 页数据提取完成\n", "第 5175 页数据提取完成\n", "第 5176 页数据提取完成\n", "第 5177 页数据提取完成\n", "第 5178 页数据提取完成\n", "第 5179 页数据提取完成\n", "第 5180 页数据提取完成\n", "第 5181 页数据提取完成\n", "第 5182 页数据提取完成\n", "第 5183 页数据提取完成\n", "第 5184 页数据提取完成\n", "第 5185 页数据提取完成\n", "第 5186 页数据提取完成\n", "第 5187 页数据提取完成\n", "第 5188 页数据提取完成\n", "第 5189 页数据提取完成\n", "第 5190 页数据提取完成\n", "第 5191 页数据提取完成\n", "第 5192 页数据提取完成\n", "第 5193 页数据提取完成\n", "第 5194 页数据提取完成\n", "第 5195 页数据提取完成\n", "第 5196 页数据提取完成\n", "第 5197 页数据提取完成\n", "第 5198 页数据提取完成\n", "第 5199 页数据提取完成\n", "第 5200 页数据提取完成\n", "第 5201 页数据提取完成\n", "第 5202 页数据提取完成\n", "第 5203 页数据提取完成\n", "第 5204 页数据提取完成\n", "第 5205 页数据提取完成\n", "第 5206 页数据提取完成\n", "第 5207 页数据提取完成\n", "第 5208 页数据提取完成\n", "第 5209 页数据提取完成\n", "第 5210 页数据提取完成\n", "第 5211 页数据提取完成\n", "第 5212 页数据提取完成\n", "第 5213 页数据提取完成\n", "第 5214 页数据提取完成\n", "第 5215 页数据提取完成\n", "第 5216 页数据提取完成\n", "第 5217 页数据提取完成\n", "第 5218 页数据提取完成\n", "第 5219 页数据提取完成\n", "第 5220 页数据提取完成\n", "第 5221 页数据提取完成\n", "第 5222 页数据提取完成\n", "第 5223 页数据提取完成\n", "第 5224 页数据提取完成\n", "第 5225 页数据提取完成\n", "第 5226 页数据提取完成\n", "第 5227 页数据提取完成\n", "第 5228 页数据提取完成\n", "第 5229 页数据提取完成\n", "第 5230 页数据提取完成\n", "第 5231 页数据提取完成\n", "第 5232 页数据提取完成\n", "第 5233 页数据提取完成\n", "第 5234 页数据提取完成\n", "第 5235 页数据提取完成\n", "第 5236 页数据提取完成\n", "第 5237 页数据提取完成\n", "第 5238 页数据提取完成\n", "第 5239 页数据提取完成\n", "第 5240 页数据提取完成\n", "第 5241 页数据提取完成\n", "第 5242 页数据提取完成\n", "第 5243 页数据提取完成\n", "第 5244 页数据提取完成\n", "第 5245 页数据提取完成\n", "第 5246 页数据提取完成\n", "第 5247 页数据提取完成\n", "第 5248 页数据提取完成\n", "第 5249 页数据提取完成\n", "第 5250 页数据提取完成\n", "第 5251 页数据提取完成\n", "第 5252 页数据提取完成\n", "第 5253 页数据提取完成\n", "第 5254 页数据提取完成\n", "第 5255 页数据提取完成\n", "第 5256 页数据提取完成\n", "第 5257 页数据提取完成\n", "第 5258 页数据提取完成\n", "第 5259 页数据提取完成\n", "第 5260 页数据提取完成\n", "第 5261 页数据提取完成\n", "第 5262 页数据提取完成\n", "第 5263 页数据提取完成\n", "第 5264 页数据提取完成\n", "第 5265 页数据提取完成\n", "第 5266 页数据提取完成\n", "第 5267 页数据提取完成\n", "第 5268 页数据提取完成\n", "第 5269 页数据提取完成\n", "第 5270 页数据提取完成\n", "第 5271 页数据提取完成\n", "第 5272 页数据提取完成\n", "第 5273 页数据提取完成\n", "第 5274 页数据提取完成\n", "第 5275 页数据提取完成\n", "第 5276 页数据提取完成\n", "第 5277 页数据提取完成\n", "第 5278 页数据提取完成\n", "第 5279 页数据提取完成\n", "第 5280 页数据提取完成\n", "第 5281 页数据提取完成\n", "第 5282 页数据提取完成\n", "第 5283 页数据提取完成\n", "第 5284 页数据提取完成\n", "第 5285 页数据提取完成\n", "第 5286 页数据提取完成\n", "第 5287 页数据提取完成\n", "第 5288 页数据提取完成\n", "第 5289 页数据提取完成\n", "第 5290 页数据提取完成\n", "第 5291 页数据提取完成\n", "第 5292 页数据提取完成\n", "第 5293 页数据提取完成\n", "第 5294 页数据提取完成\n", "第 5295 页数据提取完成\n", "第 5296 页数据提取完成\n", "第 5297 页数据提取完成\n", "第 5298 页数据提取完成\n", "第 5299 页数据提取完成\n", "第 5300 页数据提取完成\n", "第 5301 页数据提取完成\n", "第 5302 页数据提取完成\n", "第 5303 页数据提取完成\n", "第 5304 页数据提取完成\n", "第 5305 页数据提取完成\n", "第 5306 页数据提取完成\n", "第 5307 页数据提取完成\n", "第 5308 页数据提取完成\n", "第 5309 页数据提取完成\n", "第 5310 页数据提取完成\n", "第 5311 页数据提取完成\n", "第 5312 页数据提取完成\n", "第 5313 页数据提取完成\n", "第 5314 页数据提取完成\n", "第 5315 页数据提取完成\n", "第 5316 页数据提取完成\n", "第 5317 页数据提取完成\n", "第 5318 页数据提取完成\n", "第 5319 页数据提取完成\n", "第 5320 页数据提取完成\n", "第 5321 页数据提取完成\n", "第 5322 页数据提取完成\n", "第 5323 页数据提取完成\n", "第 5324 页数据提取完成\n", "第 5325 页数据提取完成\n", "第 5326 页数据提取完成\n", "第 5327 页数据提取完成\n", "第 5328 页数据提取完成\n", "第 5329 页数据提取完成\n", "第 5330 页数据提取完成\n", "第 5331 页数据提取完成\n", "第 5332 页数据提取完成\n", "第 5333 页数据提取完成\n", "第 5334 页数据提取完成\n", "第 5335 页数据提取完成\n", "第 5336 页数据提取完成\n", "第 5337 页数据提取完成\n", "第 5338 页数据提取完成\n", "第 5339 页数据提取完成\n", "第 5340 页数据提取完成\n", "第 5341 页数据提取完成\n", "第 5342 页数据提取完成\n", "第 5343 页数据提取完成\n", "第 5344 页数据提取完成\n", "第 5345 页数据提取完成\n", "第 5346 页数据提取完成\n", "第 5347 页数据提取完成\n", "第 5348 页数据提取完成\n", "第 5349 页数据提取完成\n", "第 5350 页数据提取完成\n", "第 5351 页数据提取完成\n", "第 5352 页数据提取完成\n", "第 5353 页数据提取完成\n", "第 5354 页数据提取完成\n", "第 5355 页数据提取完成\n", "第 5356 页数据提取完成\n", "第 5357 页数据提取完成\n", "第 5358 页数据提取完成\n", "第 5359 页数据提取完成\n", "第 5360 页数据提取完成\n", "第 5361 页数据提取完成\n", "第 5362 页数据提取完成\n", "第 5363 页数据提取完成\n", "第 5364 页数据提取完成\n", "第 5365 页数据提取完成\n", "第 5366 页数据提取完成\n", "第 5367 页数据提取完成\n", "第 5368 页数据提取完成\n", "第 5369 页数据提取完成\n", "第 5370 页数据提取完成\n", "第 5371 页数据提取完成\n", "第 5372 页数据提取完成\n", "第 5373 页数据提取完成\n", "第 5374 页数据提取完成\n", "第 5375 页数据提取完成\n", "第 5376 页数据提取完成\n", "第 5377 页数据提取完成\n", "第 5378 页数据提取完成\n", "第 5379 页数据提取完成\n", "第 5380 页数据提取完成\n", "第 5381 页数据提取完成\n", "第 5382 页数据提取完成\n", "第 5383 页数据提取完成\n", "第 5384 页数据提取完成\n", "第 5385 页数据提取完成\n", "第 5386 页数据提取完成\n", "第 5387 页数据提取完成\n", "第 5388 页数据提取完成\n", "第 5389 页数据提取完成\n", "第 5390 页数据提取完成\n", "第 5391 页数据提取完成\n", "第 5392 页数据提取完成\n", "第 5393 页数据提取完成\n", "第 5394 页数据提取完成\n", "第 5395 页数据提取完成\n", "第 5396 页数据提取完成\n", "第 5397 页数据提取完成\n", "第 5398 页数据提取完成\n", "第 5399 页数据提取完成\n", "第 5400 页数据提取完成\n", "第 5401 页数据提取完成\n", "第 5402 页数据提取完成\n", "第 5403 页数据提取完成\n", "第 5404 页数据提取完成\n", "第 5405 页数据提取完成\n", "第 5406 页数据提取完成\n", "第 5407 页数据提取完成\n", "第 5408 页数据提取完成\n", "第 5409 页数据提取完成\n", "第 5410 页数据提取完成\n", "第 5411 页数据提取完成\n", "第 5412 页数据提取完成\n", "第 5413 页数据提取完成\n", "第 5414 页数据提取完成\n", "第 5415 页数据提取完成\n", "第 5416 页数据提取完成\n", "第 5417 页数据提取完成\n", "第 5418 页数据提取完成\n", "第 5419 页数据提取完成\n", "第 5420 页数据提取完成\n", "第 5421 页数据提取完成\n", "第 5422 页数据提取完成\n", "第 5423 页数据提取完成\n", "第 5424 页数据提取完成\n", "第 5425 页数据提取完成\n", "第 5426 页数据提取完成\n", "第 5427 页数据提取完成\n", "第 5428 页数据提取完成\n", "第 5429 页数据提取完成\n", "第 5430 页数据提取完成\n", "第 5431 页数据提取完成\n", "第 5432 页数据提取完成\n", "第 5433 页数据提取完成\n", "第 5434 页数据提取完成\n", "第 5435 页数据提取完成\n", "第 5436 页数据提取完成\n", "第 5437 页数据提取完成\n", "第 5438 页数据提取完成\n", "第 5439 页数据提取完成\n", "第 5440 页数据提取完成\n", "第 5441 页数据提取完成\n", "第 5442 页数据提取完成\n", "第 5443 页数据提取完成\n", "第 5444 页数据提取完成\n", "第 5445 页数据提取完成\n", "第 5446 页数据提取完成\n", "第 5447 页数据提取完成\n", "第 5448 页数据提取完成\n", "第 5449 页数据提取完成\n", "第 5450 页数据提取完成\n", "第 5451 页数据提取完成\n", "第 5452 页数据提取完成\n", "第 5453 页数据提取完成\n", "第 5454 页数据提取完成\n", "第 5455 页数据提取完成\n", "第 5456 页数据提取完成\n", "第 5457 页数据提取完成\n", "第 5458 页数据提取完成\n", "第 5459 页数据提取完成\n", "第 5460 页数据提取完成\n", "第 5461 页数据提取完成\n", "第 5462 页数据提取完成\n", "第 5463 页数据提取完成\n", "第 5464 页数据提取完成\n", "第 5465 页数据提取完成\n", "第 5466 页数据提取完成\n", "第 5467 页数据提取完成\n", "第 5468 页数据提取完成\n", "第 5469 页数据提取完成\n", "第 5470 页数据提取完成\n", "第 5471 页数据提取完成\n", "第 5472 页数据提取完成\n", "第 5473 页数据提取完成\n", "第 5474 页数据提取完成\n", "第 5475 页数据提取完成\n", "第 5476 页数据提取完成\n", "第 5477 页数据提取完成\n", "第 5478 页数据提取完成\n", "第 5479 页数据提取完成\n", "第 5480 页数据提取完成\n", "第 5481 页数据提取完成\n", "第 5482 页数据提取完成\n", "第 5483 页数据提取完成\n", "第 5484 页数据提取完成\n", "第 5485 页数据提取完成\n", "第 5486 页数据提取完成\n", "第 5487 页数据提取完成\n", "第 5488 页数据提取完成\n", "第 5489 页数据提取完成\n", "第 5490 页数据提取完成\n", "第 5491 页数据提取完成\n", "第 5492 页数据提取完成\n", "第 5493 页数据提取完成\n", "第 5494 页数据提取完成\n", "第 5495 页数据提取完成\n", "第 5496 页数据提取完成\n", "第 5497 页数据提取完成\n", "第 5498 页数据提取完成\n", "第 5499 页数据提取完成\n", "第 5500 页数据提取完成\n", "数据已保存为 高被引学者5001-6246.csv\n"]}], "execution_count": 6}, {"metadata": {"ExecuteTime": {"end_time": "2025-03-27T03:38:37.842236Z", "start_time": "2025-03-27T03:38:37.834720Z"}}, "cell_type": "code", "source": ["# 转换为DataFrame并保存为CSV\n", "df.head()"], "id": "670db9ce50de1bee", "outputs": [{"data": {"text/plain": ["Empty DataFrame\n", "Columns: []\n", "Index: []"], "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "</div>"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "execution_count": 7}, {"metadata": {"ExecuteTime": {"end_time": "2025-03-27T03:33:21.112059Z", "start_time": "2025-03-27T03:33:21.102543Z"}}, "cell_type": "code", "source": "", "id": "714e14ef7f23a767", "outputs": [{"data": {"text/plain": ["[]"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "execution_count": 4}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.6"}}, "nbformat": 4, "nbformat_minor": 5}