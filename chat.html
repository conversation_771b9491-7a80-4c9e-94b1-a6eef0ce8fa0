<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>AI Chat Interface</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      background-color: #f4f4f9;
      margin: 0;
      padding: 0;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 100vh;
    }
    .chat-container {
      width: 400px;
      height: 600px;
      background: #fff;
      border-radius: 10px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      display: flex;
      flex-direction: column;
      overflow: hidden;
    }
    .chat-header {
      background: #007bff;
      color: white;
      padding: 15px;
      text-align: center;
      font-size: 18px;
      font-weight: bold;
      border-top-left-radius: 10px;
      border-top-right-radius: 10px;
    }
    .chat-box {
      flex: 1;
      padding: 15px;
      overflow-y: auto;
      border-bottom: 1px solid #ddd;
    }
    .message {
      margin: 15px 0;
      padding: 10px;
      border-radius: 5px;
      max-width: 80%;
    }
    .message.user {
      background: #e9ecef;
      align-self: flex-end;
    }
    .message.bot {
      background: #f1f8ff;
      align-self: flex-start;
    }
    .input-container {
      display: flex;
      padding: 10px;
      background: #f9f9f9;
      border-top: 1px solid #ddd;
    }
    .input-container input {
      flex: 1;
      padding: 10px;
      border: 1px solid #ddd;
      border-radius: 5px;
      outline: none;
      font-size: 14px;
    }
    .input-container button {
      padding: 10px 20px;
      margin-left: 10px;
      background: #007bff;
      color: white;
      border: none;
      border-radius: 5px;
      cursor: pointer;
      font-size: 14px;
    }
    .input-container button:hover {
      background: #0056b3;
    }
    .loading {
      color: #007bff;
      font-size: 14px;
      text-align: center;
      margin-top: 10px;
      animation: spin 1s linear infinite;
    }
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
  </style>
</head>
<body>
  <div class="chat-container">
    <div class="chat-header">AI Chat</div>
    <div class="chat-box" id="chatBox"></div>
    <div class="input-container">
      <input type="text" id="userInput" placeholder="Type your message here..." />
      <button onclick="sendMessage()">Send</button>
    </div>
    <div class="loading" id="loadingIndicator" style="display: none;">Loading...</div>
  </div>
  <script>
    const chatBox = document.getElementById('chatBox');
    const userInput = document.getElementById('userInput');
    const loadingIndicator = document.getElementById('loadingIndicator');

    function appendMessage(sender, message) {
      const messageElement = document.createElement('div');
      messageElement.classList.add('message', sender);
      messageElement.textContent = message;
      chatBox.appendChild(messageElement);
      chatBox.scrollTop = chatBox.scrollHeight;
    }

    async function sendMessage() {
      const userMessage = userInput.value.trim();
      if (!userMessage) return;

      // Append user message
      appendMessage('user', userMessage);
      loadingIndicator.style.display = 'block';

      try {
        const response = await fetch('https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer sk-470a9e513830456aaf67cff29209e4cb', // 替换为你的 API 密钥
          },
          body: JSON.stringify({
            model: "deepseek-r1-distill-qwen-32b", // 模型名称
            messages: [
              {
                role: "system",
                content: "你是一个很强的文本处理助手，接下来的问题你都使用中文回答问题"
              },
              {
                role: "user",
                content: userMessage
              }
            ]
          }),
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.message || `HTTP Error ${response.status}`);
        }

        const data = await response.json();
        const botMessage = data.choices?.[0]?.message?.content || 'Sorry, I could not understand that.';
        appendMessage('bot', botMessage);

        // Clear input only after successful response
        userInput.value = '';
      } catch (error) {
        console.error('Error details:', error);
        appendMessage('bot', error.message || 'An error occurred while processing your request.');
      } finally {
        loadingIndicator.style.display = 'none';
        userInput.focus();
      }
    }

    userInput.addEventListener('keydown', (event) => {
      if (event.key === 'Enter') {
        sendMessage();
      }
    });
  </script>
</body>
</html>