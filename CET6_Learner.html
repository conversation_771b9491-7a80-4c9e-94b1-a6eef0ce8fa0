<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>六级高频词汇记忆助手</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');

        :root {
            --primary-color: #4361ee;
            --secondary-color: #3a0ca3;
            --accent-color: #4cc9f0;
            --background-color: #f8f9fa;
            --card-color: rgba(255, 255, 255, 0.9);
            --text-color: #2b2d42;
            --correct-color: #4caf50;
            --wrong-color: #f44336;
            --glass-effect: blur(10px);
        }

        body {
            font-family: 'Poppins', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: var(--background-color);
            color: var(--text-color);
            margin: 0;
            padding: 0;
            line-height: 1.6;
            background-image:
                radial-gradient(circle at 10% 20%, rgba(67, 97, 238, 0.1) 0%, transparent 20%),
                radial-gradient(circle at 90% 80%, rgba(76, 201, 240, 0.1) 0%, transparent 20%),
                linear-gradient(135deg, #f5f7fa 0%, #e4e8f0 100%);
            background-attachment: fixed;
            min-height: 100vh;
        }

        .container {
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
        }

        header {
            text-align: center;
            margin-bottom: 30px;
            background-color: rgba(67, 97, 238, 0.9);
            color: white;
            padding: 25px;
            border-radius: 16px;
            box-shadow: 0 8px 32px rgba(31, 38, 135, 0.2);
            backdrop-filter: var(--glass-effect);
            -webkit-backdrop-filter: var(--glass-effect);
            border: 1px solid rgba(255, 255, 255, 0.18);
            position: relative;
            overflow: hidden;
        }

        header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0) 60%);
            transform: rotate(30deg);
            z-index: -1;
        }

        h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 600;
            letter-spacing: -0.5px;
        }

        .subtitle {
            font-size: 1.2em;
            opacity: 0.9;
            margin-top: 10px;
            font-weight: 300;
        }

        .game-area {
            background-color: var(--card-color);
            border-radius: 16px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(31, 38, 135, 0.1);
            margin-bottom: 25px;
            backdrop-filter: var(--glass-effect);
            -webkit-backdrop-filter: var(--glass-effect);
            border: 1px solid rgba(255, 255, 255, 0.3);
            position: relative;
            overflow: hidden;
        }

        .game-area::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #4361ee, #4cc9f0);
        }

        .word-display {
            font-size: 3em;
            text-align: center;
            margin: 30px 0;
            color: var(--secondary-color);
            min-height: 80px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            text-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .meaning-display {
            font-size: 1.5em;
            text-align: center;
            margin: 25px 0;
            min-height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--secondary-color);
            font-weight: 500;
            padding: 15px;
            background-color: rgba(67, 97, 238, 0.1);
            border-radius: 12px;
            border-left: 4px solid var(--primary-color);
        }

        .options {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 35px 0;
        }

        @media (max-width: 600px) {
            .options {
                grid-template-columns: 1fr;
            }
        }

        button {
            background-color: var(--primary-color);
            color: white;
            border: none;
            padding: 14px 24px;
            border-radius: 12px;
            font-size: 1.1em;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            font-weight: 500;
            letter-spacing: 0.5px;
            position: relative;
            overflow: hidden;
        }

        button::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(rgba(255,255,255,0.2), rgba(255,255,255,0));
            opacity: 0;
            transition: opacity 0.3s;
        }

        button:hover::after {
            opacity: 1;
        }

        button:hover {
            background-color: var(--secondary-color);
            transform: translateY(-3px);
            box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
        }

        button:active {
            transform: translateY(0);
        }

        button:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
            transform: none;
        }

        .option-btn {
            background-color: var(--card-color);
            color: var(--text-color);
            border: 2px solid var(--primary-color);
            transition: all 0.3s, transform 0.2s;
        }

        .option-btn:hover {
            background-color: var(--primary-color);
            color: white;
            transform: translateY(-3px) scale(1.02);
        }

        .correct {
            background-color: var(--correct-color) !important;
            color: white !important;
            border-color: var(--correct-color) !important;
        }

        .wrong {
            background-color: var(--wrong-color) !important;
            color: white !important;
            border-color: var(--wrong-color) !important;
        }

        .controls {
            display: flex;
            justify-content: space-between;
            margin-top: 25px;
            gap: 15px;
        }

        .controls button {
            flex: 1;
        }

        .stats {
            display: flex;
            justify-content: space-around;
            background-color: var(--card-color);
            padding: 20px;
            border-radius: 16px;
            margin-bottom: 25px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            backdrop-filter: var(--glass-effect);
            -webkit-backdrop-filter: var(--glass-effect);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .stat-item {
            text-align: center;
            padding: 0 15px;
            position: relative;
        }

        .stat-item:not(:last-child)::after {
            content: '';
            position: absolute;
            right: 0;
            top: 50%;
            transform: translateY(-50%);
            height: 50%;
            width: 1px;
            background-color: rgba(0,0,0,0.1);
        }

        .stat-value {
            font-size: 2em;
            font-weight: bold;
            color: var(--primary-color);
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 1em;
            opacity: 0.8;
            text-transform: uppercase;
            letter-spacing: 1px;
            font-weight: 500;
        }

        .progress-container {
            width: 100%;
            background-color: rgba(0,0,0,0.05);
            border-radius: 12px;
            margin: 25px 0;
            height: 12px;
            overflow: hidden;
            box-shadow: inset 0 1px 3px rgba(0,0,0,0.1);
        }

        .progress-bar {
            height: 100%;
            border-radius: 12px;
            background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
            width: 0%;
            transition: width 0.5s ease;
            position: relative;
            overflow: hidden;
        }

        .progress-bar::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(
                90deg,
                rgba(255, 255, 255, 0.1) 0%,
                rgba(255, 255, 255, 0.5) 50%,
                rgba(255, 255, 255, 0.1) 100%
            );
            background-size: 200% 100%;
            animation: shimmer 2s infinite;
        }

        @keyframes shimmer {
            0% { background-position: 200% 0; }
            100% { background-position: -200% 0; }
        }

        .hidden {
            display: none;
        }

        .example-sentence {
            font-style: italic;
            color: #666;
            margin: 20px 0;
            padding: 15px;
            background-color: rgba(240, 244, 248, 0.7);
            border-left: 4px solid var(--accent-color);
            border-radius: 8px;
            font-size: 1.1em;
            line-height: 1.6;
        }

        .pronunciation {
            text-align: center;
            margin: 15px 0;
            color: var(--secondary-color);
            font-size: 1.3em;
            font-weight: 500;
            background-color: rgba(67, 97, 238, 0.1);
            padding: 8px 15px;
            border-radius: 20px;
            display: inline-block;
        }

        .difficulty-buttons {
            display: flex;
            justify-content: center;
            gap: 12px;
            margin: 25px 0;
            flex-wrap: wrap;
        }

        .difficulty-btn {
            padding: 10px 18px;
            font-size: 0.95em;
            border-radius: 12px;
        }

        .active-difficulty {
            background-color: var(--secondary-color);
            font-weight: 600;
            box-shadow: 0 4px 8px rgba(58, 12, 163, 0.3);
        }

        .tabs {
            display: flex;
            margin-bottom: 20px;
            background-color: var(--card-color);
            border-radius: 12px;
            padding: 5px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            backdrop-filter: var(--glass-effect);
            -webkit-backdrop-filter: var(--glass-effect);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .tab {
            padding: 12px 24px;
            background-color: transparent;
            cursor: pointer;
            border-radius: 8px;
            margin-right: 5px;
            font-weight: 500;
            transition: all 0.3s ease;
            flex: 1;
            text-align: center;
        }

        .tab:hover {
            background-color: rgba(67, 97, 238, 0.1);
        }

        .tab.active {
            background-color: var(--primary-color);
            color: white;
            font-weight: 600;
            box-shadow: 0 4px 8px rgba(67, 97, 238, 0.2);
        }

        .tab-content {
            display: none;
            animation: fadeIn 0.5s ease;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .tab-content.active {
            display: block;
        }

        .review-list {
            max-height: 400px;
            overflow-y: auto;
            background-color: var(--card-color);
            padding: 20px;
            border-radius: 16px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            backdrop-filter: var(--glass-effect);
            -webkit-backdrop-filter: var(--glass-effect);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .review-item {
            padding: 15px;
            border-bottom: 1px solid rgba(0,0,0,0.05);
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: background-color 0.3s;
        }

        .review-item:hover {
            background-color: rgba(67, 97, 238, 0.05);
            border-radius: 8px;
        }

        .review-item:last-child {
            border-bottom: none;
        }

        .word-review {
            font-weight: 600;
            font-size: 1.1em;
            color: var(--secondary-color);
        }

        .meaning-review {
            color: #666;
            font-size: 0.95em;
            text-align: right;
            flex: 1;
            margin-left: 20px;
        }

        .timer {
            text-align: center;
            font-size: 1.3em;
            margin: 15px 0;
            color: var(--secondary-color);
            font-weight: 500;
            background-color: rgba(67, 97, 238, 0.1);
            padding: 8px 15px;
            border-radius: 20px;
            display: inline-block;
        }

        .settings {
            background-color: var(--card-color);
            padding: 25px;
            border-radius: 16px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 25px;
            backdrop-filter: var(--glass-effect);
            -webkit-backdrop-filter: var(--glass-effect);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .setting-item {
            margin-bottom: 20px;
        }

        .setting-item label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: var(--secondary-color);
        }

        .setting-item select, .setting-item input {
            width: 100%;
            padding: 12px 15px;
            border-radius: 12px;
            border: 1px solid rgba(0,0,0,0.1);
            background-color: rgba(255,255,255,0.7);
            font-size: 1em;
            transition: all 0.3s;
        }

        .setting-item select:focus, .setting-item input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(67, 97, 238, 0.2);
        }

        .word-import {
            background-color: var(--card-color);
            padding: 25px;
            border-radius: 16px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 25px;
            backdrop-filter: var(--glass-effect);
            -webkit-backdrop-filter: var(--glass-effect);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .word-textarea {
            width: 100%;
            height: 250px;
            padding: 15px;
            border-radius: 12px;
            border: 1px solid rgba(0,0,0,0.1);
            font-family: 'Consolas', monospace;
            margin-bottom: 15px;
            background-color: rgba(255,255,255,0.7);
            transition: all 0.3s;
            font-size: 0.95em;
            line-height: 1.6;
        }

        .word-textarea:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(67, 97, 238, 0.2);
        }

        .dark-mode {
            --primary-color: #5e60ce;
            --secondary-color: #6930c3;
            --accent-color: #5390d9;
            --background-color: #121212;
            --card-color: rgba(30, 30, 30, 0.9);
            --text-color: #e0e0e0;
            --glass-effect: blur(12px);
        }

        .dark-mode body {
            background-image:
                radial-gradient(circle at 10% 20%, rgba(94, 96, 206, 0.15) 0%, transparent 20%),
                radial-gradient(circle at 90% 80%, rgba(83, 144, 217, 0.15) 0%, transparent 20%),
                linear-gradient(135deg, #121212 0%, #1e1e1e 100%);
        }

        .dark-mode .word-textarea,
        .dark-mode .setting-item select,
        .dark-mode .setting-item input {
            background-color: rgba(40, 40, 40, 0.7);
            color: #e0e0e0;
            border-color: rgba(255,255,255,0.1);
        }

        .dark-mode .example-sentence {
            background-color: rgba(40, 40, 40, 0.7);
            color: #b0b0b0;
        }

        .dark-mode .stat-item:not(:last-child)::after {
            background-color: rgba(255,255,255,0.1);
        }

        .theme-toggle {
            position: fixed;
            bottom: 25px;
            right: 25px;
            background-color: var(--primary-color);
            color: white;
            border: none;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            font-size: 1.8em;
            cursor: pointer;
            box-shadow: 0 6px 12px rgba(0, 0, 0, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 100;
            transition: all 0.3s ease;
        }

        .theme-toggle:hover {
            transform: scale(1.1) rotate(30deg);
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
        }

        .confetti {
            position: fixed;
            width: 12px;
            height: 12px;
            background-color: #f00;
            border-radius: 50%;
            pointer-events: none;
            z-index: 999;
        }

        .builtin-words-btn {
            margin-top: 15px;
            background-color: #7209b7;
            transition: all 0.3s;
        }

        .builtin-words-btn:hover {
            background-color: #5a189a;
            transform: translateY(-3px);
        }

        .import-btn {
            background-color: #4361ee;
        }

        .import-result {
            margin-top: 15px;
            padding: 15px;
            background-color: rgba(67, 97, 238, 0.1);
            border-radius: 12px;
            color: var(--secondary-color);
            font-weight: 500;
        }

        /* 浮动气泡背景 */
        .bubbles {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            overflow: hidden;
        }

        .bubble {
            position: absolute;
            bottom: -100px;
            background: rgba(67, 97, 238, 0.1);
            border-radius: 50%;
            opacity: 0.5;
            animation: rise 15s infinite ease-in;
        }

        .bubble:nth-child(1) {
            width: 40px;
            height: 40px;
            left: 10%;
            animation-duration: 8s;
        }

        .bubble:nth-child(2) {
            width: 20px;
            height: 20px;
            left: 20%;
            animation-duration: 5s;
            animation-delay: 1s;
        }

        .bubble:nth-child(3) {
            width: 50px;
            height: 50px;
            left: 35%;
            animation-duration: 7s;
            animation-delay: 2s;
        }

        .bubble:nth-child(4) {
            width: 80px;
            height: 80px;
            left: 50%;
            animation-duration: 11s;
            animation-delay: 0s;
        }

        .bubble:nth-child(5) {
            width: 35px;
            height: 35px;
            left: 55%;
            animation-duration: 6s;
            animation-delay: 1s;
        }

        .bubble:nth-child(6) {
            width: 45px;
            height: 45px;
            left: 65%;
            animation-duration: 8s;
            animation-delay: 3s;
        }

        .bubble:nth-child(7) {
            width: 25px;
            height: 25px;
            left: 75%;
            animation-duration: 7s;
            animation-delay: 2s;
        }

        .bubble:nth-child(8) {
            width: 80px;
            height: 80px;
            left: 80%;
            animation-duration: 6s;
            animation-delay: 1s;
        }

        @keyframes rise {
            0% {
                bottom: -100px;
                transform: translateX(0);
            }
            50% {
                transform: translateX(100px);
            }
            100% {
                bottom: 1080px;
                transform: translateX(-200px);
            }
        }

        /* 响应式调整 */
        @media (max-width: 768px) {
            .container {
                padding: 15px;
            }

            header {
                padding: 20px 15px;
            }

            h1 {
                font-size: 2em;
            }

            .word-display {
                font-size: 2.5em;
            }

            .controls {
                flex-direction: column;
            }

            .stats {
                flex-wrap: wrap;
            }

            .stat-item {
                width: 50%;
                margin-bottom: 15px;
            }

            .stat-item:not(:last-child)::after {
                display: none;
            }
        }
    </style>
</head>
<body>
    <!-- 浮动气泡背景 -->
    <div class="bubbles">
        <div class="bubble"></div>
        <div class="bubble"></div>
        <div class="bubble"></div>
        <div class="bubble"></div>
        <div class="bubble"></div>
        <div class="bubble"></div>
        <div class="bubble"></div>
        <div class="bubble"></div>
        <div class="bubble"></div>
        <div class="bubble"></div>
    </div>

    <div class="container">
        <header>
            <h1>六级高频词汇记忆助手</h1>
            <div class="subtitle">现代化设计 · 高效记忆 · 开箱即用</div>
        </header>

        <div class="stats">
            <div class="stat-item">
                <div class="stat-value" id="correct-count">0</div>
                <div class="stat-label">正确</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="wrong-count">0</div>
                <div class="stat-label">错误</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="streak-count">0</div>
                <div class="stat-label">连胜</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="progress-percent">0%</div>
                <div class="stat-label">进度</div>
            </div>
        </div>

        <div class="progress-container">
            <div class="progress-bar" id="progress-bar"></div>
        </div>

        <div class="tabs">
            <div class="tab active" data-tab="game">记忆游戏</div>
            <div class="tab" data-tab="import">导入单词</div>
            <div class="tab" data-tab="review">复习列表</div>
            <div class="tab" data-tab="settings">设置</div>
        </div>

        <div class="tab-content active" id="game-tab">
            <div class="difficulty-buttons">
                <button class="difficulty-btn active-difficulty" data-difficulty="all">全部词汇</button>
                <button class="difficulty-btn" data-difficulty="easy">简单</button>
                <button class="difficulty-btn" data-difficulty="medium">中等</button>
                <button class="difficulty-btn" data-difficulty="hard">困难</button>
            </div>

            <div class="game-area">
                <div class="timer" id="timer">时间: 0秒</div>
                <div class="word-display" id="word-display">准备开始</div>
                <div class="pronunciation hidden" id="pronunciation"></div>
                <div class="meaning-display hidden" id="meaning-display"></div>
                <div class="example-sentence hidden" id="example-sentence"></div>

                <div class="options hidden" id="options">
                    <!-- 选项按钮将通过JavaScript动态生成 -->
                </div>

                <div class="controls">
                    <button id="show-answer-btn">显示答案</button>
                    <button id="next-word-btn">下一个单词</button>
                </div>
            </div>
        </div>

        <div class="tab-content" id="import-tab">
            <div class="word-import">
                <h2>导入单词表</h2>
                <p>请按照以下格式输入单词（每行一个单词）：</p>
                <p><em>1. alter v. 改变，改动，变更<br>
                2. burst vi. n. 突然发生，爆裂<br>
                3. dispose vi. 除掉；处置；解决；处理(of)</em></p>

                <textarea class="word-textarea" id="word-textarea" placeholder="请输入单词列表..."></textarea>
                <button id="import-btn" class="import-btn">导入单词</button>
                <button id="load-builtin-btn" class="builtin-words-btn">加载内置词汇</button>
                <div id="import-result" class="import-result hidden"></div>
            </div>
        </div>

        <div class="tab-content" id="review-tab">
            <h2>复习列表</h2>
            <div class="review-list" id="review-list">
                <!-- 复习列表将通过JavaScript动态生成 -->
            </div>
        </div>

        <div class="tab-content" id="settings-tab">
            <h2>设置</h2>
            <div class="settings">
                <div class="setting-item">
                    <label for="mode-select">学习模式</label>
                    <select id="mode-select">
                        <option value="choice">选择题模式</option>
                        <option value="recall">回忆模式</option>
                    </select>
                </div>
                <div class="setting-item">
                    <label for="timer-toggle">显示计时器</label>
                    <select id="timer-toggle">
                        <option value="on">开启</option>
                        <option value="off">关闭</option>
                    </select>
                </div>
                <div class="setting-item">
                    <label for="example-toggle">显示例句</label>
                    <select id="example-toggle">
                        <option value="on">开启</option>
                        <option value="off">关闭</option>
                    </select>
                </div>
                <div class="setting-item">
                    <label for="pronunciation-toggle">显示音标</label>
                    <select id="pronunciation-toggle">
                        <option value="on">开启</option>
                        <option value="off">关闭</option>
                    </select>
                </div>
            </div>
        </div>
    </div>

    <button class="theme-toggle" id="theme-toggle">🌓</button>

    <script>
        // 全局变量
        let wordList = [];
        let currentWord = null;
        let correctCount = 0;
        let wrongCount = 0;
        let streakCount = 0;
        let maxStreak = 0;
        let seenWords = new Set();
        let startTime = 0;
        let timerInterval = null;
        let currentDifficulty = "all";
        let isDarkMode = false;

        // 内置CET-6高频词汇库
        const builtInWords = `1. alter v. 改变，改动，变更
2. burst vi. n. 突然发生，爆裂
3. dispose vi. 除掉；处置；解决；处理(of)
4. blast n. 爆炸；气流 vi. 炸，炸掉
5. consume v. 消耗，耗尽
6. split v. 劈开；割裂；分裂 a.裂开的
7. spit v. 吐(唾液等)；唾弃
8. spill v. 溢出，溅出，倒出
9. slip v. 滑动，滑落；忽略
10. slide v. 滑动，滑落 n. 滑动；滑面；幻灯片
11. bacteria n. 细菌
12. breed n. 种，品种 v. 繁殖，产仔
13. budget n. 预算 v. 编预算，作安排
14. candidate n. 候选人
15. campus n. 校园
16. liberal a. 慷慨的；丰富的；自由的
17. transform v. 转变，变革；变换
18. transmit v. 传播，播送；传递
19. transplant v. 移植
20. transport vat. 运输，运送 n. 运输，运输工具
21. shift v. 转移；转动；转变
22. vary v. 变化，改变；使多样化
23. vanish vi. 消灭，不见
24. swallow v. 吞下，咽下 n. 燕子
25. suspicion n. 怀疑，疑心
26. suspicious a. 怀疑的，可疑的
27. mild a. 温暖的，暖和的；温柔的，味淡的
28. tender a. 温柔的；脆弱的
29. nuisance n. 损害，妨害，讨厌(的人或事物)
30. insignificant a. 无意义的，无足轻重的；无价值的
31. accelerate vt. 加速，促进
32. absolute a. 绝对的，无条件的；完全的
33. boundary n. 分界线，边界
34. brake n. 刹车，制动器 v. 刹住(车)
35. catalog n. 目录(册) v. 编目
36. vague a. 模糊的，不明确的
37. vain n. 徒劳，白费
38. extinct a. 绝灭的，熄灭的
39. extraordinary a. 不平常的，特别的，非凡的
40. extreme a. 极度的，极端的 n. 极端，过分
41. agent n. 代理人，代理商；动因，原因
42. alcohol n. 含酒精的饮料，酒精
43. appeal n. /vi. 呼吁，恳求
44. appreciate vt. 重视，赏识，欣赏
45. approve v. 赞成，同意，批准
46. stimulate vt. 刺激，激励
47. acquire vt. 取得，获得；学到
lish vt .完成，到达；实行
49. network n. 网状物；广播网，电视网；网络
50. tide n. 潮汐；潮流
51. tidy a. 整洁的，整齐的
52. trace vt. 追踪，找到 n. 痕迹，踪迹
53. torture n./vt. 拷打，折磨
54. wander vi. 漫游，闲逛
55. wax n. 蜡
56. weave v. 织，编
57. preserve v. 保护，保存，保持，维持
61. abuse v. 滥用，虐待；谩骂
62. academic a. 学术的；高等院校的；研究院的
63. academy n. (高等)专科院校；学会
64. battery n. 电池(组)
65. barrier n. 障碍；棚栏
66. cargo n. (船、飞机等装载的)货物
67. career n. 生涯，职业
68. vessel n. 船舶；容器，器皿；血管
69. vertical a. 垂直的
70. oblige v. 迫使，责成；使感激
71. obscure a. 阴暗，模糊
72. extent n. 程度，范围，大小，限度
73. exterior n. 外部，外表 a. 外部的，外表的
74. external a. 外部的，外表的，外面的
75. petrol n. 汽油
76. petroleum n. 石油
77. delay vt./n. 推迟，延误，耽搁
78. decay vi. 腐烂，腐朽
79. decent a. 像样的，体面的
80. route n. 路；路线；航线
81. ruin v. 毁坏，破坏 n. 毁灭，[pl.]废墟
82. sake n. 缘故，理由
83. satellite n. 卫星
84. scale n. 大小，规模；等级；刻度
85. temple n. 庙宇
86. tedious a. 乏味道，单调的，
87. tend vi.易于，趋向
88. tendency n.趋向，趋势
89. ultimate a. 极端的，最大的，最终的 n. 极端
90. undergo v. 经历，遭受
91. abundant a. 丰富的，充裕的，大量的
92. adopt v. 收养；采用；采纳
93. adapt vi. 适应，适合；改编，改写 vt. 使适应
94. bachelor n. 学士，学士学位；单身汉
95. casual a. 偶然的，碰巧的；临时的；非正式的
96. trap n. 陷阱，圈套 v. 设陷阱捕捉
97. vacant a. 空的，未占用的
98. vacuum n. 真空，真空吸尘器
99. oral a. 口头的，口述的，口的
100. optics n. (单、复数同形)光学
101. organ n. 器官，风琴
102. excess n. 过分，过量，过剩
103. expel v. 驱逐，开除，赶出
104. expend v. 消费
105. expenditure n. 支出，消费；经费
106. expense n. 开销，费用
107. expensive a. 花钱多的；价格高贵的
108. expand v. 扩大，扩张；展开，膨胀
109. expansion n. 扩大，扩充；发展，膨胀
110. private a. 私人的，个人的
111. individual a. 个别的，单独的 n. 个人，个体
112. personal a. 个人的，私人的；亲自的
114. personnel n. [总称] 人员，员工；人事部门
115. the Pacific Ocean 太平洋
116. the Atlantic Ocean 大西洋
117. the Arctic Ocean 北冰洋
118. the Antarctic Ocean 南冰洋
119. grant vt. 授予，同意，准予
119. grand a. 宏伟大，壮丽的，重大的
120. invade v. 侵入，侵略，侵袭
121. acid n. 酸，酸性物质 a. 酸的；尖刻的
122. acknowledge v. 承认；致谢
123. balcony n. 阳台
124. calculate vt. 计算，核算
125. calendar n. 日历，月历
126. optimistic a. 乐观
127. optional a. 可以任选的，非强制的
128. outstanding a. 杰出的，突出的，显著的
129. export n. 出口(物) v. 出口，输出
130. import n. 进口(物) v. 进口，输入
131. impose vt. 把…加强(on)；采用，利用
132. religion n. 宗教，宗教信仰
133. religious a. 宗教的
134. victim n. 牺牲品，受害者
135. video n. 电视，视频 a. 电视的，录像的
136. videotape n. 录像磁带 v. 把…录在录像带上
137. offend v. 冒犯，触犯
138. bother v. 打搅，麻烦
139. interfere v. 干涉，干扰，妨碍
140. internal a. 内部的，国内的
141. beforehand ad. 预先，事先
142. racial a. 人种的种族的
143. radiation n. 放射物，辐射
144. radical a.根本的；激进的
145. range n. 幅度，范围 v. (在某范围内)变动
146. wonder n. 惊奇，奇迹 v. 想知道，对…感到疑惑
147. isolate vt. 使隔离，使孤立
148. issue n. 问题，争论点；发行，(报刊)一期
149. hollow a. 空的，中空的，空虚道
150. hook n. 钩 vt. 钩住
151. adequate a. 适当地；足够
152. adhere vi. 粘附，附着；遵守，坚持
153. ban vt. 取缔，禁止
154. capture vt. 俘虏，捕获
155. valid a. 有效的，有根据的；正当的
156. valley n. 山谷，峡谷
157. consistent a. 坚固定；一致的，始终如一的
158. continuous a. 继续的，连续(不断)的
159. continual a. 不断地，频繁的
160. explode v. 爆炸；爆发；激增
161. exploit v. 剥削；利用，开采
162. explore v. 勘探
163. explosion n. 爆炸；爆发；激增
164. explosive a. 爆炸的；极易引起争论的
165. remote a. 遥远的，偏僻的
166. removal n. 除去，消除
167. render vt. 使得，致使
168. precaution n. 预防，防备，警惕
169. idle a. 懒散的，无所事事的
170. identify vt. 认出，鉴定
171. identity n. 身份；个性，特性
172. poverty n. 贫穷
173. resistant a. (to) 抵抗的，抗…的，耐…的
174. resolve vt. 解决；决定，决意
175. barrel n. 桶
176. bargain n. 便宜货 vi. 讨价还价
177. coarse a. 粗的，粗糙的，粗劣的
178. coach n. 教练；长途公共汽车
179. code n. 准则，法规，密码
180. coil n. 线圈 v. 卷，盘绕
181. adult n. 成年人
182. advertise v. 为…做广告
183. advertisement n. 广告
184. agency n. 代理商，经销商
185. focus v. (使)聚集 n. 焦点，中心，聚焦
186. forbid vt. 不许，禁止
187. debate n. /v. 辩论，争论
188. debt n. 欠债
189. decade n. 十年
190. enclose vt. 围住；把…装入信封
191. encounter vt. /n. 遭遇，遭到
192. globe n. 地球，世界；地球仪
193. global a. 全球的；总的
194. scan vt. 细看；扫描；浏览
195. scandal n. 丑事，丑闻
196. significance n. 意义；重要性
197. subsequent a. 随后的，后来的
198. virtue n. 美德，优点
199. virtual a. 实际上的，事实上的
200. orient vt. 使适应，(to, toward)使朝向 n. 东方
201. portion n. 一部分
202. target n. 目标，靶子 vt. 瞄准
203. portable a. 手提式的
204. decline v. 拒绝，谢绝；下降
205. illusion n. 错觉
206. likelihood n. 可能，可能性
207. stripe n. 条纹
208. emphasize vt. 强调，着重
209. emotion n. 情感，感情
210. emotional a. 感情的，情绪(上)的
211. awful a. 极坏的，威严的，可怕的
212. awkward a. 笨拙的，棘手的
213. clue n. 线索，提示
214. collision n. 碰撞，冲突
215. device n. 装置，设备
216. devise vt. 发明，策划，想出
217. inevitable a. 不可避免的
218. naval a. 海军的
219. navigation n. 航行
220. necessity n. 必需品；必要性
221. previous a. 先，前，以前的
222. provision n. [pl.] 给养，口粮；准备，设备，装置
223. pursue vt. 追逐；追求；从事，进行
224. stale a. 不新鲜的，陈腐的
225. substitute n. 代用品 vt. 代替
226. deserve vt. 应受，应得，值得
227. discrimination n. 歧视；辨别力
228. professional a. 职业的，专门的
229. secure a. 安全的，可靠的
230. security n. 安全，保障
231. scratch v. /n. 抓，搔，扒
232. talent n. 才能，天资；人才
233. insurance n. 保险，保险费
234. insure vt. 给…保险，保证，确保
235. nevertheless ad. 仍然，然而，不过
236. neutral a. 中立的，中性的
237. spot n. 地点；斑点 vt. 认出，发现；玷污
238. spray v. 喷，(使)溅散
239. medium a. 中等的，适中的 n. 媒介物，新闻媒介
240. media n. 新闻传媒
241. auxiliary a. 辅助的，备用的
242. automatic a. 自动的
243. compete vi. 竞争，比赛
244. competent a. 有能力的，能胜任的
245. competition n. 竞争，比赛
246. distribute vt. 分发
247. disturb vt. 打搅，妨碍
248. infer v. 推论，推断
249. integrate v. (into, with) (使)成为一体，(使)合并
250. moist a. 潮湿
251. moisture n. 潮湿
252. promote vt. 促进；提升
253. region n. 地区；范围；幅度
254. register v./n.登记，注册
255. stable a. 稳定的
256. sophisticated a. 老于世故的，老练的；很复杂的
257. splendid a. 极好的，壮丽的，辉煌的
258. cancel vt. 取消，废除
259. variable a. 易变的，可变的
260. prospect n. 前景，前途；景象
261. prosperity n.兴旺，繁荣
262. aspect n. 方面；朝向；面貌
263. cope vi. (with)(成功地)应付，处理
264. core n. 果心，核心
265. maintain vt. 维持，保持；坚持，主张
266. mainland n. 大陆
267. discipline n. 纪律；惩罚；学科
268. domestic a. 本国的，国内的；家用的；家庭的
269. constant a. 不变的，恒定的 n. 常数
270. cliff n. 悬崖，峭壁
271. authority n. 权威；当局
272. audio a. 听觉
273. attitude n. 态度
274. community n. 社区，社会
275. commit vt. 犯(错误，罪行等)，干(坏事等)
276. comment n. /vt. 评论
277. distinguish vt. 区分，辨别
278. distress n. 痛苦，悲伤 vt. 使痛苦
279. facility n. [pl.] 设备，设施；便利，方便
280. faculty n. 能力，技能；系，学科，学院；全体教员
281. mixture n. 混合，混合物
282. mood n. 心情，情绪；语气
283. moral a. 道德上的，有道德的
284. prominent a. 突出的
285. substance n. 物质；实质
286. substantial a. 可观的；牢固的；实质的
287. prompt vt. 促使 a. 敏捷的，及时的
288. vivid a. 生动的
289. vocabulary n. 词汇(量)；词汇表
290. venture n. 风险投资，风险项目 v. 冒险；取于
291. version n. 版本，译本；说法
292. waist n. 腰，腰部
293. weld v. /n. 焊接
294. yawn vi. 打哈欠
295. yield vi. (to)屈服于；让出，放弃 n. 产量
296. zone n. 地区，区域
297. strategy n. 战略，策略
298. strategic a. 战略(上)的，关键的
299. tense a. 紧张的 v. 拉紧 n. 时态
300. tension n. 紧张(状态)，张力
301. avenue n. 林荫道，大街
302. available a. 现成可用的；可得到的
303. comparable a. (with, to) 可比较的，类似的
304. comparative a. 比较的，相对的
305. dash vi. 猛冲，飞奔
306. data n. 数据，资料
307. dive vi. 跳水，潜水
308. diverse a. 不同的，多种多样的
309. entitle vt. 给…权利，给…资格
310. regulate vt. 管理，调节
311. release vt. /n. 释放，排放；解释解脱
312. exaggerate v. 夸大，夸张
313. evil a. 邪恶的，坏的
314. shrink vi. 起皱，收缩；退缩
315. subtract v. 减(去)
316. suburb n. 市郊
317. subway n. 地铁
318. survey n. /vt. 调查，勘测
319. wealthy a. 富裕的
320. adjust v. 调整，调节
321. attach vt. 系，贴；使附属
322. profit n. 利润，益处；v. 有益于，有利于
323. profitable a. 有利可图的
324. slope n. 斜坡，斜面
325. reinforce vt. 增强，加强
326. reject vt. 拒绝
327. fatal a. 致命的；重大的
328. fate n. 命运
329. humble a. 谦逊的；谦虚的
330. illegal a. 不合法的，非法的
331. award vt. 授予，判给 n. 奖品，奖金
332. aware a. 意识到
333. column n. 柱，圆柱；栏，专栏
334. comedy n. 喜剧
335. dumb a. 哑的；沉默的
336. dump vt. 倾卸，倾倒
337. deaf a. 聋的；不愿听的
338. decorate vt. 装饰，装璜
339. principal a. 最重要的 n. 负责人，校长
340. principle n. 原则，原理
341. prior a. 优先的，在前的
342. priority n. 优先，重点
343. prohibit vt. 禁止，不准
344. remarkable a. 值得注意的，异常的，非凡的
345. remedy n. /vt. 补救，医治，治疗
346. repetition n. 重复，反复
347. vain a. 徒劳的，无效的
348. undertake vt. 承担，着手做；同意，答应
349. unique a. 唯一的，独特的
350. obstacle n. 障碍(物)，妨碍
351. odd a. 奇特的，古怪的；奇数的
352. omit vt. 省略
353. opponent n. 敌手，对手
354. opportunity n. 机会，时机
355. orchestra n. 管弦乐队
356. semester n. 学期；半年
357. semiconductor n. 半导体
358. seminar n. 研讨会
359. terminal a. 末端的，极限的 n. 终点
360. territory n. 领土
361. approximate a. 大概的，大约 v. 近似
362. arbitrary a. 随意的，未断的
363. architect n. 建筑师
364. architecture n. 建筑学
365. biology n. 生物学
366. geography n. 地理(学)
367. geology n. 地质学
368. geometry n. 几何(学)
369. arithmetic n. 算术
370. algebra n. 代数
371. entertainment n. 娱乐；招待，款待
372. enthusiasm n. 热情，热心
373. entry n. 进入，入口处；参赛的人(或物)
374. environment n. 环境
375. episode n. 插曲，片段
376. equation n. 方程(式)
377. restrain vt. 阻止，抑制
378. restraint n. 抑制，限制
379. resume v. (中断后)重新开始
380. severe a. 严重的
381. sexual a. 性的
382. simplicity n. 简单；朴素
383. simplify vt. 简化
384. sorrow n. 悲哀，悲痛
385. stuff n. 原料，材料 vt. 填进，塞满
386. temporary a. 暂时的，临时的
387. temptation n. 诱惑，引诱
388. terror n. 恐怖
389. thrust v. 挤，推，插
390. treaty n. 条约，协定
391. arise vi. 产生，出现，发生；起身
392. arouse vt. 引起，激起；唤醒
393. burden n. 重担，负荷
394. bureau n. 局，办事处
395. marvelous a. 奇迹般的，惊人的
396. massive a. 大的，大量的，大块的
397. mature a. 成熟的
398. maximum a. 最高的，最大的
399. minimum a. 最低的，最小的
400. nonsense n. 胡说，冒失的行动
401. nuclear a. 核子的，核能的
402. nucleus n. 核
403. retail n. /v. /ad. 零售
404. retain vt. 保留，保持
405. restrict vt. 限制，约束
406. sponsor n. 发起者，主办者 vt. 发起，主办，资助
407. spur n. /vt. 刺激，激励
408. triumph n. 胜利，成功
409. tuition n. 学费
410. twist vt. 使缠绕；转动；扭歪
411. undergraduate n. 大学肄业生
412. universal a. 普遍的，通用的；宇宙的
413. universe n. 宇宙
414. via prep. 经由，经过，通过
415. vibrate v. 振动，摇摆
416. virus n. 病毒
417. voluntary a. 自愿的
418. volunteer n. 志愿者 v. 自愿(做)
419. vote v. 选举 n. 选票
420. wagon n. 四轮马车，铁路货车
421. appoint vt. 任命，委派
422. approach v. 靠近，接近 n. 途径，方式
423. appropriate a. 适当的
424. bunch n. 群，伙；束，串
425. bundle n. 捆，包，束 vt. 收集，归拢
426. ceremony n. 典礼，仪式
427. chaos n. 混乱，紊乱
428. discount n. (价格)折扣
429. display n. /vt. 陈列，展览
430. equivalent a. 相等的 a. 相等物
431. erect a. 竖直的 v. 建造，竖立
432. fax n. /vt. 传真
433. fertile a. 肥沃的；多产的
434. fertilizer n. 肥料
435. grateful a. 感激的
436. gratitude n. 感激
437. horror n. 恐怖
438. horrible a. 可怕的
439. Internet n. 国际互联网，因特网
440. interpret v. 翻译，解释
441. interpretation n. 解释，说明
442. jungle n. 丛林，密林
443. knot n. 结 vt. 把…打成结
444. leak v. 漏，渗出
445. lean vi. 倾斜，倚，靠
446. leap vi. 跳跃
447. modify vt. 修改
448. nylon n. 尼龙
449. onion n. 洋葱
450. powder n. 粉末
451. applicable a. 可应用的，适当的
452. applicant n. 申请人
453. breadth n. 宽度
454. conservation n. 保存，保护
455. conservative a. 保守的
456. parallel n. 平行线；可相比拟的事物
457. passion n. 激情，热情
458. passive a. 被动的，消极的
459. pat v. /n. 轻拍，轻打
460. peak n. 山峰，顶点
461. phenomenon n. 现象
462. reluctant a. 不情愿的，勉强的
463. rely vi. (on ,upon)依赖，指望
464. relevant a. 有关的，切题的
465. reliable a. 可靠的
466. relief n. 轻松，宽慰；减轻
467. reputation n. 名气，声誉
468. rescue vt. /n. 营救
469. triangle n. 三角(形)
470. sequence n. 连续；顺序
471. shallow a. 浅的
472. shiver vi/n. 发抖
473. shrug v. /n. 耸肩
474. signature n. 签名
475. sincere a. 诚挚的，真诚的
476. utility n. 功用，效用
477. utilize vt. 利用
478. utter vt. 说出 a. 完全的，彻底的
479. variation n. 变化，变动
480. vehicle n. 交通工具，车辆
481. applause n. 鼓掌，掌声
482. appliance n. 器具，器械
483. consent n. 准许，同意 vi (to) 准许，同意
484. conquer vt. 征服
485. defect n. 缺点，缺陷
486. delicate a. 易碎的；娇弱的；精美的
487. evolve v.演变
488. evolution n. 演变，进化
489. frown v. /n. 皱眉
490. frustrate vt. 使沮丧
491. guarantee vt. /n. 保证
492. guilty a. 内疚的；有罪的
493. jealous a. 妒忌的
494. jeans n. 牛仔裤
495. liquor n. 酒，烈性酒
496. liter/litre n. 升
497. modest a. 谦虚道
498. molecule n. 分子
499. orbit n. 轨道 v. (绕…)作轨道运行
500. participate v. (in) 参与，参加
501. particle n. 微粒
502. particularly ad. 特别，尤其
503. respond vi. 回答，答复；反应
504. response n. 回答，答复；反应
505. sensible a. 明智的
506. sensitive a. 敏感到，灵敏的
507. tremble vi. 颤抖
508. tremendous a. 巨大的；精彩的
509. trend n. 趋向，倾向
510. trial n. 审讯；试验
511. apparent a. 显然的，明白的
512. appetite n. 胃口；欲望
513. deposit n. 存款，定金 v.存放，储蓄
514. deputy n. 副职，代表
515. derive vt. 取得，得到；(from)起源于
516. descend v. 下来，下降
517. missile n. 导弹
518. mission n. 使命；代表团
519. mist n.薄雾
520. noticeable a. 显而易见到
521. notify vt. 通知，告知
522. notion n. 概念；意图，想法
523. resemble vt. 像，类似于
524. reveal vt. 揭露
525. revenue n. 收入，岁入；税收
526. shelter n. 掩蔽处；住所
527. shield n. 防护物，盾 vt. 保护，防护
528. vital a. 重要的；致命的，生命的
529. vitally ad. 极度，非常；致命地
530. urban a. 城市的
531. urge vt. 鼓励，激励
532. urgent a. 急迫的，紧急得
533. usage n. 使用，用法
534. violence n. 强力，暴力
535. violent a. 强暴的
536. violet a. 紫色的
537. weed n. 杂草，野草
538. welfare n. 福利
539. whatsoever ad. (用于否定句)任何
540. whereas conj. 然而，但是，尽管
541. essential a. 必不可少的；本质的
542. estimate n. /vt. 估计，估量
543. evaluate vt. 评估，评价
544. exceed vt. 超过，越出
545. exceedingly ad. 非常，极其
546. exclaim v. 呼喊，大声说
547. exclude vt. 把…排斥在外，不包括
548. exclusive a. 读有的，排他的
549. excursion n. 远足
550. flash vi. 闪光，闪耀
551. flee vi. 逃走
552. flexible a. 易弯曲的
553. flock n. 羊群，(鸟兽等)一群；一伙人
554. hardware n. 五金器具
555. harmony n. 和谐，融洽
556. haste n. 急速，急忙
557. hatred n. 憎恶，憎恨
558. incident n. 事件，事变
559. index n. 索引，标志
560. infant n. 婴儿
561. infect v. 传染
562. inferior a. 劣等的，次的，下级的
563. infinite a. 无限的
564. ingredient n. 组成部分
565. inhabitant n. 居民
566. jail n. 监狱
567. jam n. 果酱；拥挤，堵塞
568. jewel n. 宝石
569. joint a.连接的；共同的
570. junior a. 年少的；资历较浅的
571. laser n. 激光
572. launch vt. 发动，发起
573. luxury n. 奢侈；奢侈品
574. magnet n. 磁铁，磁体
575. male a. 男性的，雄的
576. female a. 女性的，雌的
577. manual a. 用手的，手工做的 n. 手册
578. manufacture vt. /n. 制造，加工
579. marine a. 海的；海产的
580. mutual a. 相互的
581. naked a. 裸露的
582. negative a. 否定的，消极的
583. neglect vt. 忽视，忽略
584. origin n.起源
`;

        // DOM元素
        const wordDisplay = document.getElementById('word-display');
        const meaningDisplay = document.getElementById('meaning-display');
        const pronunciationDisplay = document.getElementById('pronunciation');
        const exampleSentenceDisplay = document.getElementById('example-sentence');
        const optionsContainer = document.getElementById('options');
        const showAnswerBtn = document.getElementById('show-answer-btn');
        const nextWordBtn = document.getElementById('next-word-btn');
        const correctCountDisplay = document.getElementById('correct-count');
        const wrongCountDisplay = document.getElementById('wrong-count');
        const streakCountDisplay = document.getElementById('streak-count');
        const progressPercentDisplay = document.getElementById('progress-percent');
        const progressBar = document.getElementById('progress-bar');
        const timerDisplay = document.getElementById('timer');
        const reviewList = document.getElementById('review-list');
        const wordTextarea = document.getElementById('word-textarea');
        const importBtn = document.getElementById('import-btn');
        const loadBuiltinBtn = document.getElementById('load-builtin-btn');
        const importResult = document.getElementById('import-result');
        const themeToggle = document.getElementById('theme-toggle');

        // 初始化
        function init() {
            // 设置事件监听器
            showAnswerBtn.addEventListener('click', showAnswer);
            nextWordBtn.addEventListener('click', nextWord);
            importBtn.addEventListener('click', importWords);
            loadBuiltinBtn.addEventListener('click', loadBuiltinWords);
            themeToggle.addEventListener('click', toggleTheme);

            // 设置标签切换
            document.querySelectorAll('.tab').forEach(tab => {
                tab.addEventListener('click', () => {
                    document.querySelectorAll('.tab').forEach(t => t.classList.remove('active'));
                    document.querySelectorAll('.tab-content').forEach(c => c.classList.remove('active'));

                    tab.classList.add('active');
                    const tabId = tab.getAttribute('data-tab') + '-tab';
                    document.getElementById(tabId).classList.add('active');
                });
            });

            // 设置难度按钮
            document.querySelectorAll('.difficulty-btn').forEach(btn => {
                btn.addEventListener('click', () => {
                    document.querySelectorAll('.difficulty-btn').forEach(b => b.classList.remove('active-difficulty'));
                    btn.classList.add('active-difficulty');
                    currentDifficulty = btn.getAttribute('data-difficulty');
                    nextWord();
                });
            });

            // 初始化设置
            document.getElementById('mode-select').addEventListener('change', updateSettings);
            document.getElementById('timer-toggle').addEventListener('change', updateSettings);
            document.getElementById('example-toggle').addEventListener('change', updateSettings);
            document.getElementById('pronunciation-toggle').addEventListener('change', updateSettings);

            // 尝试从本地存储加载数据
            loadFromLocalStorage();

            // 如果没有单词数据，自动加载内置词汇
            if (wordList.length === 0) {
                loadBuiltinWords();
            } else {
                nextWord();
            }
        }

        // 加载内置词汇
        function loadBuiltinWords() {
            wordTextarea.value = builtInWords;
            importWords();
            importResult.textContent = "已加载内置CET-6高频词汇";
            importResult.classList.remove('hidden');
        }

        // 解析单词文本
        function parseWordList(text) {
            const lines = text.split('\n');
            const parsedWords = [];

            for (const line of lines) {
                // 移除行号（如 "1. "）
                const cleanedLine = line.replace(/^\d+\.\s*/, '').trim();
                if (!cleanedLine) continue;

                // 分割单词和释义
                const firstSpaceIndex = cleanedLine.indexOf(' ');
                if (firstSpaceIndex === -1) continue;

                const wordPart = cleanedLine.substring(0, firstSpaceIndex).trim();
                const meaningPart = cleanedLine.substring(firstSpaceIndex + 1).trim();

                // 从单词部分提取单词和词性
                const wordMatch = wordPart.match(/^([a-zA-Z]+)\s*([a-z\.]*)$/);
                if (!wordMatch) continue;

                const word = wordMatch[1];
                const pos = wordMatch[2] || '';

                // 创建单词对象
                const wordObj = {
                    word: word,
                    meaning: meaningPart,
                    pronunciation: "", // 用户提供的格式中没有音标
                    example: "", // 用户提供的格式中没有例句
                    difficulty: getRandomDifficulty() // 随机分配难度
                };

                parsedWords.push(wordObj);
            }

            return parsedWords;
        }

        // 随机分配难度
        function getRandomDifficulty() {
            const difficulties = ['easy', 'medium', 'hard'];
            return difficulties[Math.floor(Math.random() * difficulties.length)];
        }

        // 导入单词
        function importWords() {
            const text = wordTextarea.value.trim();
            if (!text) {
                importResult.textContent = "请输入单词列表";
                importResult.classList.remove('hidden');
                return;
            }

            const parsedWords = parseWordList(text);
            if (parsedWords.length === 0) {
                importResult.textContent = "无法解析单词列表，请检查格式";
                importResult.classList.remove('hidden');
                return;
            }

            wordList = parsedWords;
            importResult.textContent = `成功导入 ${wordList.length} 个单词`;
            importResult.classList.remove('hidden');

            // 重置学习状态
            resetLearningState();

            // 保存到本地存储
            saveToLocalStorage();

            // 切换到游戏标签
            document.querySelector('.tab[data-tab="game"]').click();

            // 开始学习
            nextWord();
        }

        // 重置学习状态
        function resetLearningState() {
            currentWord = null;
            correctCount = 0;
            wrongCount = 0;
            streakCount = 0;
            seenWords = new Set();
            updateStats();
            stopTimer();
        }

        // 获取下一个单词
        function nextWord() {
            // 隐藏答案相关元素
            meaningDisplay.classList.add('hidden');
            pronunciationDisplay.classList.add('hidden');
            exampleSentenceDisplay.classList.add('hidden');
            optionsContainer.classList.add('hidden');

            // 过滤单词基于难度
            let filteredWords = wordList;
            if (currentDifficulty !== 'all') {
                filteredWords = wordList.filter(word => word.difficulty === currentDifficulty);
            }

            // 如果没有单词，显示提示
            if (filteredWords.length === 0) {
                wordDisplay.textContent = "没有可用的单词";
                return;
            }

            // 如果已经看过所有单词，重置seenWords
            if (seenWords.size >= filteredWords.length) {
                seenWords = new Set();
            }

            // 选择一个未看过的单词
            let availableWords = filteredWords.filter((_, index) => !seenWords.has(index));
            if (availableWords.length === 0) {
                // 这种情况理论上不应该发生，但为了安全起见
                availableWords = filteredWords;
                seenWords = new Set();
            }

            const randomIndex = Math.floor(Math.random() * availableWords.length);
            currentWord = availableWords[randomIndex];
            const originalIndex = wordList.indexOf(currentWord);
            seenWords.add(originalIndex);

            // 显示单词
            wordDisplay.textContent = currentWord.word;

            // 如果是选择题模式，生成选项
            if (document.getElementById('mode-select').value === 'choice') {
                generateOptions();
            }

            // 更新进度
            updateProgress();

            // 重置计时器
            stopTimer();
            startTimer();
        }

        // 生成选择题选项
        function generateOptions() {
            optionsContainer.innerHTML = '';

            // 获取3个错误选项
            let otherWords = wordList.filter(word => word.word !== currentWord.word);
            otherWords = shuffleArray(otherWords).slice(0, 3);

            // 合并选项并打乱顺序
            let options = [currentWord, ...otherWords];
            options = shuffleArray(options);

            // 创建选项按钮
            options.forEach(option => {
                const button = document.createElement('button');
                button.className = 'option-btn';
                button.textContent = option.meaning;
                button.addEventListener('click', () => checkAnswer(option === currentWord));
                optionsContainer.appendChild(button);
            });

            optionsContainer.classList.remove('hidden');
        }

        // 检查答案
        function checkAnswer(isCorrect) {
            if (isCorrect) {
                correctCount++;
                streakCount++;
                if (streakCount > maxStreak) {
                    maxStreak = streakCount;
                }

                // 显示正确反馈
                document.querySelectorAll('.option-btn').forEach(btn => {
                    if (btn.textContent === currentWord.meaning) {
                        btn.classList.add('correct');
                    }
                });

                // 庆祝效果
                if (streakCount % 5 === 0) {
                    createConfetti();
                }
            } else {
                wrongCount++;
                streakCount = 0;

                // 显示错误反馈
                document.querySelectorAll('.option-btn').forEach(btn => {
                    if (btn.textContent === currentWord.meaning) {
                        btn.classList.add('correct');
                    } else {
                        btn.classList.add('wrong');
                    }
                });
            }

            updateStats();
            showAnswer();

            // 禁用所有选项按钮
            document.querySelectorAll('.option-btn').forEach(btn => {
                btn.disabled = true;
            });
        }

        // 显示答案
        function showAnswer() {
            meaningDisplay.textContent = currentWord.meaning;
            pronunciationDisplay.textContent = currentWord.pronunciation || "暂无音标";
            exampleSentenceDisplay.textContent = currentWord.example || "暂无例句";

            // 根据设置显示/隐藏元素
            const showPronunciation = document.getElementById('pronunciation-toggle').value === 'on';
            const showExample = document.getElementById('example-toggle').value === 'on';

            meaningDisplay.classList.remove('hidden');
            if (showPronunciation && currentWord.pronunciation) {
                pronunciationDisplay.classList.remove('hidden');
            }
            if (showExample && currentWord.example) {
                exampleSentenceDisplay.classList.remove('hidden');
            }
        }

        // 更新统计信息
        function updateStats() {
            correctCountDisplay.textContent = correctCount;
            wrongCountDisplay.textContent = wrongCount;
            streakCountDisplay.textContent = streakCount;
        }

        // 更新进度
        function updateProgress() {
            const totalWords = wordList.length;
            const seen = seenWords.size;
            const percent = totalWords > 0 ? Math.round((seen / totalWords) * 100) : 0;

            progressPercentDisplay.textContent = `${percent}%`;
            progressBar.style.width = `${percent}%`;
        }

        // 开始计时器
        function startTimer() {
            startTime = Date.now();
            stopTimer();

            timerInterval = setInterval(() => {
                const elapsed = Math.floor((Date.now() - startTime) / 1000);
                timerDisplay.textContent = `时间: ${elapsed}秒`;
            }, 1000);
        }

        // 停止计时器
        function stopTimer() {
            if (timerInterval) {
                clearInterval(timerInterval);
                timerInterval = null;
            }
        }

        // 更新设置
        function updateSettings() {
            const showTimer = document.getElementById('timer-toggle').value === 'on';
            timerDisplay.style.display = showTimer ? 'block' : 'none';
        }

        // 创建庆祝彩花效果
        function createConfetti() {
            for (let i = 0; i < 50; i++) {
                const confetti = document.createElement('div');
                confetti.className = 'confetti';
                confetti.style.left = `${Math.random() * 100}vw`;
                confetti.style.backgroundColor = `hsl(${Math.random() * 360}, 100%, 50%)`;
                confetti.style.transform = `rotate(${Math.random() * 360}deg)`;
                document.body.appendChild(confetti);

                // 动画
                const animation = confetti.animate([
                    { top: '0', opacity: 1 },
                    { top: '100vh', opacity: 0 }
                ], {
                    duration: 1000 + Math.random() * 2000,
                    easing: 'cubic-bezier(0.1, 0.8, 0.9, 1)'
                });

                animation.onfinish = () => confetti.remove();
            }
        }

        // 切换主题
        function toggleTheme() {
            isDarkMode = !isDarkMode;
            document.body.classList.toggle('dark-mode', isDarkMode);
            themeToggle.textContent = isDarkMode ? '🌞' : '🌓';
            saveToLocalStorage();
        }

        // 保存到本地存储
        function saveToLocalStorage() {
            const data = {
                wordList: wordList,
                correctCount: correctCount,
                wrongCount: wrongCount,
                streakCount: streakCount,
                maxStreak: maxStreak,
                seenWords: Array.from(seenWords),
                isDarkMode: isDarkMode,
                wordText: wordTextarea.value
            };

            localStorage.setItem('vocabularyAppData', JSON.stringify(data));
        }

        // 从本地存储加载
        function loadFromLocalStorage() {
            const savedData = localStorage.getItem('vocabularyAppData');
            if (!savedData) return;

            try {
                const data = JSON.parse(savedData);

                if (data.wordList) wordList = data.wordList;
                if (data.correctCount) correctCount = data.correctCount;
                if (data.wrongCount) wrongCount = data.wrongCount;
                if (data.streakCount) streakCount = data.streakCount;
                if (data.maxStreak) maxStreak = data.maxStreak;
                if (data.seenWords) seenWords = new Set(data.seenWords);
                if (data.isDarkMode) {
                    isDarkMode = data.isDarkMode;
                    document.body.classList.toggle('dark-mode', isDarkMode);
                    themeToggle.textContent = isDarkMode ? '🌞' : '🌓';
                }
                if (data.wordText) wordTextarea.value = data.wordText;

                updateStats();
                updateProgress();
            } catch (e) {
                console.error("加载保存的数据失败:", e);
            }
        }

        // 辅助函数：打乱数组
        function shuffleArray(array) {
            const newArray = [...array];
            for (let i = newArray.length - 1; i > 0; i--) {
                const j = Math.floor(Math.random() * (i + 1));
                [newArray[i], newArray[j]] = [newArray[j], newArray[i]];
            }
            return newArray;
        }

        // 初始化应用
        init();
    </script>
</body>
</html>