{"cells": [{"cell_type": "code", "id": "initial_id", "metadata": {"collapsed": true}, "source": ["import requests\n", "import pandas as pd\n", "from tqdm import tqdm  # 进度条库\n", "\n", "# 设置请求的 URL\n", "url = \"https://www.gdrc.com/data-service/job/getJobsByJobfairId\"\n", "\n", "# 请求头\n", "headers = {\n", "    \"Content-Type\": \"application/json\",\n", "    \"Referer\": \"https://www.gdrc.com\",\n", "    \"User-Agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36\",\n", "}\n", "\n", "# 查询参数\n", "params = {\n", "    \"citycategory\": \"%E5%B9%BF%E4%B8%9C\"  # URL 编码的\"广东\"\n", "}\n", "\n", "# 设置页数范围\n", "start_page = 200  # 你可以更改为需要的起始页\n", "end_page = 1000    # 你可以更改为需要的结束页\n", "\n", "# 存储所有爬取的职位数据\n", "all_jobs = []\n", "\n", "# 遍历页数进行爬取，使用 tqdm 添加进度条\n", "for page in tqdm(range(start_page, end_page + 1), desc=\"爬取进度\", unit=\"页\"):\n", "    # 请求体（POST 请求的 body）\n", "    payload = {\n", "        \"pages\": page,\n", "        \"pageSize\": 30,  # 每页30条数据\n", "        \"parameter\": {\n", "            \"id\": 149,\n", "            \"districtCn\": \"广东\"\n", "        }\n", "    }\n", "\n", "    # 发送 POST 请求\n", "    response = requests.post(url, headers=headers, params=params, json=payload)\n", "\n", "    # 检查请求是否成功\n", "    if response.status_code == 200:\n", "        data = response.json()\n", "        \n", "        # 提取职位数据\n", "        if \"data\" in data and isinstance(data[\"data\"], list):\n", "            for job in data[\"data\"]:\n", "                all_jobs.append({\n", "                    \"职位名称\": job.get(\"jobsName\", \"\"),\n", "                    \"公司名称\": job.get(\"companyname\", \"\"),\n", "                    \"职位类别\": job.get(\"categoryCn\", \"\"),\n", "                    \"行业\": job.get(\"tradeCn\", \"\"),\n", "                    \"地区\": job.get(\"districtCn\", \"\"),\n", "                    \"职位标签\": job.get(\"tagCn\", \"\"),\n", "                    \"学历要求\": job.get(\"educationCn\", \"\"),\n", "                    \"经验要求\": job.get(\"experienceCn\", \"\"),\n", "                    \"最低薪资\": job.get(\"minwage\", \"\"),\n", "                    \"最高薪资\": job.get(\"maxwage\", \"\"),\n", "                    \"职位描述\": job.get(\"contents\", \"\"),\n", "                })\n", "    else:\n", "        print(f\"请求失败，页码 {page}，状态码: {response.status_code}\")\n", "\n", "# 将数据转换为 DataFrame\n", "df = pd.DataFrame(all_jobs)\n", "\n", "# 保存为 Excel 文件\n", "excel_path = \"jobs_data1.xlsx\"\n", "df.to_excel(excel_path, index=False)\n", "\n", "# 输出完成信息\n", "print(f\"数据已保存为 {excel_path}\")\n"], "outputs": [], "execution_count": null}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.6"}}, "nbformat": 4, "nbformat_minor": 5}