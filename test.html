<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>单词记忆大师 - CET-6高频词汇</title>
    <style>
        :root {
            --primary: #4361ee;
            --secondary: #3f37c9;
            --accent: #4895ef;
            --light: #f8f9fa;
            --dark: #212529;
            --success: #4cc9f0;
            --danger: #f72585;
            --warning: #f8961e;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        body {
            background-color: #f5f7fa;
            color: var(--dark);
            line-height: 1.6;
        }

        .container {
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
        }

        header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px 0;
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            color: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        h1 {
            font-size: 2.2rem;
            margin-bottom: 10px;
        }

        .subtitle {
            font-size: 1rem;
            opacity: 0.9;
        }

        .main-content {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .card {
            background: white;
            border-radius: 10px;
            padding: 25px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }

        .word-display {
            text-align: center;
            margin-bottom: 20px;
            min-height: 200px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }

        .word {
            font-size: 2.5rem;
            font-weight: bold;
            color: var(--primary);
            margin-bottom: 10px;
        }

        .pronunciation {
            font-size: 1.2rem;
            color: var(--accent);
            margin-bottom: 15px;
        }

        .definition {
            font-size: 1.1rem;
            line-height: 1.8;
            max-width: 80%;
        }

        .example {
            font-style: italic;
            color: #6c757d;
            margin-top: 10px;
            border-left: 3px solid var(--accent);
            padding-left: 15px;
        }

        .controls {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-top: 20px;
        }

        button {
            padding: 12px 25px;
            border: none;
            border-radius: 50px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background-color: var(--primary);
            color: white;
        }

        .btn-primary:hover {
            background-color: var(--secondary);
            transform: translateY(-2px);
        }

        .btn-secondary {
            background-color: var(--light);
            color: var(--dark);
            border: 1px solid #dee2e6;
        }

        .btn-secondary:hover {
            background-color: #e9ecef;
            transform: translateY(-2px);
        }

        .btn-success {
            background-color: var(--success);
            color: white;
        }

        .btn-success:hover {
            opacity: 0.9;
            transform: translateY(-2px);
        }

        .btn-danger {
            background-color: var(--danger);
            color: white;
        }

        .btn-danger:hover {
            opacity: 0.9;
            transform: translateY(-2px);
        }

        .progress-container {
            margin-top: 30px;
            background-color: #e9ecef;
            border-radius: 10px;
            height: 10px;
            overflow: hidden;
        }

        .progress-bar {
            height: 100%;
            background: linear-gradient(90deg, var(--primary), var(--accent));
            width: 0%;
            transition: width 0.3s ease;
        }

        .stats {
            display: flex;
            justify-content: space-between;
            margin-top: 10px;
            font-size: 0.9rem;
            color: #6c757d;
        }

        .upload-section {
            margin-top: 30px;
        }

        .file-upload {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .file-upload label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
        }

        .file-upload input[type="file"] {
            display: none;
        }

        .custom-file-upload {
            border: 2px dashed #ced4da;
            border-radius: 5px;
            padding: 15px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .custom-file-upload:hover {
            border-color: var(--primary);
            background-color: rgba(67, 97, 238, 0.05);
        }

        .file-name {
            margin-top: 10px;
            font-size: 0.9rem;
            color: #6c757d;
        }

        .hidden {
            display: none;
        }

        .flip-card {
            perspective: 1000px;
            width: 100%;
        }

        .flip-card-inner {
            position: relative;
            width: 100%;
            height: 100%;
            transition: transform 0.6s;
            transform-style: preserve-3d;
        }

        .flip-card.flipped .flip-card-inner {
            transform: rotateY(180deg);
        }

        .flip-card-front, .flip-card-back {
            position: absolute;
            width: 100%;
            height: 100%;
            backface-visibility: hidden;
        }

        .flip-card-back {
            transform: rotateY(180deg);
        }

        .flip-btn {
            background-color: var(--warning);
            color: white;
        }

        .flip-btn:hover {
            opacity: 0.9;
            transform: translateY(-2px);
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }

            .word {
                font-size: 2rem;
            }

            .controls {
                flex-wrap: wrap;
            }

            button {
                padding: 10px 15px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>单词记忆大师</h1>
            <p class="subtitle">CET-6高频词汇记忆工具</p>
        </header>

        <div class="main-content">
            <div class="card">
                <div class="flip-card" id="flipCard">
                    <div class="flip-card-inner">
                        <div class="flip-card-front">
                            <div class="word-display">
                                <div class="word" id="currentWord">欢迎使用</div>
                                <div class="pronunciation" id="pronunciation">/wel'kʌm/</div>
                                <div class="definition" id="definition">点击"开始"按钮开始记忆单词</div>
                                <div class="example" id="example">Welcome to our word memorization tool!</div>
                            </div>
                        </div>
                        <div class="flip-card-back">
                            <div class="word-display">
                                <div class="word" id="currentWordBack">欢迎使用</div>
                                <div class="pronunciation" id="pronunciationBack">/wel'kʌm/</div>
                                <div class="definition" id="definitionBack">点击"开始"按钮开始记忆单词</div>
                                <div class="example" id="exampleBack">Welcome to our word memorization tool!</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="progress-container">
                    <div class="progress-bar" id="progressBar"></div>
                </div>
                <div class="stats">
                    <span id="progressText">0/0</span>
                    <span id="memoryRate">记忆率: 0%</span>
                </div>

                <div class="controls">
                    <button class="btn-primary" id="startBtn">开始</button>
                    <button class="btn-secondary" id="prevBtn" disabled>上一个</button>
                    <button class="flip-btn" id="flipBtn">翻转</button>
                    <button class="btn-success" id="knowBtn" disabled>认识</button>
                    <button class="btn-danger" id="dontKnowBtn" disabled>不认识</button>
                    <button class="btn-secondary" id="nextBtn" disabled>下一个</button>
                </div>
            </div>

            <div class="card upload-section">
                <h2>上传自定义单词表</h2>
                <p>支持格式：1. alter v. 改变，改动，变更</p>
                <div class="file-upload">
                    <label for="wordFile">选择单词文件：</label>
                    <input type="file" id="wordFile" accept=".txt,.text">
                    <label for="wordFile" class="custom-file-upload">
                        <i class="fas fa-cloud-upload-alt"></i> 点击上传文件
                    </label>
                    <div class="file-name" id="fileName">未选择文件</div>
                    <button class="btn-primary" id="loadBtn" disabled>加载单词</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 内置CET-6高频词汇
        const builtInWords = [
            { word: "alter", pronunciation: "/ˈɔːltər/", definition: "v. 改变，改动，变更", example: "The design must be altered to meet the new requirements." },
            { word: "burst", pronunciation: "/bɜːrst/", definition: "vi. n. 突然发生，爆裂", example: "The balloon burst with a loud bang." },
            { word: "dispose", pronunciation: "/dɪˈspəʊz/", definition: "vi. 除掉；处置；解决；处理(of)", example: "How did they dispose of the body?" },
            { word: "blast", pronunciation: "/blɑːst/", definition: "n. 爆炸；气流 vi. 炸，炸掉", example: "A blast of cold air hit him as he opened the window." },
            { word: "consume", pronunciation: "/kənˈsjuːm/", definition: "v. 消耗，耗尽", example: "The fire quickly consumed the wooden house." },
            { word: "split", pronunciation: "/splɪt/", definition: "v. 劈开；割裂；分裂 a.裂开的", example: "The board split in two when I hammered it." },
            { word: "spit", pronunciation: "/spɪt/", definition: "v. 吐(唾液等)；唾弃", example: "It's rude to spit in public." },
            { word: "abolish", pronunciation: "/əˈbɒlɪʃ/", definition: "v. 废除，取消", example: "Slavery was abolished in the US in the 19th century." },
            { word: "abrupt", pronunciation: "/əˈbrʌpt/", definition: "a. 突然的，意外的；唐突的", example: "The meeting came to an abrupt end when the chairman walked out." },
            { word: "absurd", pronunciation: "/əbˈsɜːd/", definition: "a. 荒谬的，荒唐的", example: "It's absurd to expect a two-year-old to sit still for an hour." },
            { word: "abundance", pronunciation: "/əˈbʌndəns/", definition: "n. 大量，丰富，充足", example: "The country has an abundance of natural resources." },
            { word: "accommodate", pronunciation: "/əˈkɒmədeɪt/", definition: "v. 容纳；使适应；向...提供住处", example: "The hotel can accommodate up to 500 guests." },
            { word: "addict", pronunciation: "/ˈædɪkt/", definition: "n. 有瘾的人；入迷的人", example: "He's a drug addict who needs help." },
            { word: "adhere", pronunciation: "/ədˈhɪər/", definition: "vi. 粘附；遵守；坚持", example: "The stamp didn't adhere to the envelope." },
            { word: "adjacent", pronunciation: "/əˈdʒeɪsnt/", definition: "a. 邻近的，毗连的", example: "They live in a house adjacent to the railway station." },
            { word: "adverse", pronunciation: "/ˈædvɜːrs/", definition: "a. 不利的，有害的", example: "The drug has no adverse side effects." },
            { word: "aesthetic", pronunciation: "/iːsˈθetɪk/", definition: "a. 美学的，审美的；悦目的", example: "The building is functional but lacks aesthetic appeal." },
            { word: "affiliate", pronunciation: "/əˈfɪlieɪt/", definition: "v. 使隶属(或附属)于 n. 附属机构", example: "The hospital is affiliated with the medical school." }
        ];

        let words = [...builtInWords];
        let currentIndex = 0;
        let knownWords = new Set();
        let isFlipped = false;

        // DOM元素
        const currentWordEl = document.getElementById('currentWord');
        const pronunciationEl = document.getElementById('pronunciation');
        const definitionEl = document.getElementById('definition');
        const exampleEl = document.getElementById('example');
        const currentWordBackEl = document.getElementById('currentWordBack');
        const pronunciationBackEl = document.getElementById('pronunciationBack');
        const definitionBackEl = document.getElementById('definitionBack');
        const exampleBackEl = document.getElementById('exampleBack');
        const progressBarEl = document.getElementById('progressBar');
        const progressTextEl = document.getElementById('progressText');
        const memoryRateEl = document.getElementById('memoryRate');
        const startBtn = document.getElementById('startBtn');
        const prevBtn = document.getElementById('prevBtn');
        const nextBtn = document.getElementById('nextBtn');
        const knowBtn = document.getElementById('knowBtn');
        const dontKnowBtn = document.getElementById('dontKnowBtn');
        const flipBtn = document.getElementById('flipBtn');
        const wordFileEl = document.getElementById('wordFile');
        const fileNameEl = document.getElementById('fileName');
        const loadBtn = document.getElementById('loadBtn');
        const flipCard = document.getElementById('flipCard');

        // 初始化
        function init() {
            updateProgress();
            bindEvents();
        }

        // 绑定事件
        function bindEvents() {
            startBtn.addEventListener('click', startLearning);
            prevBtn.addEventListener('click', showPrevWord);
            nextBtn.addEventListener('click', showNextWord);
            knowBtn.addEventListener('click', () => markWord(true));
            dontKnowBtn.addEventListener('click', () => markWord(false));
            flipBtn.addEventListener('click', flipCardHandler);
            wordFileEl.addEventListener('change', handleFileSelect);
            loadBtn.addEventListener('click', loadCustomWords);
        }

        // 开始学习
        function startLearning() {
            if (words.length === 0) {
                alert('请先加载单词！');
                return;
            }

            startBtn.disabled = true;
            prevBtn.disabled = false;
            nextBtn.disabled = false;
            knowBtn.disabled = false;
            dontKnowBtn.disabled = false;

            currentIndex = 0;
            knownWords.clear();
            updateWordDisplay();
            updateProgress();
        }

        // 显示上一个单词
        function showPrevWord() {
            if (currentIndex > 0) {
                currentIndex--;
                resetCard();
                updateWordDisplay();
            }
        }

        // 显示下一个单词
        function showNextWord() {
            if (currentIndex < words.length - 1) {
                currentIndex++;
                resetCard();
                updateWordDisplay();
            }
        }

        // 标记单词认识/不认识
        function markWord(isKnown) {
            const currentWord = words[currentIndex].word;
            if (isKnown) {
                knownWords.add(currentWord);
            } else {
                knownWords.delete(currentWord);
            }
            updateProgress();

            // 自动翻回正面
            if (isFlipped) {
                flipCardHandler();
            }

            // 自动跳到下一个单词
            if (currentIndex < words.length - 1) {
                setTimeout(() => {
                    currentIndex++;
                    resetCard();
                    updateWordDisplay();
                }, 500);
            }
        }

        // 翻转卡片
        function flipCardHandler() {
            isFlipped = !isFlipped;
            if (isFlipped) {
                flipCard.classList.add('flipped');
            } else {
                flipCard.classList.remove('flipped');
            }
        }

        // 重置卡片状态
        function resetCard() {
            if (isFlipped) {
                flipCardHandler();
            }
        }

        // 更新单词显示
        function updateWordDisplay() {
            const currentWord = words[currentIndex];
            currentWordEl.textContent = currentWord.word;
            pronunciationEl.textContent = currentWord.pronunciation;
            definitionEl.textContent = currentWord.definition;
            exampleEl.textContent = currentWord.example;

            currentWordBackEl.textContent = currentWord.word;
            pronunciationBackEl.textContent = currentWord.pronunciation;
            definitionBackEl.textContent = currentWord.definition;
            exampleBackEl.textContent = currentWord.example;
        }

        // 更新进度
        function updateProgress() {
            const progress = ((currentIndex + 1) / words.length) * 100;
            progressBarEl.style.width = `${progress}%`;
            progressTextEl.textContent = `${currentIndex + 1}/${words.length}`;

            const memoryRate = words.length > 0 ? Math.round((knownWords.size / words.length) * 100) : 0;
            memoryRateEl.textContent = `记忆率: ${memoryRate}%`;
        }

        // 处理文件选择
        function handleFileSelect(e) {
            const file = e.target.files[0];
            if (file) {
                fileNameEl.textContent = file.name;
                loadBtn.disabled = false;
            } else {
                fileNameEl.textContent = '未选择文件';
                loadBtn.disabled = true;
            }
        }

        // 加载自定义单词
        function loadCustomWords() {
            const file = wordFileEl.files[0];
            if (!file) return;

            const reader = new FileReader();
            reader.onload = function(e) {
                const content = e.target.result;
                const lines = content.split('\n');
                const newWords = [];

                lines.forEach(line => {
                    line = line.trim();
                    if (line) {
                        // 解析格式：1. alter v. 改变，改动，变更
                        const match = line.match(/^\d+\.\s+(\w+)\s+([^\.]+)\.?\s*(.*)/);
                        if (match) {
                            const word = match[1].toLowerCase();
                            const posAndDef = match[2].trim();
                            const example = match[3].trim();

                            // 简单生成音标（实际应用中可能需要字典API）
                            const pronunciation = generatePronunciation(word);

                            newWords.push({
                                word: word,
                                pronunciation: pronunciation,
                                definition: posAndDef,
                                example: example || `Example sentence for ${word}.`
                            });
                        }
                    }
                });

                if (newWords.length > 0) {
                    words = newWords;
                    currentIndex = 0;
                    knownWords.clear();
                    updateWordDisplay();
                    updateProgress();
                    startBtn.disabled = false;
                    alert(`成功加载 ${newWords.length} 个单词！`);
                } else {
                    alert('未能解析单词文件，请检查格式是否正确。');
                }
            };
            reader.readAsText(file);
        }

        // 简单生成音标（模拟）
        function generatePronunciation(word) {
            const vowels = ['a', 'e', 'i', 'o', 'u'];
            let stressPos = 0;

            // 找到第一个元音作为重音位置
            for (let i = 0; i < word.length; i++) {
                if (vowels.includes(word[i])) {
                    stressPos = i;
                    break;
                }
            }

            // 简单音标生成规则
            let pronunciation = '/';
            for (let i = 0; i < word.length; i++) {
                if (i === stressPos) {
                    pronunciation += "'";
                }
                pronunciation += word[i];
            }
            pronunciation += '/';

            return pronunciation;
        }

        // 初始化应用
        init();
    </script>
</body>
</html>
