import os
from langchain_unstructured.document_loaders import UnstructuredLoader
from langchain_text_splitters import RecursiveCharacterTextSplitter
from langchain_huggingface import HuggingFaceEmbeddings
from langchain_community.vectorstores import FAISS
from langchain_community.llms import BaseLLM
from langchain_core.output_parsers import StrOutputParser
from langchain_core.runnables import RunnablePassthrough
from langchain_core.prompts import Chat<PERSON>romptTemplate
from langchain_core.outputs import LLMResult, Generation
from openai import OpenAI
import gradio as gr
import time

# 1. 环境设置
uploaded_dir = "uploaded_files"
if not os.path.exists(uploaded_dir):
    os.makedirs(uploaded_dir)  # 创建上传文件目录


# 2. 文件处理函数
def process_uploaded_files(files):
    docs = []
    for file in files:
        try:
            file_path = file.name  # 直接获取临时文件路径
            loader = UnstructuredLoader(file_path)
            doc = loader.load()
            docs.extend(doc)
            print(f"成功加载文件: {file_path}，文档数: {len(doc)}")  # 调试日志
        except Exception as e:
            print(f"加载文件 {file_path} 失败: {str(e)}")
    return docs


# 3. 文本分块
text_splitter = RecursiveCharacterTextSplitter(
    chunk_size=500,  # 每块最大字符数
    chunk_overlap=100  # 块之间的重叠字符数
)


def split_docs(docs):
    """
    将提取的文档分割成小块
    """
    all_splits = text_splitter.split_documents(docs)
    return all_splits


# 4. 嵌入与向量存储
embedding_model = HuggingFaceEmbeddings(model_name="BAAI/bge-base-zh-v1.5")  # 使用 BAAI 的中文嵌入模型


def create_vectorstore(splits):
    """
    将文本块转换为嵌入向量并存储到 FAISS 中
    """
    vectorstore = FAISS.from_documents(splits, embedding_model)
    return vectorstore


# 5. 自定义 LLM 类（使用百炼平台 Qwen 模型）
class QwenLLM(BaseLLM):
    def __init__(self):
        super().__init__()
        self.client = OpenAI(
            api_key=os.getenv("DASHSCOPE_API_KEY"),  # 支持环境变量或直接传入
            base_url="https://dashscope.aliyuncs.com/compatible-mode/v1"
        )
        self.model = "qwen-max"  # 可根据需要更换模型，例如 "qwen2.5"

    def _generate(self, prompts, stop=None, **kwargs):
        """
        实现抽象方法 _generate，调用百炼平台的 Qwen 模型生成回答
        """
        prompt = prompts[0]
        completion = self.client.chat.completions.create(
            model=self.model,
            messages=[
                {"role": "system", "content": "You are a helpful assistant."},
                {"role": "user", "content": prompt}
            ],
            max_tokens=1024,
            temperature=0.7
        )
        generation = Generation(text=completion.choices[0].message.content)
        return LLMResult(generations=[[generation]])

    @property
    def _llm_type(self):
        return "custom"  # LangChain 要求定义 LLM 类型


# 6. LCEL RAG 管道构建
def format_docs(docs):
    """
    将检索到的文档格式化为字符串
    """
    return "\n\n".join(doc.page_content for doc in docs)


def build_qa_chain(vectorstore, llm):
    """
    使用 LCEL 构建 RAG 问答链
    """
    # 定义提示模板（参考 rlm/rag-prompt）
    prompt_template = """你是一个有帮助的助手，根据以下上下文回答问题。如果不知道答案，就说不知道，不要胡编。
    上下文：{context}
    问题：{question}
    回答："""
    prompt = ChatPromptTemplate.from_template(prompt_template)

    # LCEL 链
    qa_chain = (
            {
                "context": vectorstore.as_retriever() | format_docs,
                "question": RunnablePassthrough()
            }
            | prompt
            | llm
            | StrOutputParser()
    )
    return qa_chain


# 7. Gradio 界面逻辑
def process_files_with_status(files):
    try:
        print("Starting to process files...")
        # 处理文件
        docs = process_uploaded_files(files)
        print("Documents loaded:", len(docs))

        # 文本分块
        splits = split_docs(docs)
        print("Text splits:", len(splits))

        # 创建向量存储
        vectorstore = create_vectorstore(splits)
        print("Vectorstore created successfully")

        # 返回结果和状态信息
        return vectorstore, True, "✅ 知识库构建成功！共处理 " + str(len(splits)) + " 个文本块"

    except Exception as e:
        error_msg = f"❌ 处理文件时出错: {str(e)}"
        print(error_msg)
        return None, False, error_msg


# 调整后的问答流函数
def answer_question_with_stream(question, vectorstore, chat_history):
    if not question.strip():
        yield chat_history, "❓ 请输入问题"
        return

    if vectorstore is None:
        yield chat_history, "⚠️ 请先上传文件"
        return

    try:
        # 初始化用户问题和空回答
        new_history = chat_history.copy()
        new_history.extend([
            {"role": "user", "content": question},
            {"role": "assistant", "content": ""}
        ])
        yield new_history, "🤔 思考中..."

        # 生成完整回答
        llm = QwenLLM()
        qa_chain = build_qa_chain(vectorstore, llm)
        full_answer = qa_chain.invoke(question)

        # 流式更新回答
        for i in range(len(full_answer)):
            new_history[-1]["content"] = full_answer[:i + 1]
            yield new_history, "💬 生成中..."
            time.sleep(0.03)

        yield new_history, "✅ 回答完成"

    except Exception as e:
        error_msg = f"❌ 错误: {str(e)}"
        new_history.append({"role": "assistant", "content": error_msg})
        yield new_history, "⚠️ 发生异常"


# 8. Gradio 前端界面（适配 5.20.0）
with gr.Blocks(title="RAG-LLM 知识库问答系统", theme=gr.themes.Soft()) as demo:
    gr.Markdown("# 📚 RAG-LLM 知识库问答系统")
    gr.Markdown("上传文档，构建知识库，然后提问问题，AI 将基于您的文档给出回答。")

    # 状态显示
    status_display = gr.Markdown("系统已准备就绪，请上传文件", elem_id="status_display")

    # 知识库状态
    vectorstore_state = gr.State(None)
    kb_ready_state = gr.State(False)

    with gr.Tabs():
        with gr.Tab("📄 知识库管理"):
            # 文件上传区域
            with gr.Row():
                file_upload = gr.File(
                    label="上传知识文件",
                    file_count="multiple",
                    file_types=[".txt", ".pdf", ".docx"],
                    height=150,
                    elem_id="file_upload"
                )

            with gr.Row():
                process_button = gr.Button("🔨 构建知识库", variant="primary", elem_id="process_btn", scale=1)
                clear_button = gr.Button("🗑️ 清除知识库", variant="secondary", elem_id="clear_btn", scale=1)

        with gr.Tab("💬 问答交互"):
            # 问答交互区域
            chat_history = gr.Chatbot(
                label="对话历史",
                height=400,
                elem_id="chat_history",
                show_copy_button=True,
                type="messages"
            )

            with gr.Row():
                question_input = gr.Textbox(
                    label="输入问题",
                    placeholder="请输入您的问题...",
                    elem_id="question_input",
                    autofocus=True,
                    scale=4
                )
                send_button = gr.Button("🚀 发送", variant="primary", elem_id="send_btn", scale=1)

    # 回答显示区域（现在用Chatbot替代，但保留这个用于存储当前回答）
    answer_box = gr.Textbox(visible=False)


    # 事件绑定
    def clear_knowledge_base():
        return None, False, "🗑️ 知识库已清除"


    # 将原有的列表式对话历史改为字典格式
    def add_message(question, answer, history):
        history.append({"role": "user", "content": question})
        history.append({"role": "assistant", "content": answer})
        return history, ""


    # 处理文件上传
    process_button.click(
        fn=process_files_with_status,
        inputs=[file_upload],  # 不再传递 status_display 作为输入
        outputs=[vectorstore_state, kb_ready_state, status_display]  # 状态信息通过返回值更新
    )

    # 清除知识库
    clear_button.click(
        fn=clear_knowledge_base,
        outputs=[vectorstore_state, kb_ready_state, status_display]
    )


    # 发送问题

    # 调整事件绑定
    send_button.click(
        fn=lambda q, h: (h + [{"role": "user", "content": q}], ""),
        inputs=[question_input, chat_history],
        outputs=[chat_history, question_input]
    ).then(
        fn=answer_question_with_stream,
        inputs=[question_input, vectorstore_state, chat_history],
        outputs=[chat_history, status_display]
    )

    # 修改回车键提交的事件链
    question_input.submit(
        fn=lambda q, h: (h + [{"role": "user", "content": q}], ""),
        inputs=[question_input, chat_history],
        outputs=[chat_history, question_input]
    ).then(
        fn=answer_question_with_stream,
        inputs=[question_input, vectorstore_state, chat_history],
        outputs=[chat_history, status_display]
    )
# 9. 启动应用
demo.launch(debug=True, share=True)


