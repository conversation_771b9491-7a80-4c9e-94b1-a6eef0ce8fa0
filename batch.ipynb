{"cells": [{"cell_type": "code", "id": "initial_id", "metadata": {"collapsed": true, "ExecuteTime": {"end_time": "2025-03-24T02:46:05.289410Z", "start_time": "2025-03-24T02:46:05.249944Z"}}, "source": ["import csv\n", "import json\n", "def messages_builder_example(content):\n", "    messages = [{\"role\": \"system\", \"content\": '''请根据提供的会议信息，按以下要求生成总结：\n", "亮点 ：\n", "需包含会议的权威性（如主办单位、技术支持单位、往届检索记录）、出版与检索保障（明确数据库名称，如EI、Scopus、IEEE Xplore）、嘉宾阵容（突出专家学术头衔及研究方向）、审稿与参会服务（审稿周期、翻译/查重支持、灵活参会形式）、主题前沿性（结合会议征稿关键词说明技术热点）。\n", "语言风格需吸引消费者 ：使用积极、简洁的表达，突出会议的独特优势（如“权威认证”“快速审稿”“顶尖学者面对面”“高影响力出版”），可适当使用感叹号或强调词增强吸引力。\n", "避免冗余信息（如不提及价格、评审专家个人背景）。\n", "会议主题 ：\n", "直接从会议征稿主题中提取关键词，用中文逗号分隔，无需分点或分类，保持与原文一致的术语。\n", "示例输出：\n", "亮点 ：2025年计算智能与机器人国际学术会议（CIR2025）由国家级权威高校广东第二师范学院、华南理工大学、陕西理工大学与海南大学联合主办，IEEE CIS广州分会技术支持，自创办以来吸引来自50多个国家的顶尖学者投稿并持续被EI核心、Scopus及IEEE Xplore稳定检索，学术影响力显著；会议论文集由IEEE正式出版（ISBN:979‑8‑3315‑2382‑4），议程包括国际知名专家主旨报告、专题论坛与海报展示，聚焦机器学习、计算机视觉、强化学习等前沿技术，与用户研究方向高度契合；审稿周期仅7天，支持在线投稿、查重与翻译服务，提供早鸟优惠、学生与团队折扣，推荐优秀论文至免版面费SCI期刊《Human‑Centric Intelligent Systems》并设最佳论文奖，助力快速发表与国际合作，打造高效、国际化的学术与产业交流平台！\n", "会议主题 ：[直接列出征稿主题关键词，用逗号分隔]。\n", "\n", "说明 ：\n", "\n", "若会议信息中未明确往届检索记录，可省略；若嘉宾无显著头衔（如IEEE Fellow），仅提及姓名及单位。\n", "会议主题需严格从“征稿主题”模块提取，保留原文术语，避免遗漏或改写。'''}, {\"role\": \"user\", \"content\": content}]\n", "    return messages\n", "\n", "with open(\"input_demo.csv\", \"r\",encoding='utf-8') as fin:\n", "    with open(\"input_demo.jsonl\", 'w', encoding='utf-8') as fout:\n", "        csvreader = csv.reader(fin)\n", "        for row in csvreader:\n", "            body = {\"model\": \"qwen-max\", \"messages\": messages_builder_example(row[1])}\n", "            # 选择Embedding文本向量模型进行调用时，url的值需填写\"/v1/embeddings\"\n", "            request = {\"custom_id\": row[0], \"method\": \"POST\", \"url\": \"/v1/chat/completions\", \"body\": body}\n", "            fout.write(json.dumps(request, separators=(',', ':'), ensure_ascii=False) + \"\\n\", )"], "outputs": [], "execution_count": 9}, {"metadata": {"ExecuteTime": {"end_time": "2025-03-24T06:29:51.478584Z", "start_time": "2025-03-24T06:29:51.384640Z"}}, "cell_type": "code", "source": ["import json\n", "import csv\n", "columns = [\"custom_id\",\n", "           \"model\",\n", "           \"request_id\",\n", "           \"status_code\",\n", "           \"error_code\",\n", "           \"error_message\",\n", "           \"created\",\n", "           \"content\",\n", "           \"usage\"]\n", "\n", "def dict_get_string(dict_obj, path):\n", "    obj = dict_obj\n", "    try:\n", "        for element in path:\n", "            obj = obj[element]\n", "        return obj\n", "    except:\n", "        return None\n", "\n", "with open(\"result.jsonl\", \"r\",encoding='utf-8') as fin:\n", "    with open(\"result1.csv\", 'w', encoding='utf-8') as fout:\n", "        rows = [columns]\n", "        for line in fin:\n", "            request_result = json.loads(line)\n", "            row = [dict_get_string(request_result, [\"custom_id\"]),\n", "                   dict_get_string(request_result, [\"response\", \"body\", \"model\"]),\n", "                   dict_get_string(request_result, [\"response\", \"request_id\"]),\n", "                   dict_get_string(request_result, [\"response\", \"status_code\"]),\n", "                   dict_get_string(request_result, [\"error\", \"error_code\"]),\n", "                   dict_get_string(request_result, [\"error\", \"error_message\"]),\n", "                   dict_get_string(request_result, [\"response\", \"body\", \"created\"]),\n", "                   dict_get_string(request_result, [\"response\", \"body\", \"choices\", 0, \"message\", \"content\"]),\n", "                   dict_get_string(request_result, [\"response\", \"body\", \"usage\"])]\n", "            rows.append(row)\n", "        writer = csv.writer(fout)\n", "        writer.writerows(rows)"], "id": "69b8ab81fb86714f", "outputs": [], "execution_count": 12}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.6"}}, "nbformat": 4, "nbformat_minor": 5}