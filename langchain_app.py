import os
from flask import Flask, request, jsonify
from werkzeug.utils import secure_filename

# ----------------------
# 引入你已有的 RAG 逻辑
# ----------------------
from langchain_unstructured.document_loaders import UnstructuredLoader
from langchain_text_splitters import RecursiveCharacterTextSplitter
from langchain_huggingface import HuggingFaceEmbeddings
from langchain_community.vectorstores import FAISS
from langchain_core.output_parsers import StrOutputParser
from langchain_core.runnables import RunnablePassthrough
from langchain_core.prompts import ChatPromptTemplate
from openai import OpenAI

# 这里放置你已有的 QwenLLM 定义、process_files, answer_question 等逻辑
# 这里只做简化示例

UPLOAD_FOLDER = "uploaded_files"
if not os.path.exists(UPLOAD_FOLDER):
    os.makedirs(UPLOAD_FOLDER)

app = Flask(__name__)
app.config["UPLOAD_FOLDER"] = UPLOAD_FOLDER

# --------------------------
# 保留你的向量库等全局对象
# --------------------------
vectorstore = None

# 自定义文本分割器
text_splitter = RecursiveCharacterTextSplitter(chunk_size=2000, chunk_overlap=200)
embedding_model = HuggingFaceEmbeddings(model_name="BAAI/bge-base-zh-v1.5")


class QwenLLM:
    def __init__(self):
        self.client = OpenAI(
            api_key=os.getenv("DASHSCOPE_API_KEY"),
            base_url="https://dashscope.aliyuncs.com/compatible-mode/v1"
        )
        self.model = "qwen-max"

    def __call__(self, prompt, **kwargs):
        completion = self.client.chat.completions.create(
            model=self.model,
            messages=[
                {"role": "system", "content": "You are a helpful assistant."},
                {"role": "user", "content": prompt}
            ],
            max_tokens=1024,
            temperature=0.7
        )
        return completion.choices[0].message.content


def format_docs(docs):
    """将所有文档内容合并为字符串"""
    return "\n\n".join(doc.page_content for doc in docs)


def build_qa_chain(store, llm):
    prompt_template = """你是一个有帮助的助手，根据以下上下文回答问题。如果不知道答案，就说不知道，不要胡编。
    上下文：{context}
    问题：{question}
    回答："""
    prompt = ChatPromptTemplate.from_template(prompt_template)
    qa_chain = (
            {
                "context": store.as_retriever() | format_docs,
                "question": RunnablePassthrough()
            }
            | prompt
            | llm
            | StrOutputParser()
    )
    return qa_chain


@app.route("/")
def index():
    # 可直接重定向到静态页面，也可让 index1.html 由 Flask render_template
    return "Backend is running. Please open the front-end HTML file."


@app.route("/upload", methods=["POST"])
def upload():
    """接收前端传来的文件并保存到本地"""
    if 'files' not in request.files:
        return jsonify({"error": "No files part in the request"}), 400

    uploaded_files = request.files.getlist('files')
    file_names = []
    for file in uploaded_files:
        if file.filename == '':
            continue
        filename = secure_filename(file.filename)
        filepath = os.path.join(app.config["UPLOAD_FOLDER"], filename)
        file.save(filepath)
        file_names.append(filename)

    return jsonify({"message": "Files uploaded successfully", "filenames": file_names})


@app.route("/build_kb", methods=["POST"])
def build_kb():
    """基于已上传的文件构建向量库"""
    global vectorstore
    try:
        # 1. 获取 uploaded_files 文件夹下所有文件
        all_files = os.listdir(UPLOAD_FOLDER)
        if not all_files:
            return jsonify({"error": "No files found to build the knowledge base"}), 400

        # 2. 加载并拼接所有文档
        docs = []
        for fname in all_files:
            f_path = os.path.join(UPLOAD_FOLDER, fname)
            loader = UnstructuredLoader(f_path)
            d = loader.load()
            docs.extend(d)

        # 3. 文本分块
        splits = text_splitter.split_documents(docs)

        # 4. 构建向量存储
        vectorstore = FAISS.from_documents(splits, embedding_model)

        return jsonify({"message": "知识库构建成功！"})
    except Exception as e:
        return jsonify({"error": str(e)}), 500


@app.route("/ask", methods=["POST"])
def ask():
    """接收前端的问题并调用 RAG 进行回答"""
    global vectorstore
    data = request.get_json()
    question = data.get("question", "")

    if not question:
        return jsonify({"error": "No question provided"}), 400

    if not vectorstore:
        return jsonify({"error": "Knowledge base not built yet"}), 400

    try:
        llm = QwenLLM()
        qa_chain = build_qa_chain(vectorstore, llm)
        answer = qa_chain.invoke(question)
        if not answer:
            answer = "未生成有效回答，请检查输入或知识库。"
        return jsonify({"answer": answer})
    except Exception as e:
        return jsonify({"error": str(e)}), 500


if __name__ == "__main__":
    # 开发模式下直接启动
    app.run(host="0.0.0.0", port=5000, debug=True)
