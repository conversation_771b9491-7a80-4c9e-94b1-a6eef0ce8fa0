{"cells": [{"cell_type": "code", "execution_count": null, "id": "initial_id", "metadata": {"collapsed": true}, "outputs": [], "source": ["import http.client\n", "import json\n", "from datetime import datetime\n", "import time # 用于在请求之间添加延迟，避免过于频繁访问服务器\n", "import pandas as pd\n", "def format_timestamp_for_script(ts):\n", "    \"\"\"将Unix时间戳转换为 YYYY-MM-DD 格式的日期字符串\"\"\"\n", "    if ts is None:\n", "        return \"N/A\"\n", "    try:\n", "        return datetime.fromtimestamp(ts).strftime('%Y-%m-%d')\n", "    except (<PERSON><PERSON><PERSON><PERSON>, ValueError):\n", "        return \"无效的时间戳\"\n", "\n", "def fetch_conference_data(page_number):\n", "    \"\"\"获取指定页码的会议数据\"\"\"\n", "    # 注意: 您使用的是HTTPSConnection配合8080端口。\n", "    # 通常HTTPS使用443端口，HTTP使用8080端口。请确保这符合您的服务器配置。\n", "    # 如果是HTTP服务在8080端口，应使用 http.client.HTTPConnection(\"uconf.com\", 8080)\n", "    conn = None # 初始化conn\n", "    try:\n", "        conn = http.client.HTTPSConnection(\"uconf.com\", 8080)\n", "        payload = ''\n", "        headers = {\n", "            'User-Agent': 'Apifox/1.0.0 (https://apifox.com)',\n", "            'Accept': '*/*',\n", "            'Host': 'uconf.com:8080',\n", "            'Connection': 'keep-alive'\n", "        }\n", "        \n", "        path = f\"/web/event/list?currentPage={page_number}\"\n", "        print(f\"正在获取页面: {page_number} (路径: {path})\")\n", "        \n", "        conn.request(\"GET\", path, payload, headers)\n", "        res = conn.getresponse()\n", "        print(f\"页面 {page_number} 状态码: {res.status} {res.reason}\")\n", "        \n", "        data = res.read()\n", "        decoded_data = data.decode(\"utf-8\")\n", "        \n", "        return json.loads(decoded_data)\n", "        \n", "    except http.client.HTTPException as e:\n", "        print(f\"页面 {page_number} 发生HTTP异常: {e}\")\n", "    except json.JSONDecodeError as e:\n", "        print(f\"页面 {page_number} 发生JSON解码错误: {e}\")\n", "        # print(f\"接收到的原始数据 (前500字节): {data[:500]}...\") # 取消注释以查看原始数据\n", "    except ConnectionRefusedError as e:\n", "        print(f\"页面 {page_number} 连接被拒绝: {e}. 请检查主机名和端口是否正确，以及服务器是否正在运行。\")\n", "    except Exception as e:\n", "        print(f\"页面 {page_number} 发生未知错误: {e}\")\n", "    finally:\n", "        if conn:\n", "            conn.close()\n", "    return None\n", "\n", "def extract_conference_info(json_data, page_number_for_log=\"N/A\"):\n", "    \"\"\"从解析后的JSON数据中提取会议信息\"\"\"\n", "    extracted_conferences = []\n", "    if json_data and json_data.get(\"code\") == \"000\" and \"data\" in json_data and \"list\" in json_data[\"data\"]:\n", "        for conference in json_data[\"data\"][\"list\"]:\n", "            categories = [cat.get(\"name\", \"N/A\") for cat in conference.get(\"categorys\", [])]\n", "            country_name = conference.get(\"country\", {}).get(\"venue\", \"N/A\")\n", "            city_name = conference.get(\"city\", \"N/A\")\n", "            if isinstance(city_name, str):\n", "                 city_name = city_name.strip()\n", "\n", "\n", "            info = {\n", "                \"id\": conference.get(\"id\"),\n", "                \"会议标题\": conference.get(\"title\"),\n", "                \"会议简称\": conference.get(\"event\"),\n", "                \"城市\": city_name,\n", "                \"国家\": country_name,\n", "                \"开始日期\": format_timestamp_for_script(conference.get(\"startDate\")),\n", "                \"结束日期\": format_timestamp_for_script(conference.get(\"endDate\")),\n", "                \"投稿截止日期\": format_timestamp_for_script(conference.get(\"subDate\")),\n", "                \"会议网址\": conference.get(\"web\"),\n", "                \"邮箱\": conference.get(\"email\"),\n", "                \"会议分类\": categories\n", "            }\n", "            extracted_conferences.append(info)\n", "    elif json_data:\n", "        print(f\"页面 {page_number_for_log} API调用可能成功，但数据格式不符合预期或收到错误代码: Code {json_data.get('code')}, Message: {json_data.get('message')}\")\n", "    else:\n", "        print(f\"页面 {page_number_for_log} 没有JSON数据可供处理。\")\n", "    return extracted_conferences\n", "\n", "# --- 主程序 ---\n", "if __name__ == \"__main__\":\n", "    # 设置您想要获取的页面范围\n", "    page_start = 1  # 开始页码\n", "    page_end = 120    # 结束页码 \n", "\n", "    all_extracted_conferences = []\n", "\n", "    print(f\"开始从页面 {page_start} 获取到页面 {page_end} 的数据...\")\n", "    for current_page in range(page_start, page_end + 1):\n", "        conference_json_data = fetch_conference_data(current_page)\n", "        if conference_json_data:\n", "            conferences_on_this_page = extract_conference_info(conference_json_data, current_page)\n", "            if conferences_on_this_page:\n", "                all_extracted_conferences.extend(conferences_on_this_page)\n", "                print(f\"成功从页面 {current_page} 提取了 {len(conferences_on_this_page)} 条会议信息。\")\n", "            else:\n", "                print(f\"未能从页面 {current_page} 的数据中提取会议信息。\")\n", "        else:\n", "            print(f\"获取或解析页面 {current_page} 数据失败。\")\n", "        \n", "        # 添加短暂延时，避免对服务器造成过大压力\n", "        if current_page < page_end: # 最后一页之后不需要延时\n", "             print(\"暂停1秒...\")\n", "             time.sleep(1) \n", "\n", "    print(\"\\n--- 所有提取到的会议信息 ---\")\n", "    if all_extracted_conferences:\n", "        for i, conf_info in enumerate(all_extracted_conferences):\n", "            print(f\"\\n--- 会议 {i+1} ---\")\n", "            print(json.dumps(conf_info, ensure_ascii=False, indent=2))\n", "        print(f\"\\n总共提取了 {len(all_extracted_conferences)} 条会议信息。\")\n", "    else:\n", "        print(\"未能提取到任何会议信息。\")\n", "\n", "\n", "    if all_extracted_conferences:\n", "        df = pd.DataFrame(all_extracted_conferences)\n", "        try:\n", "            df.to_csv(\"conference_data.csv\", index=False, encoding='utf-8-sig')\n", "            print(\"\\n会议数据已保存到 conference_data.csv\")\n", "        except Exception as e:\n", "            print(f\"\\n保存到CSV文件失败: {e}\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.6"}}, "nbformat": 4, "nbformat_minor": 5}