#!/bin/bash

# 系统部署与启动脚本
# 用于部署和启动电商竞品智能分析系统的前后端服务

echo "===== 电商竞品智能分析系统部署脚本 ====="

# 检查必要的软件依赖
echo "正在检查系统依赖..."
command -v python3 >/dev/null 2>&1 || { echo "错误: 需要安装 Python 3"; exit 1; }
command -v pip3 >/dev/null 2>&1 || { echo "错误: 需要安装 pip3"; exit 1; }
command -v npm >/dev/null 2>&1 || { echo "错误: 需要安装 npm"; exit 1; }
command -v mysql >/dev/null 2>&1 || { echo "警告: 未检测到 MySQL 客户端，数据库操作可能受限"; }
command -v redis-cli >/dev/null 2>&1 || { echo "警告: 未检测到 Redis 客户端，分布式功能可能受限"; }

# 设置工作目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

# 配置变量
BACKEND_DIR="$SCRIPT_DIR"
FRONTEND_DIR="$SCRIPT_DIR/frontend"
VENV_DIR="$BACKEND_DIR/venv"
LOG_DIR="$BACKEND_DIR/logs"
DB_NAME="ecommerce_analysis"
DB_USER="root"
DB_PASSWORD="password"
DB_HOST="localhost"
REDIS_HOST="localhost"
REDIS_PORT=6379

# 创建日志目录
mkdir -p "$LOG_DIR"

# 安装后端依赖
echo "正在安装后端依赖..."
if [ ! -d "$VENV_DIR" ]; then
    python3 -m venv "$VENV_DIR"
fi
source "$VENV_DIR/bin/activate"
pip3 install -r "$BACKEND_DIR/requirements.txt"

# 初始化数据库
echo "正在初始化数据库..."
if command -v mysql >/dev/null 2>&1; then
    # 检查数据库是否存在
    DB_EXISTS=$(mysql -u"$DB_USER" -p"$DB_PASSWORD" -h"$DB_HOST" -e "SHOW DATABASES LIKE '$DB_NAME';" 2>/dev/null | grep -c "$DB_NAME")
    
    if [ "$DB_EXISTS" -eq 0 ]; then
        echo "创建数据库 $DB_NAME..."
        mysql -u"$DB_USER" -p"$DB_PASSWORD" -h"$DB_HOST" -e "CREATE DATABASE IF NOT EXISTS $DB_NAME DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
    fi
    
    echo "导入数据库结构..."
    mysql -u"$DB_USER" -p"$DB_PASSWORD" -h"$DB_HOST" "$DB_NAME" < "$BACKEND_DIR/db_init.sql"
else
    echo "警告: 未检测到 MySQL 客户端，跳过数据库初始化"
    echo "请手动执行 db_init.sql 脚本初始化数据库"
fi

# 安装前端依赖
echo "正在安装前端依赖..."
cd "$FRONTEND_DIR"
npm install

# 构建前端
echo "正在构建前端..."
npm run build

# 启动后端服务
echo "正在启动后端服务..."
cd "$BACKEND_DIR"
source "$VENV_DIR/bin/activate"

# 启动API服务
echo "启动API服务..."
nohup python3 -u "$BACKEND_DIR/api/app.py" > "$LOG_DIR/api.log" 2>&1 &
API_PID=$!
echo "API服务已启动，PID: $API_PID"

# 启动任务调度器
echo "启动任务调度器..."
nohup python3 -u "$BACKEND_DIR/scheduler/task_scheduler.py" > "$LOG_DIR/scheduler.log" 2>&1 &
SCHEDULER_PID=$!
echo "任务调度器已启动，PID: $SCHEDULER_PID"

# 启动爬虫
echo "启动爬虫服务..."
cd "$BACKEND_DIR/scrapy_app"
nohup scrapy crawl jd_spider > "$LOG_DIR/jd_spider.log" 2>&1 &
JD_SPIDER_PID=$!
echo "京东爬虫已启动，PID: $JD_SPIDER_PID"

nohup scrapy crawl taobao_spider > "$LOG_DIR/taobao_spider.log" 2>&1 &
TAOBAO_SPIDER_PID=$!
echo "淘宝爬虫已启动，PID: $TAOBAO_SPIDER_PID"

nohup scrapy crawl amazon_cn_spider > "$LOG_DIR/amazon_cn_spider.log" 2>&1 &
AMAZON_SPIDER_PID=$!
echo "亚马逊爬虫已启动，PID: $AMAZON_SPIDER_PID"

# 启动前端开发服务器（可选）
if [ "$1" == "--dev" ]; then
    echo "启动前端开发服务器..."
    cd "$FRONTEND_DIR"
    npm run dev &
    FRONTEND_PID=$!
    echo "前端开发服务器已启动，PID: $FRONTEND_PID"
fi

echo "===== 系统启动完成 ====="
echo "API服务: http://localhost:5000"
if [ "$1" == "--dev" ]; then
    echo "前端开发服务器: http://localhost:3000"
else
    echo "前端已构建，可通过API服务访问"
fi
echo "日志目录: $LOG_DIR"
echo ""
echo "使用以下命令停止服务:"
echo "kill $API_PID $SCHEDULER_PID $JD_SPIDER_PID $TAOBAO_SPIDER_PID $AMAZON_SPIDER_PID"
if [ "$1" == "--dev" ]; then
    echo "kill $FRONTEND_PID"
fi
