<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>创想科技 - 引领数字未来</title>
    <style>
        /* 全局样式重置 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
        }

        /* 变量定义 */
        :root {
            --primary-color: #4361ee;
            --secondary-color: #3f37c9;
            --accent-color: #4cc9f0;
            --light-color: #f8f9fa;
            --dark-color: #212529;
            --success-color: #4bb543;
            --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            --transition: all 0.3s ease;
        }

        body {
            background-color: var(--light-color);
            color: var(--dark-color);
            line-height: 1.6;
            overflow-x: hidden;
        }

        /* 导航栏 */
        header {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            padding: 1.5rem 5%;
            display: flex;
            justify-content: space-between;
            align-items: center;
            background-color: rgba(255, 255, 255, 0.95);
            box-shadow: var(--shadow);
            z-index: 1000;
            transition: var(--transition);
        }

        .logo {
            font-size: 1.8rem;
            font-weight: 700;
            color: var(--primary-color);
            text-decoration: none;
            display: flex;
            align-items: center;
        }

        .logo i {
            margin-right: 0.5rem;
            font-size: 2rem;
        }

        .nav-links {
            display: flex;
            list-style: none;
        }

        .nav-links li {
            margin-left: 2rem;
        }

        .nav-links a {
            text-decoration: none;
            color: var(--dark-color);
            font-weight: 500;
            transition: var(--transition);
            position: relative;
        }

        .nav-links a:hover {
            color: var(--primary-color);
        }

        .nav-links a::after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 0;
            width: 0;
            height: 2px;
            background-color: var(--primary-color);
            transition: var(--transition);
        }

        .nav-links a:hover::after {
            width: 100%;
        }

        .mobile-menu-btn {
            display: none;
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: var(--dark-color);
        }

        /* 英雄区域 */
        .hero {
            height: 100vh;
            display: flex;
            align-items: center;
            padding: 0 5%;
            margin-top: -80px;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            position: relative;
            overflow: hidden;
        }

        .hero::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 50%;
            height: 100%;
            background: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMDAlIiBoZWlnaHQ9IjEwMCUiPjxkZWZzPjxwYXR0ZXJuIGlkPSJwYXR0ZXJuIiB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHBhdHRlcm5Vbml0cz0idXNlclNwYWNlT25Vc2UiIHBhdHRlcm5UcmFuc2Zvcm09InJvdGF0ZSg0NSkiPjxyZWN0IHdpZHRoPSIyMCIgaGVpZ2h0PSIyMCIgZmlsbD0icmdiYSgyNTUsMjU1LDI1NSwwLjEpIi8+PC9wYXR0ZXJuPjwvZGVmcz48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSJ1cmwoI3BhdHRlcm4pIi8+PC9zdmc+') repeat;
            z-index: 1;
        }

        .hero-content {
            max-width: 600px;
            z-index: 2;
        }

        .hero h1 {
            font-size: 3.5rem;
            margin-bottom: 1.5rem;
            color: var(--dark-color);
            line-height: 1.2;
        }

        .hero p {
            font-size: 1.2rem;
            margin-bottom: 2rem;
            color: #555;
        }

        .hero-btns {
            display: flex;
            gap: 1rem;
        }

        .btn {
            display: inline-block;
            padding: 0.8rem 1.8rem;
            border-radius: 50px;
            text-decoration: none;
            font-weight: 600;
            transition: var(--transition);
            cursor: pointer;
        }

        .btn-primary {
            background-color: var(--primary-color);
            color: white;
            box-shadow: 0 4px 15px rgba(67, 97, 238, 0.4);
        }

        .btn-primary:hover {
            background-color: var(--secondary-color);
            transform: translateY(-3px);
            box-shadow: 0 6px 20px rgba(67, 97, 238, 0.6);
        }

        .btn-outline {
            border: 2px solid var(--primary-color);
            color: var(--primary-color);
            background: transparent;
        }

        .btn-outline:hover {
            background-color: var(--primary-color);
            color: white;
            transform: translateY(-3px);
        }

        .hero-image {
            position: absolute;
            right: 5%;
            width: 45%;
            max-width: 700px;
            z-index: 2;
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
            100% { transform: translateY(0px); }
        }

        /* 特性部分 */
        .features {
            padding: 6rem 5%;
            background-color: white;
        }

        .section-title {
            text-align: center;
            margin-bottom: 4rem;
        }

        .section-title h2 {
            font-size: 2.5rem;
            color: var(--dark-color);
            margin-bottom: 1rem;
            position: relative;
            display: inline-block;
        }

        .section-title h2::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 4px;
            background-color: var(--primary-color);
            border-radius: 2px;
        }

        .section-title p {
            color: #666;
            max-width: 700px;
            margin: 0 auto;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-top: 3rem;
        }

        .feature-card {
            background-color: white;
            border-radius: 10px;
            padding: 2rem;
            box-shadow: var(--shadow);
            transition: var(--transition);
            text-align: center;
        }

        .feature-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
        }

        .feature-icon {
            width: 80px;
            height: 80px;
            background-color: rgba(67, 97, 238, 0.1);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1.5rem;
            color: var(--primary-color);
            font-size: 2rem;
        }

        .feature-card h3 {
            font-size: 1.5rem;
            margin-bottom: 1rem;
            color: var(--dark-color);
        }

        .feature-card p {
            color: #666;
        }

        /* 产品展示 */
        .products {
            padding: 6rem 5%;
            background-color: #f9fafc;
        }

        .products-container {
            display: flex;
            flex-wrap: wrap;
            gap: 2rem;
            justify-content: center;
            margin-top: 3rem;
        }

        .product-card {
            background-color: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: var(--shadow);
            transition: var(--transition);
            max-width: 350px;
            width: 100%;
        }

        .product-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
        }

        .product-image {
            height: 200px;
            overflow: hidden;
        }

        .product-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: var(--transition);
        }

        .product-card:hover .product-image img {
            transform: scale(1.1);
        }

        .product-content {
            padding: 1.5rem;
        }

        .product-content h3 {
            font-size: 1.5rem;
            margin-bottom: 0.5rem;
            color: var(--dark-color);
        }

        .product-content p {
            color: #666;
            margin-bottom: 1.5rem;
        }

        .product-price {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 1rem;
        }

        /* 客户评价 */
        .testimonials {
            padding: 6rem 5%;
            background-color: white;
        }

        .testimonial-slider {
            max-width: 1000px;
            margin: 3rem auto 0;
            position: relative;
            overflow: hidden;
        }

        .testimonial-slide {
            background-color: #f9fafc;
            border-radius: 10px;
            padding: 2rem;
            box-shadow: var(--shadow);
            text-align: center;
            display: none;
            animation: fade 0.5s ease-in-out;
        }

        @keyframes fade {
            from { opacity: 0.4; }
            to { opacity: 1; }
        }

        .testimonial-slide.active {
            display: block;
        }

        .testimonial-avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            object-fit: cover;
            margin: 0 auto 1rem;
            border: 3px solid var(--primary-color);
        }

        .testimonial-quote {
            font-size: 1.1rem;
            font-style: italic;
            color: #555;
            margin-bottom: 1.5rem;
            position: relative;
        }

        .testimonial-quote::before,
        .testimonial-quote::after {
            content: '"';
            font-size: 2rem;
            color: var(--primary-color);
            opacity: 0.3;
            position: absolute;
        }

        .testimonial-quote::before {
            top: -15px;
            left: -10px;
        }

        .testimonial-quote::after {
            bottom: -25px;
            right: -10px;
        }

        .testimonial-author {
            font-weight: 700;
            color: var(--dark-color);
            margin-bottom: 0.5rem;
        }

        .testimonial-position {
            color: #777;
            font-size: 0.9rem;
        }

        .slider-dots {
            display: flex;
            justify-content: center;
            margin-top: 2rem;
        }

        .dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background-color: #ddd;
            margin: 0 5px;
            cursor: pointer;
            transition: var(--transition);
        }

        .dot.active {
            background-color: var(--primary-color);
        }

        /* 联系表单 */
        .contact {
            padding: 6rem 5%;
            background-color: #f9fafc;
        }

        .contact-container {
            max-width: 1000px;
            margin: 3rem auto 0;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 3rem;
        }

        .contact-info {
            display: flex;
            flex-direction: column;
            gap: 2rem;
        }

        .contact-item {
            display: flex;
            align-items: flex-start;
            gap: 1rem;
        }

        .contact-icon {
            width: 50px;
            height: 50px;
            background-color: rgba(67, 97, 238, 0.1);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--primary-color);
            font-size: 1.2rem;
            flex-shrink: 0;
        }

        .contact-text h3 {
            font-size: 1.2rem;
            margin-bottom: 0.5rem;
            color: var(--dark-color);
        }

        .contact-text p {
            color: #666;
        }

        .contact-form {
            background-color: white;
            border-radius: 10px;
            padding: 2rem;
            box-shadow: var(--shadow);
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: var(--dark-color);
        }

        .form-control {
            width: 100%;
            padding: 0.8rem 1rem;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 1rem;
            transition: var(--transition);
        }

        .form-control:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.2);
        }

        textarea.form-control {
            min-height: 150px;
            resize: vertical;
        }

        .submit-btn {
            width: 100%;
            padding: 0.8rem;
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: 5px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition);
        }

        .submit-btn:hover {
            background-color: var(--secondary-color);
        }

        /* 页脚 */
        footer {
            background-color: var(--dark-color);
            color: white;
            padding: 4rem 5% 2rem;
        }

        .footer-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 2rem;
            max-width: 1200px;
            margin: 0 auto;
        }

        .footer-logo {
            font-size: 1.8rem;
            font-weight: 700;
            color: white;
            margin-bottom: 1rem;
            display: inline-block;
        }

        .footer-about p {
            color: #aaa;
            margin-bottom: 1.5rem;
        }

        .social-links {
            display: flex;
            gap: 1rem;
        }

        .social-link {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: rgba(255, 255, 255, 0.1);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            text-decoration: none;
            transition: var(--transition);
        }

        .social-link:hover {
            background-color: var(--primary-color);
            transform: translateY(-3px);
        }

        .footer-links h3 {
            font-size: 1.2rem;
            margin-bottom: 1.5rem;
            position: relative;
            padding-bottom: 0.5rem;
        }

        .footer-links h3::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 40px;
            height: 2px;
            background-color: var(--primary-color);
        }

        .footer-links ul {
            list-style: none;
        }

        .footer-links li {
            margin-bottom: 0.8rem;
        }

        .footer-links a {
            color: #aaa;
            text-decoration: none;
            transition: var(--transition);
        }

        .footer-links a:hover {
            color: white;
            padding-left: 5px;
        }

        .footer-newsletter p {
            color: #aaa;
            margin-bottom: 1.5rem;
        }

        .newsletter-form {
            display: flex;
        }

        .newsletter-input {
            flex: 1;
            padding: 0.8rem;
            border: none;
            border-radius: 5px 0 0 5px;
            font-size: 1rem;
        }

        .newsletter-input:focus {
            outline: none;
        }

        .newsletter-btn {
            padding: 0 1.2rem;
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: 0 5px 5px 0;
            cursor: pointer;
            transition: var(--transition);
        }

        .newsletter-btn:hover {
            background-color: var(--secondary-color);
        }

        .copyright {
            text-align: center;
            padding-top: 3rem;
            margin-top: 3rem;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            color: #aaa;
            font-size: 0.9rem;
        }

        /* 返回顶部按钮 */
        .back-to-top {
            position: fixed;
            bottom: 2rem;
            right: 2rem;
            width: 50px;
            height: 50px;
            background-color: var(--primary-color);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            text-decoration: none;
            font-size: 1.5rem;
            opacity: 0;
            visibility: hidden;
            transition: var(--transition);
            z-index: 999;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
        }

        .back-to-top.active {
            opacity: 1;
            visibility: visible;
        }

        .back-to-top:hover {
            background-color: var(--secondary-color);
            transform: translateY(-5px);
        }

        /* 响应式设计 */
        @media (max-width: 992px) {
            .hero h1 {
                font-size: 2.8rem;
            }

            .hero-image {
                width: 40%;
            }
        }

        @media (max-width: 768px) {
            .nav-links {
                position: fixed;
                top: 80px;
                left: -100%;
                width: 100%;
                height: calc(100vh - 80px);
                background-color: white;
                flex-direction: column;
                align-items: center;
                justify-content: flex-start;
                padding-top: 3rem;
                transition: var(--transition);
            }

            .nav-links.active {
                left: 0;
            }

            .nav-links li {
                margin: 1rem 0;
            }

            .mobile-menu-btn {
                display: block;
            }

            .hero {
                flex-direction: column;
                text-align: center;
                padding-top: 120px;
                height: auto;
                min-height: 100vh;
            }

            .hero::before {
                display: none;
            }

            .hero-content {
                max-width: 100%;
                margin-bottom: 3rem;
            }

            .hero-btns {
                justify-content: center;
            }

            .hero-image {
                position: relative;
                right: auto;
                width: 80%;
                margin: 0 auto;
            }

            .section-title h2 {
                font-size: 2rem;
            }
        }

        @media (max-width: 576px) {
            .hero h1 {
                font-size: 2.2rem;
            }

            .hero p {
                font-size: 1rem;
            }

            .btn {
                padding: 0.6rem 1.4rem;
            }

            .hero-btns {
                flex-direction: column;
                gap: 1rem;
            }

            .features-grid {
                grid-template-columns: 1fr;
            }

            .section-title h2 {
                font-size: 1.8rem;
            }

            .footer-container {
                grid-template-columns: 1fr;
            }
        }
    </style>
    <!-- 使用Font Awesome图标 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
</head>
<body>
    <!-- 导航栏 -->
    <header>
        <a href="#" class="logo">
            <i class="fas fa-rocket"></i>
            创想科技
        </a>
        <button class="mobile-menu-btn" id="mobileMenuBtn">
            <i class="fas fa-bars"></i>
        </button>
        <ul class="nav-links" id="navLinks">
            <li><a href="#home">首页</a></li>
            <li><a href="#features">产品特性</a></li>
            <li><a href="#products">产品展示</a></li>
            <li><a href="#testimonials">客户评价</a></li>
            <li><a href="#contact">联系我们</a></li>
        </ul>
    </header>

    <!-- 英雄区域 -->
    <section class="hero" id="home">
        <div class="hero-content">
            <h1>创新科技，引领未来</h1>
            <p>我们致力于为企业提供最前沿的数字化解决方案，助力您的业务在数字时代蓬勃发展。从云计算到人工智能，我们为您提供一站式技术服务。</p>
            <div class="hero-btns">
                <a href="#contact" class="btn btn-primary">免费咨询</a>
                <a href="#products" class="btn btn-outline">产品展示</a>
            </div>
        </div>
        <img src="https://images.unsplash.com/photo-1551288049-bebda4e38f71?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80" alt="科技展示" class="hero-image">
    </section>

    <!-- 特性部分 -->
    <section class="features" id="features">
        <div class="section-title">
            <h2>我们的优势</h2>
            <p>创想科技拥有行业领先的技术实力和服务团队，为您提供全方位的数字化解决方案</p>
        </div>
        <div class="features-grid">
            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-bolt"></i>
                </div>
                <h3>高效性能</h3>
                <p>我们的解决方案采用最先进的架构设计，确保系统运行高效稳定，处理速度比传统方案提升300%。</p>
            </div>
            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-shield-alt"></i>
                </div>
                <h3>安全保障</h3>
                <p>多层安全防护机制，数据加密传输存储，符合国际安全标准，为您的业务保驾护航。</p>
            </div>
            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-cogs"></i>
                </div>
                <h3>智能定制</h3>
                <p>基于AI的智能分析系统，可根据您的业务需求提供个性化定制服务，满足不同场景需求。</p>
            </div>
            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-headset"></i>
                </div>
                <h3>全天候支持</h3>
                <p>7×24小时专业技术支持团队，随时响应您的需求，确保业务连续性无忧。</p>
            </div>
            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-chart-line"></i>
                </div>
                <h3>数据分析</h3>
                <p>强大的数据分析能力，帮助您洞察业务趋势，做出更明智的决策，提升运营效率。</p>
            </div>
            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-cloud"></i>
                </div>
                <h3>云端部署</h3>
                <p>支持公有云、私有云和混合云多种部署方式，灵活扩展，按需付费，降低IT成本。</p>
            </div>
        </div>
    </section>

    <!-- 产品展示 -->
    <section class="products" id="products">
        <div class="section-title">
            <h2>热门产品</h2>
            <p>我们提供全方位的数字化产品解决方案，满足企业不同发展阶段的需求</p>
        </div>
        <div class="products-container">
            <div class="product-card">
                <div class="product-image">
                    <img src="https://images.unsplash.com/photo-1551288049-bebda4e38f71?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80" alt="智能ERP系统">
                </div>
                <div class="product-content">
                    <h3>智能ERP系统</h3>
                    <p>新一代企业资源规划系统，整合财务、供应链、生产、销售等核心业务流程，实现企业数字化管理。</p>
                    <div class="product-price">¥98,000起</div>
                    <a href="#contact" class="btn btn-outline">咨询详情</a>
                </div>
            </div>
            <div class="product-card">
                <div class="product-image">
                    <img src="https://images.unsplash.com/photo-1620712943543-bcc4688e7485?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80" alt="CRM客户关系管理">
                </div>
                <div class="product-content">
                    <h3>CRM客户关系管理</h3>
                    <p>基于AI的客户关系管理系统，帮助您更好地了解客户需求，提升销售转化率和客户满意度。</p>
                    <div class="product-price">¥68,000起</div>
                    <a href="#contact" class="btn btn-outline">咨询详情</a>
                </div>
            </div>
            <div class="product-card">
                <div class="product-image">
                    <img src="https://images.unsplash.com/photo-1523961131990-5ea7c61b2107?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80" alt="数据分析平台">
                </div>
                <div class="product-content">
                    <h3>数据分析平台</h3>
                    <p>一站式商业智能分析平台，支持多源数据整合、可视化分析和预测建模，助力数据驱动决策。</p>
                    <div class="product-price">¥128,000起</div>
                    <a href="#contact" class="btn btn-outline">咨询详情</a>
                </div>
            </div>
        </div>
    </section>

    <!-- 客户评价 -->
    <section class="testimonials" id="testimonials">
        <div class="section-title">
            <h2>客户评价</h2>
            <p>听听我们的客户对创想科技产品和服务的真实反馈</p>
        </div>
        <div class="testimonial-slider">
            <div class="testimonial-slide active">
                <img src="https://randomuser.me/api/portraits/women/43.jpg" alt="张女士" class="testimonial-avatar">
                <p class="testimonial-quote">创想科技的ERP系统彻底改变了我们的业务流程，效率提升了40%，数据可视化让管理决策更加科学高效。</p>
                <h4 class="testimonial-author">张女士</h4>
                <p class="testimonial-position">某制造业集团 CIO</p>
            </div>
            <div class="testimonial-slide">
                <img src="https://randomuser.me/api/portraits/men/32.jpg" alt="王先生" class="testimonial-avatar">
                <p class="testimonial-quote">他们的CRM系统帮助我们建立了完整的客户画像，销售转化率提升了25%，客户满意度也有显著提高。</p>
                <h4 class="testimonial-author">王先生</h4>
                <p class="testimonial-position">某零售连锁企业 销售总监</p>
            </div>
            <div class="testimonial-slide">
                <img src="https://randomuser.me/api/portraits/women/65.jpg" alt="李女士" class="testimonial-avatar">
                <p class="testimonial-quote">数据分析平台让我们的运营团队能够实时监控业务指标，发现问题并快速响应，运营效率提升了30%。</p>
                <h4 class="testimonial-author">李女士</h4>
                <p class="testimonial-position">某电商平台 运营总监</p>
            </div>
            <div class="slider-dots">
                <span class="dot active" data-slide="0"></span>
                <span class="dot" data-slide="1"></span>
                <span class="dot" data-slide="2"></span>
            </div>
        </div>
    </section>

    <!-- 联系表单 -->
    <section class="contact" id="contact">
        <div class="section-title">
            <h2>联系我们</h2>
            <p>无论您有任何问题或需求，我们的专业团队都随时准备为您提供帮助</p>
        </div>
        <div class="contact-container">
            <div class="contact-info">
                <div class="contact-item">
                    <div class="contact-icon">
                        <i class="fas fa-map-marker-alt"></i>
                    </div>
                    <div class="contact-text">
                        <h3>公司地址</h3>
                        <p>北京市海淀区中关村南大街5号创想大厦18层</p>
                    </div>
                </div>
                <div class="contact-item">
                    <div class="contact-icon">
                        <i class="fas fa-phone-alt"></i>
                    </div>
                    <div class="contact-text">
                        <h3>联系电话</h3>
                        <p>************<br>010-88889999</p>
                    </div>
                </div>
                <div class="contact-item">
                    <div class="contact-icon">
                        <i class="fas fa-envelope"></i>
                    </div>
                    <div class="contact-text">
                        <h3>电子邮箱</h3>
                        <p><EMAIL><br><EMAIL></p>
                    </div>
                </div>
                <div class="contact-item">
                    <div class="contact-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="contact-text">
                        <h3>工作时间</h3>
                        <p>周一至周五: 9:00 - 18:00<br>周六: 10:00 - 16:00</p>
                    </div>
                </div>
            </div>
            <div class="contact-form">
                <form id="contactForm">
                    <div class="form-group">
                        <label for="name">您的姓名</label>
                        <input type="text" id="name" class="form-control" required>
                    </div>
                    <div class="form-group">
                        <label for="email">电子邮箱</label>
                        <input type="email" id="email" class="form-control" required>
                    </div>
                    <div class="form-group">
                        <label for="phone">联系电话</label>
                        <input type="tel" id="phone" class="form-control">
                    </div>
                    <div class="form-group">
                        <label for="message">咨询内容</label>
                        <textarea id="message" class="form-control" required></textarea>
                    </div>
                    <button type="submit" class="submit-btn">提交咨询</button>
                </form>
            </div>
        </div>
    </section>

    <!-- 页脚 -->
    <footer>
        <div class="footer-container">
            <div class="footer-about">
                <a href="#" class="footer-logo">
                    <i class="fas fa-rocket"></i>
                    创想科技
                </a>
                <p>创想科技成立于2010年，是国内领先的企业数字化解决方案提供商，致力于通过技术创新帮助企业实现数字化转型。</p>
                <div class="social-links">
                    <a href="#" class="social-link"><i class="fab fa-weixin"></i></a>
                    <a href="#" class="social-link"><i class="fab fa-weibo"></i></a>
                    <a href="#" class="social-link"><i class="fab fa-linkedin-in"></i></a>
                    <a href="#" class="social-link"><i class="fab fa-github"></i></a>
                </div>
            </div>
            <div class="footer-links">
                <h3>快速链接</h3>
                <ul>
                    <li><a href="#home">首页</a></li>
                    <li><a href="#features">产品特性</a></li>
                    <li><a href="#products">产品展示</a></li>
                    <li><a href="#testimonials">客户评价</a></li>
                    <li><a href="#contact">联系我们</a></li>
                </ul>
            </div>
            <div class="footer-links">
                <h3>产品服务</h3>
                <ul>
                    <li><a href="#">智能ERP系统</a></li>
                    <li><a href="#">CRM客户管理</a></li>
                    <li><a href="#">数据分析平台</a></li>
                    <li><a href="#">云计算服务</a></li>
                    <li><a href="#">AI解决方案</a></li>
                </ul>
            </div>
            <div class="footer-newsletter">
                <h3>订阅资讯</h3>
                <p>订阅我们的电子资讯，获取最新产品动态和行业洞察。</p>
                <form class="newsletter-form">
                    <input type="email" placeholder="您的邮箱地址" class="newsletter-input" required>
                    <button type="submit" class="newsletter-btn">
                        <i class="fas fa-paper-plane"></i>
                    </button>
                </form>
            </div>
        </div>
        <div class="copyright">
            <p>&copy; 2023 创想科技 版权所有 | 京ICP备12345678号</p>
        </div>
    </footer>

    <!-- 返回顶部按钮 -->
    <a href="#" class="back-to-top" id="backToTop">
        <i class="fas fa-arrow-up"></i>
    </a>

    <script>
        // 移动端菜单切换
        const mobileMenuBtn = document.getElementById('mobileMenuBtn');
        const navLinks = document.getElementById('navLinks');

        mobileMenuBtn.addEventListener('click', () => {
            navLinks.classList.toggle('active');
            mobileMenuBtn.innerHTML = navLinks.classList.contains('active')
                ? '<i class="fas fa-times"></i>'
                : '<i class="fas fa-bars"></i>';
        });

        // 平滑滚动
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function(e) {
                e.preventDefault();

                if(this.getAttribute('href') === '#') return;

                const target = document.querySelector(this.getAttribute('href'));
                if(target) {
                    window.scrollTo({
                        top: target.offsetTop - 80,
                        behavior: 'smooth'
                    });

                    // 关闭移动菜单
                    if(navLinks.classList.contains('active')) {
                        navLinks.classList.remove('active');
                        mobileMenuBtn.innerHTML = '<i class="fas fa-bars"></i>';
                    }
                }
            });
        });

        // 返回顶部按钮显示/隐藏
        const backToTop = document.getElementById('backToTop');

        window.addEventListener('scroll', () => {
            if(window.pageYOffset > 300) {
                backToTop.classList.add('active');
            } else {
                backToTop.classList.remove('active');
            }
        });

        // 客户评价轮播
        let currentSlide = 0;
        const slides = document.querySelectorAll('.testimonial-slide');
        const dots = document.querySelectorAll('.dot');

        function showSlide(n) {
            slides.forEach(slide => slide.classList.remove('active'));
            dots.forEach(dot => dot.classList.remove('active'));

            currentSlide = (n + slides.length) % slides.length;
            slides[currentSlide].classList.add('active');
            dots[currentSlide].classList.add('active');
        }

        dots.forEach(dot => {
            dot.addEventListener('click', function() {
                showSlide(parseInt(this.getAttribute('data-slide')));
            });
        });

        // 自动轮播
        setInterval(() => {
            showSlide(currentSlide + 1);
        }, 5000);

        // 表单提交
        const contactForm = document.getElementById('contactForm');

        contactForm.addEventListener('submit', function(e) {
            e.preventDefault();

            // 这里可以添加表单验证和AJAX提交逻辑
            alert('感谢您的咨询！我们的团队会尽快与您联系。');
            this.reset();
        });

        // 页面加载动画
        document.addEventListener('DOMContentLoaded', () => {
            document.body.style.opacity = '1';
        });
    </script>
</body>
</html>