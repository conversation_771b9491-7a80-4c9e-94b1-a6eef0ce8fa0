# -*- coding: utf-8 -*-
from flask import Blueprint, request, jsonify
from services import product_service, task_service, analysis_service, user_service
import logging

# 创建蓝图
api_bp = Blueprint('api', __name__)
logger = logging.getLogger(__name__)

# 用户相关接口
@api_bp.route('/auth/login', methods=['POST'])
def login():
    """用户登录"""
    data = request.get_json()
    username = data.get('username')
    password = data.get('password')
    
    if not username or not password:
        return jsonify({'status': 'error', 'message': '用户名和密码不能为空'}), 400
    
    result = user_service.login(username, password)
    if result['status'] == 'success':
        return jsonify(result), 200
    else:
        return jsonify(result), 401

@api_bp.route('/auth/register', methods=['POST'])
def register():
    """用户注册"""
    data = request.get_json()
    username = data.get('username')
    password = data.get('password')
    email = data.get('email')
    
    if not username or not password:
        return jsonify({'status': 'error', 'message': '用户名和密码不能为空'}), 400
    
    result = user_service.register(username, password, email)
    if result['status'] == 'success':
        return jsonify(result), 201
    else:
        return jsonify(result), 400

# 商品相关接口
@api_bp.route('/products', methods=['GET'])
def get_products():
    """获取商品列表"""
    platform = request.args.get('platform')
    category = request.args.get('category')
    keyword = request.args.get('keyword')
    page = int(request.args.get('page', 1))
    page_size = int(request.args.get('page_size', 20))
    
    result = product_service.get_products(platform, category, keyword, page, page_size)
    return jsonify(result)

@api_bp.route('/products/<platform>/<product_id>', methods=['GET'])
def get_product_detail(platform, product_id):
    """获取商品详情"""
    result = product_service.get_product_detail(platform, product_id)
    if result:
        return jsonify({'status': 'success', 'data': result})
    else:
        return jsonify({'status': 'error', 'message': '商品不存在'}), 404

@api_bp.route('/products/price-history/<platform>/<product_id>', methods=['GET'])
def get_price_history(platform, product_id):
    """获取商品价格历史"""
    days = int(request.args.get('days', 30))
    result = product_service.get_price_history(platform, product_id, days)
    return jsonify({'status': 'success', 'data': result})

@api_bp.route('/products/reviews/<platform>/<product_id>', methods=['GET'])
def get_product_reviews(platform, product_id):
    """获取商品评价信息"""
    result = product_service.get_product_reviews(platform, product_id)
    return jsonify({'status': 'success', 'data': result})

# 任务相关接口
@api_bp.route('/tasks', methods=['GET'])
def get_tasks():
    """获取任务列表"""
    status = request.args.get('status')
    platform = request.args.get('platform')
    page = int(request.args.get('page', 1))
    page_size = int(request.args.get('page_size', 20))
    
    result = task_service.get_tasks(status, platform, page, page_size)
    return jsonify(result)

@api_bp.route('/tasks', methods=['POST'])
def create_task():
    """创建新任务"""
    data = request.get_json()
    url = data.get('url')
    platform = data.get('platform')
    
    if not url:
        return jsonify({'status': 'error', 'message': 'URL不能为空'}), 400
    
    result = task_service.create_task(url, platform)
    if result['status'] == 'success':
        return jsonify(result), 201
    else:
        return jsonify(result), 400

@api_bp.route('/tasks/<task_id>', methods=['GET'])
def get_task_detail(task_id):
    """获取任务详情"""
    result = task_service.get_task_detail(task_id)
    if result:
        return jsonify({'status': 'success', 'data': result})
    else:
        return jsonify({'status': 'error', 'message': '任务不存在'}), 404

@api_bp.route('/tasks/<task_id>', methods=['DELETE'])
def delete_task(task_id):
    """删除任务"""
    result = task_service.delete_task(task_id)
    if result['status'] == 'success':
        return jsonify(result)
    else:
        return jsonify(result), 400

@api_bp.route('/tasks/retry/<task_id>', methods=['POST'])
def retry_task(task_id):
    """重试任务"""
    result = task_service.retry_task(task_id)
    if result['status'] == 'success':
        return jsonify(result)
    else:
        return jsonify(result), 400

# 分析相关接口
@api_bp.route('/analysis/compare', methods=['POST'])
def compare_products():
    """比较多个商品"""
    data = request.get_json()
    product_ids = data.get('product_ids', [])
    platforms = data.get('platforms', [])
    
    if not product_ids or len(product_ids) < 2:
        return jsonify({'status': 'error', 'message': '至少需要两个商品进行比较'}), 400
    
    result = analysis_service.compare_products(product_ids, platforms)
    return jsonify({'status': 'success', 'data': result})

@api_bp.route('/analysis/price-trend/<platform>/<product_id>', methods=['GET'])
def analyze_price_trend(platform, product_id):
    """分析价格趋势"""
    days = int(request.args.get('days', 30))
    result = analysis_service.analyze_price_trend(platform, product_id, days)
    return jsonify({'status': 'success', 'data': result})

@api_bp.route('/analysis/review-sentiment/<platform>/<product_id>', methods=['GET'])
def analyze_review_sentiment(platform, product_id):
    """分析评价情感"""
    result = analysis_service.analyze_review_sentiment(platform, product_id)
    return jsonify({'status': 'success', 'data': result})

@api_bp.route('/analysis/market-share', methods=['POST'])
def analyze_market_share():
    """分析市场份额"""
    data = request.get_json()
    product_ids = data.get('product_ids', [])
    platforms = data.get('platforms', [])
    category = data.get('category')
    
    result = analysis_service.analyze_market_share(product_ids, platforms, category)
    return jsonify({'status': 'success', 'data': result})

# 系统相关接口
@api_bp.route('/system/stats', methods=['GET'])
def get_system_stats():
    """获取系统统计信息"""
    result = {
        'status': 'success',
        'data': {
            'product_count': product_service.get_product_count(),
            'task_count': task_service.get_task_count(),
            'platform_stats': product_service.get_platform_stats(),
            'recent_tasks': task_service.get_recent_tasks(5)
        }
    }
    return jsonify(result)

@api_bp.route('/system/categories', methods=['GET'])
def get_categories():
    """获取所有商品类别"""
    result = product_service.get_categories()
    return jsonify({'status': 'success', 'data': result})
