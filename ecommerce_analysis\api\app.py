# -*- coding: utf-8 -*-
from flask import Flask, request, jsonify
from flask_cors import CORS
import logging
import os
from dotenv import load_dotenv
from routes import api_bp
from services.db_service import init_db

# 加载环境变量
load_dotenv()

def create_app():
    """创建并配置Flask应用"""
    app = Flask(__name__)
    
    # 配置跨域
    CORS(app)
    
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # 配置数据库
    app.config['MYSQL_HOST'] = os.getenv('MYSQL_HOST', 'localhost')
    app.config['MYSQL_PORT'] = int(os.getenv('MYSQL_PORT', 3306))
    app.config['MYSQL_USER'] = os.getenv('MYSQL_USER', 'root')
    app.config['MYSQL_PASSWORD'] = os.getenv('MYSQL_PASSWORD', 'password')
    app.config['MYSQL_DB'] = os.getenv('MYSQL_DB', 'ecommerce_analysis')
    
    # 配置Redis
    app.config['REDIS_HOST'] = os.getenv('REDIS_HOST', 'localhost')
    app.config['REDIS_PORT'] = int(os.getenv('REDIS_PORT', 6379))
    app.config['REDIS_DB'] = int(os.getenv('REDIS_DB', 0))
    
    # 注册蓝图
    app.register_blueprint(api_bp, url_prefix='/api')
    
    # 初始化数据库连接
    init_db(app)
    
    return app

app = create_app()

@app.route('/')
def index():
    """API根路径"""
    return jsonify({
        'status': 'success',
        'message': '电商竞品智能分析系统API服务正在运行',
        'version': '1.0.0'
    })

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000, debug=True)
