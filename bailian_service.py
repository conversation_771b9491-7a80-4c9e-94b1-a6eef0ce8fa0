import os
from alibabacloud_bailian20231229.client import Client as BailianClient
from alibabacloud_tea_openapi import models as open_api_models
from alibabacloud_bailian20231229 import models as bailian_models
from alibabacloud_tea_util import models as util_models
from Tea.exceptions import TeaException

class BailianService:
    def __init__(self):
        config = open_api_models.Config(
            access_key_id=os.getenv('ALIBABA_CLOUD_ACCESS_KEY_ID'),
            access_key_secret=os.getenv('ALIBABA_CLOUD_ACCESS_KEY_SECRET'),
            endpoint='bailian.cn-beijing.aliyuncs.com'
        )
        self.client = BailianClient(config)
        self.workspace_id = os.getenv('WORKSPACE_ID')

    def apply_upload_lease(self, filename, file_md5, file_size):
        req = bailian_models.ApplyFileUploadLeaseRequest(
            file_name=filename,
            md_5=file_md5,
            size_in_bytes=str(file_size),
            category_id="default",
            category_type="UNSTRUCTURED"
        )
        return self.client.apply_file_upload_lease_with_options(req, {}, util_models.RuntimeOptions())

    def upload_file(self, pre_signed_url, headers, file_data):
        import requests
        return requests.put(pre_signed_url, data=file_data, headers=headers)

    def add_file(self, lease_id):
        req = bailian_models.AddFileRequest(
            lease_id=lease_id,
            parser="DASHSCOPE_DOCMIND",
            category_id="default"
        )
        return self.client.add_file_with_options(self.workspace_id, req, {}, util_models.RuntimeOptions())

    def create_index(self, name, document_ids):
        req = bailian_models.CreateIndexRequest(
            name=name,
            structure_type="unstructured",
            source_type="DATA_CENTER_FILE",
            document_ids=document_ids,
            sink_type="BUILT_IN"
        )
        return self.client.create_index_with_options(self.workspace_id, req, {}, util_models.RuntimeOptions())

    def submit_index_job(self, index_id):
        req = bailian_models.SubmitIndexJobRequest(
            index_id=index_id
        )
        return self.client.submit_index_job_with_options(self.workspace_id, req, {}, util_models.RuntimeOptions())